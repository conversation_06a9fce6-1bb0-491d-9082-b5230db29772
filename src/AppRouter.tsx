import { Navigate, Route, Routes } from 'react-router-dom';
import {
  CUSTOMER_BUSINESS,
  CUSTOMER_PERSONAL,
  DASH_BOARD,
  EMPLOYEE_EXTERNAL_MANAGEMENT,
  EMPLOYEE_INTERNAL_MANAGEMENT,
  INVESTORS_MANAGEMENT,
  LEAD,
  <PERSON>EA<PERSON>_<PERSON>NFIG,
  LEAD_SOURCE,
  PROJEC<PERSON>_MANAGEMENT,
  SALES_PROGRAM,
  UNIT_MANAGEMENT,
  UNIT_PARTNER_MANAGEMENT,
  USER_ADMIN_MANAGEMENT,
  USER_ROLES_MANAGEMENT,
  HISTORY_CUSTOMER,
  OFFICIAL_CUSTOMER_PERSONAL,
  OFFICIAL_CUSTOMER_BUSINESS,
  LEAD_ASSIGNED,
  ASSIGN_LEAD,
  PR<PERSON>OS<PERSON>,
  LEAD_DASHBOARD,
  FORM_REPORT,
  LEAD_HISTORY_IMPORT,
  IMPORT_HISTORY_PROJECT_MANAGEMENT,
  PAYMENT_POLICY,
  SALES_POLICY,
  CO<PERSON><PERSON><PERSON><PERSON>_POLICY,
  <PERSON><PERSON><PERSON>UNT_POLICY,
  LEAD_REPORT,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>PERATE_SELL_PROGRAM,
  INDICATOR,
  COMMISSION_HISTORY_IMPORT,
  COMMISSION_PERIOD_LIST,
  SELL_PROGRAM_MANAGEMENT,
  COMMISSION_PERIOD_DEFAULT,
  DELIVERY_CONFIGURATION,
  E_VOUCHER,
  PRODUCT_DETAIL,
  OFFER_COLLECT_MONEY_MANAGEMENT,
  OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT,
  OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT,
  TRAINING_USER_MANAGEMENT,
  TRAINING_USER_CREATE_MANAGEMENT,
  TRAINING,
  DEPOSIT_CONTRACT_MANAGEMENT,
  E_VOUCHER_LIST,
  E_VOUCHER_HISTORY,
  PURCHASE_CONTRACT_MANAGEMENT,
  DELIVERY,
  LIVE_STREAM_TRAINING,
  COMMISSION_DEBT_POLICY,
  OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT,
  TRANSFER_CONTRACT_MANAGEMENT,
  PROPERTY_ATTRIBUTES_MANAGEMENT,
  PROPOSAL_DOCUMENT,
  DEBT_COMMISSION_PERIOD,
  OWNERSHIP_CERTIFICATE_CONFIGURATION,
  LIQUIDATION_PROPOSAL_MANAGEMENT,
  LIQUIDATION_CONTRACT_MANAGEMENT,
  PRICE_COEFFICIENT,
  COMMISSION_DEBT_PENALTY,
  DEBT_REPORT,
  PERSONAL_COMMISSION,
  MARKETING,
  ESCROW_CONTRACT,
} from './configs/path';
import PrivateLayout from './layout/PrivateLayout';
import PublicLayout from './layout/PublicLayout';
import AuthPage from './page/auth';
import DetailBusinessCustomer from './page/businessCustomersManagement/detailBusinessCustomer';
import ListBusinessCustomers from './page/businessCustomersManagement/listBusinessCustomer';
import Dashboard from './page/dashboard/Dashboard';
import ListEmployeeExternal from './page/employeeExternalManagement';
import EmployeeExternalDetail from './page/employeeExternalManagement/employeeExternalDetail';
import ListEmployeeInternal from './page/employeeInternalManagement';
import EmployeeDetail from './page/employeeInternalManagement/employeeInternalDetail';
import ErrorsPage from './page/errors';
import HistoryImportDemandCustomer from './page/historyImportDemandCustomer';
import DetailOfInvestor from './page/investorsManagement/detailOfInvestors';
import ListOfInvestor from './page/investorsManagement/listOfInvestors';
import DetailOfLead from './page/leadManagement/detailOfLead';
import ListOfLead from './page/leadManagement/listOfLead';
import ListOfLeadCommon from './page/leads';
import ListLeadSources from './page/leadSourcesManagement/listLeadSources';
import DetailPersonalCustomer from './page/personalCustomersManagement/detailPersonalCustomer';
import ListPersonalCustomers from './page/personalCustomersManagement/listPersonalCustomers';
import ProjectList from './page/projectManagement';
import ProjectDetail from './page/projectManagement/projectDetail';
import DetailSellProgram from './page/projectManagement/projectDetail/component/sellProgramTab/detailSellProgram';
import DetailsUnits from './page/unitManagement/detailsUnit';
import ListOfUnits from './page/unitManagement/listOfUnits';
import DetailsUnitsPartner from './page/unitPartnerManagement/detailsUnitsPartner';
import ListOfUnitsPartner from './page/unitPartnerManagement/listOfUnitsPartner';
import ListUserAdmin from './page/userAdminManagement';
import DetailAndCreateUserAdmin from './page/userAdminManagement/components/FormCreateAndDetailUserAdmin';
import UserRolesManagement from './page/userRolesManagement';
import ListOfficialPersonalCustomers from './page/officialPersonalCustomersManagement/listOfficialPersonalCustomers';
import DetailOfficialPersonalCustomer from './page/officialPersonalCustomersManagement/detailOfficialPersonalCustomer';
import ListOfficialBusinessCustomers from './page/officialBusinessCustomersManagement/listOfficialBusinessCustomer';
import DetailOfficialBusinessCustomer from './page/officialBusinessCustomersManagement/detailOfficialBusinessCustomer';
import LeadAssignedList from './page/leadsAssignedManagement/listOfLeadAssigned';
import ListAssignLeads from './page/leadToDeliverManagement/listAssignLead';
import DetailOfLeadCare from './page/leadsAssignedManagement/detailOfLead';
import ListProposals from './page/proposalManagement/listProposal';
import DetailProposal from './page/proposalManagement/detailProposal';
import LeadDashboard from './page/leadDashboard';
import FormReportDetail from './page/projectManagement/projectDetail/component/formReportTab/formReportDetail';
import HistoryImportLeads from './page/historyImportLeads';
import ImportProjectHistoryManagement from './page/importProjectHistoryManagement';
import PaymentPolicy from './page/paymentPolicyManagement';
import ListOfSalesPolicy from './page/salesPolicyManagement/listOfSalesPolicy';
import ListCommissionPolicies from './page/commissionPolicyManagement/listCommisionPolicy';
import DetailCommissionPolicy from './page/commissionPolicyManagement/detailCommissionPolicy';
import DiscountPolicyList from './page/discountPolicyManagement/discountPolicyList';
import DiscountPolicyDetail from './page/discountPolicyManagement/discountPolicyDetail';
import DetailOfSalesPolicy from './page/salesPolicyManagement/detailOfSalesPolicy';
import PaymentPolicyDetail from './page/paymentPolicyManagement/deatailPaymentPolicy';
import ReportLead from './page/reportLead';
import ListOfCommission from './page/commissionManagement/listOfCommission';
import DetailOfCommission from './page/commissionManagement/detailOfCommission';
import ListIndicators from './page/indicatorManagement/listIndicators';
import DetailIndicator from './page/indicatorManagement/detailIndicator';
import HistoryImportCommissions from './page/historyImportCommission';
import ListOfCommissionPeriod from './page/commissionPeriodManagement/listOfCommissionPeriod';
import DetailCommissionPeriod from './page/commissionPeriodManagement/detailCommissionPeriod';
import SellProgramManagement from './page/sellProgramManagement';
import ListOfTimeConfigFeeCommission from './page/timeConfigFeeCommission/listOfTimeConfigFeeCommission';
import OperateSellProgram from './page/operateSellProgram';
import ListOfDeliveryConfig from './page/deliveryConfig/listOfDeliveryConfig';
import ListEVouchers from './page/eVoucherManagement/listOfEVoucher';
import ViewProductDetails from './page/operateSellProgram/propertyOperateSellProgram/productDetails';
import DetailOfTimeConfigFeeCommission from './page/timeConfigFeeCommission/detailOfTimeConfigFeeCommission';
import OfferMoneyManagement from './page/offerMoneyManagement';
import OfferRefundMoneyAccountancy from './page/offerRefundMoneyAccountancyManagement/List';
import OfferOrderMoneyAccountancy from './page/offerOrderMoneyAccountancyManagement/List';
import BookingTicketDetail from './page/operateSellProgram/bookingTicketPersonal/Detail';
import OfferRefundMoneyAccountancyDetail from './page/offerRefundMoneyAccountancyManagement/Detail';
import DetailEVoucher from './page/eVoucherManagement/detailEVoucher';
import DetailOfferMoney from './page/offerMoneyManagement/Detail';
import ListTraningUsers from './page/trainingUserManagement/listOfTrainingUser';
import TrainingUserCreate from './page/trainingUserCreateManagement';
import DetailDeliveryConfig from './page/deliveryConfig/detailDeliveryConfig';
import ListOfTraining from './page/trainingManagement/listOfTraining';
import DepositRequiredDetail from './page/operateSellProgram/depositRequiredTab/Detail';
import DetailDepositContract from './page/contractManagement/depositContracts/detailDepositContract';
import DetailOfTraining from './page/trainingManagement/detailOfTraining';
import ListEVoucher from './page/eVoucherListManagement/listEVoucher';
import ListParticipantsReport from './page/trainingManagement/participantsReport';
import ListEVoucherHistory from './page/eVoucherHistoryManagement/listEVoucherHistory';
import ListPrizeReport from './page/trainingManagement/prizesReport';
import DeliveryList from './page/deliveryManagement';
import DeliveryDetail from './page/deliveryManagement/deliveryDetail';
import PurchaseContract from './page/contractManagement/purchaseContract/listPurchaseContract';
import DetailPurchaseContract from './page/contractManagement/purchaseContract/detailPurchaseContract';
import ReceiptManualDetail from './page/offerOrderMoneyAccountancyManagement/Detail/ReceiptManualDetail';
import ReceiptAutoDetail from './page/offerOrderMoneyAccountancyManagement/Detail/ReceiptAutoDetail';
import LiveStreamTraining from './page/liveStreamTraining';
import OfferRefundMoneyPaymentDetail from './page/offerRefundMoneyPaymentManagement/Detail';
import OfferRefundMoneyPaymentList from './page/offerRefundMoneyPaymentManagement/List';
import PrizeSpin from './page/trainingManagement/prizeSpin';
import ListOfCommissionDebtPolicy from './page/commissionDebtPolicy/listOfCommissionDebtPolicy';
import TransferContract from './page/contractManagement/transferContract/listTransferContract';
import DetailTransferContract from './page/contractManagement/transferContract/detailTransferContract';
import PropertyAttributeList from './page/propertyAttribute/list';
import PropertyAttributeDetail from './page/propertyAttribute/detail';
import CreateProposal from './page/proposal';
import ListOfTimeConfigDebtCommission from './page/timeConfigCommissionDebt/listOfTimeConfigDebtCommission';
import DetailOfTimeConfigDebtCommission from './page/timeConfigCommissionDebt/detailOfTimeConfigFeeCommission';
import DetailOfCommissionDebtPolicy from './page/commissionDebtPolicy/detailOfCommissionDebtPolicy';
import DetailHandoverUnit from './page/deliveryManagement/deliveryDetail/handoverUnitTab/detailHandoverUnit';

import DepositContract from './page/contractManagement/depositContracts/listDepositContract';
import ListOfOwnershipCertificateConfig from './page/ownershipCertificateConfig/listOfOwnershipCertificateConfig';
import DetailOwnershipCertificateConfig from './page/ownershipCertificateConfig/detailOwnershipCertificateConfig';
import LiquidationContractManagement from './page/liquidation/liquidationContract';
import LiquidationProposalManagement from './page/liquidation/liquidationProposal';
import LiquidationContractDetail from './page/liquidation/liquidationContract/detailLiquidation';
import PriceCoefficientList from './page/priceCoefficient/list';
import PriceCoefficientsDetail from './page/priceCoefficient/detail';
import LiquidationProposalDetail from './page/liquidation/liquidationProposal/detailLiquidation';
import ListOfCommissionDebt from './page/commissionDebtManagement/listOfCommissionDebt';
import ListDebtReports from './page/debtReportManagement/listDebtReport';
import DetailOfDebtCommission from './page/commissionDebtManagement/detailOfDebtCommission';
import ListOfPersonalCommission from './page/personalCommission';
import ListMarketing from './page/marketingManagement/listMarketing';
import DetailMarketing from './page/marketingManagement/detailMarketing';
import EscrowContractList from './page/escrowContractManagement/escrowContractList';
import EscrowContractDetail from './page/escrowContractManagement/escrowContractDetail';

const AppRouter: React.FC = () => {
  return (
    <Routes>
      <Route
        path={DASH_BOARD}
        element={
          <PrivateLayout>
            <Dashboard />
          </PrivateLayout>
        }
      />
      <Route
        path={EMPLOYEE_INTERNAL_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListEmployeeInternal />
          </PrivateLayout>
        }
      />
      <Route
        path={`${EMPLOYEE_INTERNAL_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <EmployeeDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={EMPLOYEE_EXTERNAL_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListEmployeeExternal />
          </PrivateLayout>
        }
      />
      <Route
        path={`${EMPLOYEE_EXTERNAL_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <EmployeeExternalDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={USER_ROLES_MANAGEMENT}
        element={
          <PrivateLayout>
            <UserRolesManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={UNIT_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListOfUnits />
          </PrivateLayout>
        }
      />
      <Route
        path={`${UNIT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailsUnits />
          </PrivateLayout>
        }
      />
      <Route
        path={UNIT_PARTNER_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListOfUnitsPartner />
          </PrivateLayout>
        }
      />
      <Route
        path={`${UNIT_PARTNER_MANAGEMENT}/create`}
        element={
          <PrivateLayout>
            <DetailsUnitsPartner />
          </PrivateLayout>
        }
      />
      <Route
        path={`${UNIT_PARTNER_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailsUnitsPartner />
          </PrivateLayout>
        }
      />
      <Route
        path={USER_ADMIN_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListUserAdmin />
          </PrivateLayout>
        }
      />
      <Route
        path={`${USER_ADMIN_MANAGEMENT}/create`}
        element={
          <PrivateLayout>
            <DetailAndCreateUserAdmin />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PAYMENT_POLICY}`}
        element={
          <PrivateLayout>
            <PaymentPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PAYMENT_POLICY}/:id`}
        element={
          <PrivateLayout>
            <PaymentPolicyDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={INVESTORS_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListOfInvestor />
          </PrivateLayout>
        }
      />
      <Route
        path={INVESTORS_MANAGEMENT}
        element={
          <PrivateLayout>
            <DetailOfInvestor />
          </PrivateLayout>
        }
      />
      <Route
        path={`${INVESTORS_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailOfInvestor />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}${SALES_PROGRAM}/:id`}
        element={
          <PrivateLayout>
            <DetailSellProgram />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/:id`}
        element={
          <PrivateLayout>
            <OperateSellProgram />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/:id/:bookingTicketId`}
        element={
          <PrivateLayout>
            <BookingTicketDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={PROJECTS_MANAGEMENT}
        element={
          <PrivateLayout>
            <ProjectList />
          </PrivateLayout>
        }
      />
      <Route
        path={PROPERTY_ATTRIBUTES_MANAGEMENT}
        element={
          <PrivateLayout>
            <PropertyAttributeList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROPERTY_ATTRIBUTES_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <PropertyAttributeDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <ProjectDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}/:projectId${FORM_REPORT}/:formId`}
        element={
          <PrivateLayout>
            <FormReportDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${IMPORT_HISTORY_PROJECT_MANAGEMENT}`}
        element={
          <PrivateLayout>
            <ImportProjectHistoryManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROPOSAL}`}
        element={
          <PrivateLayout>
            <ListProposals />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROPOSAL}/:id`}
        element={
          <PrivateLayout>
            <DetailProposal />
          </PrivateLayout>
        }
      />
      <Route
        path={`${CUSTOMER_PERSONAL}`}
        element={
          <PrivateLayout>
            <ListPersonalCustomers />
          </PrivateLayout>
        }
      />
      <Route
        path={`${CUSTOMER_PERSONAL}/:id`}
        element={
          <PrivateLayout>
            <DetailPersonalCustomer />
          </PrivateLayout>
        }
      />
      <Route
        path={`${CUSTOMER_BUSINESS}`}
        element={
          <PrivateLayout>
            <ListBusinessCustomers />
          </PrivateLayout>
        }
      />
      <Route
        path={`${CUSTOMER_BUSINESS}/:id`}
        element={
          <PrivateLayout>
            <DetailBusinessCustomer />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFICIAL_CUSTOMER_PERSONAL}`}
        element={
          <PrivateLayout>
            <ListOfficialPersonalCustomers />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFICIAL_CUSTOMER_PERSONAL}/:id`}
        element={
          <PrivateLayout>
            <DetailOfficialPersonalCustomer />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFICIAL_CUSTOMER_BUSINESS}`}
        element={
          <PrivateLayout>
            <ListOfficialBusinessCustomers />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFICIAL_CUSTOMER_BUSINESS}/:id`}
        element={
          <PrivateLayout>
            <DetailOfficialBusinessCustomer />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_CONFIG}`}
        element={
          <PrivateLayout>
            <ListOfLead />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_CONFIG}/:id`}
        element={
          <PrivateLayout>
            <DetailOfLead />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_SOURCE}`}
        element={
          <PrivateLayout>
            <ListLeadSources />
          </PrivateLayout>
        }
      />
      <Route
        path={`${HISTORY_CUSTOMER}`}
        element={
          <PrivateLayout>
            <HistoryImportDemandCustomer />
          </PrivateLayout>
        }
      />{' '}
      <Route
        path={`${LEAD}`}
        element={
          <PrivateLayout>
            <ListOfLeadCommon />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_ASSIGNED}`}
        element={
          <PrivateLayout>
            <LeadAssignedList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_ASSIGNED}/:id`}
        element={
          <PrivateLayout>
            <DetailOfLeadCare />
          </PrivateLayout>
        }
      />
      <Route
        path={`${ASSIGN_LEAD}`}
        element={
          <PrivateLayout>
            <ListAssignLeads />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_DASHBOARD}`}
        element={
          <PrivateLayout>
            <LeadDashboard />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_DASHBOARD}/:id`}
        element={
          <PrivateLayout>
            <DetailOfLeadCare />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_HISTORY_IMPORT}`}
        element={
          <PrivateLayout>
            <HistoryImportLeads />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LEAD_REPORT}`}
        element={
          <PrivateLayout>
            <ReportLead />
          </PrivateLayout>
        }
      />
      <Route
        path={`${ESCROW_CONTRACT}`}
        element={
          <PrivateLayout>
            <EscrowContractList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${ESCROW_CONTRACT}/:id`}
        element={
          <PrivateLayout>
            <EscrowContractDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DISCOUNT_POLICY}`}
        element={
          <PrivateLayout>
            <DiscountPolicyList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DISCOUNT_POLICY}/:id`}
        element={
          <PrivateLayout>
            <DiscountPolicyDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${SALES_POLICY}`}
        element={
          <PrivateLayout>
            <ListOfSalesPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${SALES_POLICY}/:id`}
        element={
          <PrivateLayout>
            <DetailOfSalesPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path="/"
        element={
          <PublicLayout>
            <AuthPage />
          </PublicLayout>
        }
      />
      <Route
        path={`${COMMISSION_POLICY}`}
        element={
          <PrivateLayout>
            <ListCommissionPolicies />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_POLICY}/:id`}
        element={
          <PrivateLayout>
            <DetailCommissionPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION}`}
        element={
          <PrivateLayout>
            <ListOfCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION}/:id`}
        element={
          <PrivateLayout>
            <DetailOfCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_HISTORY_IMPORT}`}
        element={
          <PrivateLayout>
            <HistoryImportCommissions />
          </PrivateLayout>
        }
      />
      <Route
        path={`${INDICATOR}`}
        element={
          <PrivateLayout>
            <ListIndicators />
          </PrivateLayout>
        }
      />
      <Route
        path={`${INDICATOR}/:id`}
        element={
          <PrivateLayout>
            <DetailIndicator />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_PERIOD_LIST}`}
        element={
          <PrivateLayout>
            <ListOfCommissionPeriod />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_PERIOD_LIST}/:id`}
        element={
          <PrivateLayout>
            <DetailCommissionPeriod />
          </PrivateLayout>
        }
      />
      <Route
        path={COMMISSION_PERIOD_DEFAULT}
        element={
          <PrivateLayout>
            <ListOfTimeConfigFeeCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_PERIOD_DEFAULT}/:id`}
        element={
          <PrivateLayout>
            <DetailOfTimeConfigFeeCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={SELL_PROGRAM_MANAGEMENT}
        element={
          <PrivateLayout>
            <SellProgramManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={DELIVERY_CONFIGURATION}
        element={
          <PrivateLayout>
            <ListOfDeliveryConfig />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DELIVERY_CONFIGURATION}/:id`}
        element={
          <PrivateLayout>
            <DetailDeliveryConfig />
          </PrivateLayout>
        }
      />
      <Route
        path={`${E_VOUCHER}`}
        element={
          <PrivateLayout>
            <ListEVouchers />
          </PrivateLayout>
        }
      />
      <Route
        path={`${E_VOUCHER}/:id`}
        element={
          <PrivateLayout>
            <DetailEVoucher />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}${PRODUCT_DETAIL}/:id`}
        element={
          <PrivateLayout>
            <ViewProductDetails />
          </PrivateLayout>
        }
      />
      <Route
        path={OFFER_COLLECT_MONEY_MANAGEMENT}
        element={
          <PrivateLayout>
            <OfferMoneyManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFER_COLLECT_MONEY_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailOfferMoney />
          </PrivateLayout>
        }
      />
      <Route
        path={OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT}
        element={
          <PrivateLayout>
            <OfferRefundMoneyAccountancy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <OfferRefundMoneyAccountancyDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT}
        element={
          <PrivateLayout>
            <OfferRefundMoneyPaymentList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <OfferRefundMoneyPaymentDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}
        element={
          <PrivateLayout>
            <OfferOrderMoneyAccountancy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}/manual/:id`}
        element={
          <PrivateLayout>
            <ReceiptManualDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}/auto/:id`}
        element={
          <PrivateLayout>
            <ReceiptAutoDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={TRAINING_USER_MANAGEMENT}
        element={
          <PrivateLayout>
            <ListTraningUsers />
          </PrivateLayout>
        }
      />
      <Route
        path={TRAINING}
        element={
          <PrivateLayout>
            <ListOfTraining />
          </PrivateLayout>
        }
      />
      <Route
        path={`${TRAINING}/:id`}
        element={
          <PrivateLayout>
            <DetailOfTraining />
          </PrivateLayout>
        }
      />
      <Route
        path={TRAINING_USER_CREATE_MANAGEMENT}
        element={
          <PrivateLayout>
            <TrainingUserCreate />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/deposit/:depositId`}
        element={
          <PrivateLayout>
            <DepositRequiredDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={DEPOSIT_CONTRACT_MANAGEMENT}
        element={
          <PrivateLayout>
            <DepositContract />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DEPOSIT_CONTRACT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailDepositContract />
          </PrivateLayout>
        }
      />
      <Route
        path={PURCHASE_CONTRACT_MANAGEMENT}
        element={
          <PrivateLayout>
            <PurchaseContract />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PURCHASE_CONTRACT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailPurchaseContract />
          </PrivateLayout>
        }
      />
      <Route
        path={`${E_VOUCHER_LIST}`}
        element={
          <PrivateLayout>
            <ListEVoucher />
          </PrivateLayout>
        }
      />
      <Route
        path={`${TRAINING}/:id/report-participants`}
        element={
          <PrivateLayout>
            <ListParticipantsReport />
          </PrivateLayout>
        }
      />
      <Route
        path={`${E_VOUCHER_HISTORY}`}
        element={
          <PrivateLayout>
            <ListEVoucherHistory />
          </PrivateLayout>
        }
      />
      <Route
        path={`${TRAINING}/:id/report-prizes`}
        element={
          <PrivateLayout>
            <ListPrizeReport />
          </PrivateLayout>
        }
      />
      <Route
        path={LIQUIDATION_PROPOSAL_MANAGEMENT}
        element={
          <PrivateLayout>
            <LiquidationProposalManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LIQUIDATION_PROPOSAL_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <LiquidationProposalDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={LIQUIDATION_CONTRACT_MANAGEMENT}
        element={
          <PrivateLayout>
            <LiquidationContractManagement />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LIQUIDATION_CONTRACT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <LiquidationContractDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DELIVERY}`}
        element={
          <PrivateLayout>
            <DeliveryList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DELIVERY}/:id`}
        element={
          <PrivateLayout>
            <DeliveryDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={`${TRANSFER_CONTRACT_MANAGEMENT}`}
        element={
          <PrivateLayout>
            <TransferContract />
          </PrivateLayout>
        }
      />
      <Route
        path={`${TRANSFER_CONTRACT_MANAGEMENT}/:id`}
        element={
          <PrivateLayout>
            <DetailTransferContract />
          </PrivateLayout>
        }
      />
      <Route
        path={PROPOSAL_DOCUMENT}
        element={
          <PrivateLayout>
            <CreateProposal />
          </PrivateLayout>
        }
      />
      <Route
        path={`${LIVE_STREAM_TRAINING}/:id`}
        element={
          <PublicLayout>
            <LiveStreamTraining />
          </PublicLayout>
        }
      />
      <Route
        path={`${TRAINING}/:id/prize-spin`}
        element={
          <PublicLayout>
            <PrizeSpin />
          </PublicLayout>
        }
      />
      <Route
        path={COMMISSION_DEBT_POLICY}
        element={
          <PrivateLayout>
            <ListOfCommissionDebtPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_DEBT_POLICY}/:id`}
        element={
          <PrivateLayout>
            <DetailOfCommissionDebtPolicy />
          </PrivateLayout>
        }
      />
      <Route
        path={DEBT_COMMISSION_PERIOD}
        element={
          <PrivateLayout>
            <ListOfTimeConfigDebtCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DEBT_COMMISSION_PERIOD}/:id`}
        element={
          <PrivateLayout>
            <DetailOfTimeConfigDebtCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DELIVERY}/property/:id`}
        element={
          <PrivateLayout>
            <DetailHandoverUnit />
          </PrivateLayout>
        }
      />
      <Route
        path={OWNERSHIP_CERTIFICATE_CONFIGURATION}
        element={
          <PrivateLayout>
            <ListOfOwnershipCertificateConfig />
          </PrivateLayout>
        }
      />
      <Route
        path={PRICE_COEFFICIENT}
        element={
          <PrivateLayout>
            <PriceCoefficientList />
          </PrivateLayout>
        }
      />
      <Route
        path={`${OWNERSHIP_CERTIFICATE_CONFIGURATION}/:id`}
        element={
          <PrivateLayout>
            <DetailOwnershipCertificateConfig />
          </PrivateLayout>
        }
      />
      <Route
        path={`${PRICE_COEFFICIENT}/:id`}
        element={
          <PrivateLayout>
            <PriceCoefficientsDetail />
          </PrivateLayout>
        }
      />
      <Route
        path={COMMISSION_DEBT_PENALTY}
        element={
          <PrivateLayout>
            <ListOfCommissionDebt />
          </PrivateLayout>
        }
      />
      <Route
        path={`${DEBT_REPORT}`}
        element={
          <PrivateLayout>
            <ListDebtReports />
          </PrivateLayout>
        }
      />
      <Route
        path={`${COMMISSION_DEBT_PENALTY}/:id`}
        element={
          <PrivateLayout>
            <DetailOfDebtCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={PERSONAL_COMMISSION}
        element={
          <PrivateLayout>
            <ListOfPersonalCommission />
          </PrivateLayout>
        }
      />
      <Route
        path={`${MARKETING}`}
        element={
          <PrivateLayout>
            <ListMarketing />
          </PrivateLayout>
        }
      />
      <Route
        path={`${MARKETING}/:id`}
        element={
          <PrivateLayout>
            <DetailMarketing />
          </PrivateLayout>
        }
      />
      <Route
        path="/403"
        element={
          <PublicLayout>
            <ErrorsPage status="403" />
          </PublicLayout>
        }
      />
      <Route
        path="/404"
        element={
          <PublicLayout>
            <ErrorsPage status="404" />
          </PublicLayout>
        }
      />
      <Route path="*" element={<Navigate to="/404" />} />
    </Routes>
  );
};

export default AppRouter;
