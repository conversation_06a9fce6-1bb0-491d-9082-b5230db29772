import { CompatClient, Message, Stomp } from '@stomp/stompjs';
import { useEffect, useRef } from 'react';
import SockJS from 'sockjs-client';

interface IStompClient {
  username?: string;
  debug?: boolean;
  onMessageReceived: (message: Message) => void;
}
const url = import.meta.env.VITE_APP_URI_NOTIFICATION;

const useStompClient = ({ username, onMessageReceived, debug = false }: IStompClient) => {
  const clientRef = useRef<CompatClient | null>(null);
  const callbackRef = useRef(onMessageReceived);

  // Cập nhật callback mới nhất
  useEffect(() => {
    callbackRef.current = onMessageReceived;
  }, [onMessageReceived]);

  useEffect(() => {
    const connect = () => {
      const socket = () => new SockJS(`${url}/noti-socket/noti`);
      const client = Stomp.over(socket);
      client.reconnectDelay = 1000;

      client.connect(
        {},
        () => {
          client.subscribe(`/topic/crm/${username}`, message => {
            callbackRef.current(message);
          });
        },
        (error: unknown) => {
          if (debug) console.error('[STOMP] Error, will attempt reconnect', error);
        },
      );
      clientRef.current = client;
    };

    connect();

    return () => {
      clientRef.current?.disconnect(() => {
        if (debug) console.log('[STOMP] Disconnected');
      });
    };
  }, [username, debug]);
};

export default useStompClient;
