export const CALCULATE_TYPES = [
  {
    label: '<PERSON><PERSON> nợ gốc',
    value: 'base',
  },
  {
    label: '<PERSON><PERSON> nợ phát sinh',
    value: 'interest',
  },
  {
    label: 'Tổng dư nợ',
    value: 'all',
  },
];

export const ARRAY_PHASE = ['Trước đợt thanh toán', '<PERSON><PERSON><PERSON> đợt thanh toán', '<PERSON>u đợt thanh toán'];

export const DEBT_TYPES = [
  { name: 'Nợ xấu', value: 'BAD_DEBT' },
  { name: 'Nợ dự án', value: 'PROJECT_DEBT' },
];

export const STATUS_DEBT_ADJUSTMENT_VERSION = {
  CREATED: 'CREATED',
  CONFIRMED: 'CONFIRMED',
};

export const LABEL_STATUS_DEBT_ADJUSTMENT_VERSION = {
  [STATUS_DEBT_ADJUSTMENT_VERSION.CREATED]: '<PERSON>ớ<PERSON> tạo',
  [STATUS_DEBT_ADJUSTMENT_VERSION.CONFIRMED]: 'Đ<PERSON> xác nhận',
};

export const COLOR_STATUS_DEBT_ADJUSTMENT_VERSION = {
  [STATUS_DEBT_ADJUSTMENT_VERSION.CREATED]: 'orange',
  [STATUS_DEBT_ADJUSTMENT_VERSION.CONFIRMED]: 'green',
};

export const CALCULATE_TYPES_DEBT = [
  {
    label: 'Tổng dư nợ gốc',
    value: 'base',
  },
  {
    label: 'Tổng dư nợ phát sinh',
    value: 'interest',
  },
  {
    label: 'Tổng dư nợ',
    value: 'all',
  },
];
