import { postRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IActionsSpin, IConfirmSpin } from '../../types/training/training';

export const postSelectedPrize = async (payload?: IActionsSpin) => {
  return await postRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/selectPrize`, payload);
};

export const postStartSpin = async (payload?: IActionsSpin) => {
  return await postRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/startSpin`, payload);
};

export const postStopSpin = async (payload?: IActionsSpin) => {
  return await postRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/endSpin`, payload);
};

export const postConfirmSpin = async (payload?: IConfirmSpin) => {
  return await postRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/confirmPrize`, payload);
};
