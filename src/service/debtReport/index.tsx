import { axiosInstance, getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TManualDeliver } from '../../types/debtReport';

export const getListDebReports = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract`,
    params as Record<string, unknown> | undefined,
  );
};

export const deleteAssigner = async (payload: { contractIds?: string[] }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/deassign`,
    payload,
  );
};

export const exportDebtReport = async () => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/export`,
    {
      //   params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};

export const manualDeliver = async (payload: TManualDeliver) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/assign`,
    payload,
  );
};

export const updateCallRecord = async (payload: { id?: string; title?: string; detail?: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/callRecord`,
    payload,
  );
};

export const updateAddNote = async (payload: { id?: string; title?: string; detail?: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/addNote`,
    payload,
  );
};

export const getListOfStaff = async () => {
  return await getRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/employee/dropdown`);
};

export const getListOfProject = async () => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/projects/dropdown`,
  );
};

export const reSendEmail = async (payload: { id?: string; customerCodes?: string[] }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/debtReportContract/resendEmail`,
    payload,
  );
};
