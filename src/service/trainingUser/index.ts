import { axiosInstance, getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getListOfTrainingUser = async (params: unknown) => {
  const { id: idEvent } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/event/${idEvent}`,
    params as Record<string, unknown> | undefined,
  );
};

export const getEvents = async () => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/canRegister`);
};

export const getUsers = async () => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user`);
};

export const deleteTrainingUsers = async (payload: { [key: string]: unknown }) => {
  return await putRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/user/guest/delete-many`,
    payload as Record<string, unknown> | undefined,
  );
};

export const exportTrainingUser = async (idEvent?: string) => {
  return await axiosInstance.get(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/${idEvent}/export`, {
    //   params: params as Record<string, unknown> | undefined,
    responseType: 'arraybuffer',
  });
};

export const getListOfReportParticipants = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/attend/event/${id}`,
    restParams,
  );
};

export const exportListOfReportParticipants = async (idEvent?: string, params?: unknown) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/${idEvent}/exportUserAttend`,
    {
      responseType: 'arraybuffer',
      params,
    },
  );
};

export const getListOfReportPrizes = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/prizes/${id}`,
    restParams,
  );
};

export const exportListOfReportPrizes = async (idEvent?: string) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/${idEvent}/exportEventPrizes`,
    {
      responseType: 'arraybuffer',
    },
  );
};

export const getListOfChatAndReact = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/chatAndReact/${id}`);
};

export const exportListOfChatAndReact = async (idEvent?: string) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/${idEvent}/exportChatAndReact`,
    {
      responseType: 'arraybuffer',
    },
  );
};
