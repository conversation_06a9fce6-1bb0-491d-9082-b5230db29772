import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';

export const getListOffer = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailOffer = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(`${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/${id}`, {
    ...query,
  });
  return response;
};

export const getTransactionReceiptManual = async (params?: ParamsGetRequest) => {
  const { transactionId, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/${transactionId}`,
    {
      ...query,
    },
  );
  return response;
};

export const getPrimaryContractReceiptManual = async (params?: ParamsGetRequest) => {
  const { contractId, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${contractId}`,
    {
      ...query,
    },
  );
  return response;
};

export const getListAccount = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/employee/dropdown`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartInternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/allByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartExternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/getAll`,
    params as Record<string, unknown> | undefined,
  );
};

export const approveOffer = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/accountingList`,
    payload as Record<string, unknown> | undefined,
  );
};

export const rejectOffer = async (payload: { [key: string]: unknown }) => {
  return await putRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/reject`,
    payload as Record<string, unknown> | undefined,
  );
};

export const createOfferRefund = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getListContractRefundMoney = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/liquidation`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListContractOrderMoney = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListCustomer = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/searchAll`,
    params as Record<string, unknown> | undefined,
  );
};
export const getListProject = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListBookingTicket = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticket/${id}`,
    {
      ...query,
    },
  );
  return response;
};

export const createOfferOrder = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    payload as Record<string, unknown> | undefined,
  );
};

export const cancelOffer = async (payload: { [key: string]: unknown }) => {
  return await putRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/${payload?.id}/cancel`,
    payload as Record<string, unknown> | undefined,
  );
};

export const deleteOffer = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/${payload?.id}/delete`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getListInterestReceipt = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${id}/list-interest-calculations`,
    {
      ...query,
    },
  );
  return response;
};

export const sendProposal = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/sendProposal`,
    payload as Record<string, unknown> | undefined,
  );
};
