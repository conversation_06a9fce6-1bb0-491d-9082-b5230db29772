import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { Account } from '../../page/employeeInternalManagement/employeeInternalDetail/component/accountTab/type';
import { ParamsGetRequest } from '../../types/common/common';

export const getInfoAccount = async () => {
  return await getRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/account`);
};

export const getDetailAccount = async (id: string) => {
  return await getRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/account/${id}`);
};

export const getDataRolesAccount = async (id: string) => {
  return await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v1}/data-permission/query/account/${id}`,
  );
};

export const putFuncRolesAccount = async (payload: Account) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/account/${payload.id}`,
    payload,
  );
  return response;
};

export const addDataRoles = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v1}/data-permission/bulk`,
    payload,
  );
  return response;
};
export const getAllDataRolesInternal = async (params: ParamsGetRequest) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/getAll`,
    params as Record<string, unknown>,
  );
};
export const getAllDataRolesExternal = async (params: ParamsGetRequest) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/getAll`,
    params as Record<string, unknown>,
  );
};
