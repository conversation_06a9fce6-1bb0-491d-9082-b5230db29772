import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';
import {
  DetailProject,
  IBlock,
  Project,
  ProjectSaleProgram,
  IGetPropertyUnit,
  IFormDocumentProject,
  IFilterDocumentProject,
  DeletePartnerCodePayload,
  UpdateOrgchartProjectPayload,
  UpdatePartnerCodePayload,
} from '../../types/project/project';

export const getListProject = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendCreateProject = async (payload: Project) => {
  const response = await postRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project`, payload);
  return response;
};

export const getDetailProject = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}`);
  return response;
};

export const updateProject = async (data?: DetailProject) => {
  const response = await putRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${data?.id}`, {
    ...data,
  });
  return response;
};

export const getBlockByProjectId = async (params: unknown) => {
  const { idProject, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/blocks/${idProject}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const createBlock = async (payload: IBlock) => {
  return await postRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/blocks`, {
    ...payload,
  });
};

export const deleteBlock = async (id: string | undefined) => {
  return await deleteRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/blocks/${id}`);
};
export const updateBlock = async (payload: IBlock) => {
  const { id, ...restPayload } = payload;
  return await putRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/blocks/${id}`, {
    ...restPayload,
  });
};

export const createOrUpdateBlock = async (payload: IBlock) => {
  const { id, ...restPayload } = payload;
  const response = await putRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}/block`, {
    ...restPayload,
  });
  return response;
};

export const getListProjectSaleProgram = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListDocumentProject = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}/project-document-folder`,
    { ...query },
  );
  return response;
};
export const sendCreateProjectSaleProgram = async (payload: ProjectSaleProgram) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram`,
    payload,
  );
  return response;
};
export const sendUpdateProjectSaleProgram = async (data?: ProjectSaleProgram) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram/${data?.id}`,
    {
      ...data,
    },
  );
  return response;
};

export const getProjectSaleProgramById = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram/${id}`);
  return response;
};

export const getListOrgchartProject = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}/orgchart`,
    { ...query },
  );
  return response;
};

export const getListĐVBH = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}/getAllOrgchartInternal`,
    { ...query },
  );
  return response;
};

export const getListĐTHT = async (params?: ParamsGetRequest) => {
  const { ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/getAll`,
    { ...query },
  );
  return response;
};

export const sendUpdateOrgchartProject = async (payload?: UpdateOrgchartProjectPayload) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/orgchartIds`,
    payload,
  );
  return response;
};
export const sendDeleteOrgChartProject = async (payload: { projectId: string; orgchartId: string }) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/removeInternalOrgcharts/${payload?.projectId}/${payload?.orgchartId}`,
    payload,
  );
  return response;
};

export const sendDeletePartnerCode = async (payload?: DeletePartnerCodePayload) => {
  const response = await putRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/external/orgchart/deletePartnerCode`,
    payload,
  );
  return response;
};

export const sendUpdatePartnerCode = async (payload?: UpdatePartnerCodePayload) => {
  const response = await putRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/external/orgchart/updateMultiple`,
    payload,
  );
  return response;
};

export const getSalesProgramByProjectIds = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram/getAllByProjectId`,
    params as unknown as Record<string, unknown> | undefined,
  );
  return response;
};

export const getPropertyUnitByProjectIdAndSalesProgramIds = async (params: IGetPropertyUnit) => {
  params = {
    ...params,
    _fields:
      'id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,liquidateType,contract,contractType,shortCode,attributes.attributeName,block,floor,attributes.value,bedroom,direction,priceAbove,price,housePriceVat,landPriceVat,housePrice,landPrice,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,stage,lstImage,status',
    categoryId: import.meta.env.VITE_CATEGORY_ID_PROJECT,
  };
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary/assign`,
    params as unknown as Record<string, unknown> | undefined,
  );
  return response;
};

export const importPropertyUnit = async (params: { file: File; projectId: string; salesProgramId: string }) => {
  const formData = new FormData();
  formData.append('files', params.file);
  formData.append('projectId', params.projectId);
  formData.append('transactionType', 'SELL');
  formData.append('salesProgramId', params.salesProgramId);
  formData.append('categoryId', import.meta.env.VITE_CATEGORY_ID_PROJECT);
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary`,
    formData,
  );
  return response;
};
export const updatePropertyUnit = async (params: { file: File; projectId: string; salesProgramId: string }) => {
  const formData = new FormData();
  formData.append('files', params.file);
  formData.append('projectId', params.projectId);
  formData.append('transactionType', 'SELL');
  formData.append('salesProgramId', params.salesProgramId);
  formData.append('categoryId', import.meta.env.VITE_CATEGORY_ID_PROJECT);
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary`,
    formData,
  );
  return response;
};

export const exportPropertyUnit = async (params: { projectId: string; salesProgramIds: string; fields?: string }) => {
  const defaultFields =
    'id,project.name,project.id,pos.name,pos.id,code,apartmentType,areaWide,areaLong,primaryStatus,shortCode,attributes.attributeName,block,floor,outsideArea,attributes.value,bedroom,direction,priceAbove,price,area,priorities,extendable,extendPos,reasonRevoke,modifiedDate,priceVat,category.id,description,salesProgram.id,owner,extData,status';
  const queryParams = {
    projectId: params.projectId,
    salesProgramIds: params.salesProgramIds,
    _fields: params.fields || defaultFields,
  };

  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary/assign`,
    queryParams,
  );
  return response;
};

export const addProjectDocument = async (params: IFormDocumentProject) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/${params.projectID}`,
    params,
  );
  return response;
};

export const getProjectDocument = async (params: IFilterDocumentProject) => {
  const { projectID, ...restParams } = params;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/${projectID}`,
    restParams,
  );
  return response;
};

export const getDocumentProjectItems = async (params: { documentId?: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/item/${params.documentId}`,
  );
};

export const updateProjectDocument = async (params: IFormDocumentProject) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/${params.projectID}/${params.id}`,
    params,
  );
  return response;
};

export const deleteProjectDocument = async (params: IFormDocumentProject) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/${params.id}`,
  );
  return response;
};

export const deleteItemProjectDocument = async (params: { documentId?: string; itemId?: string }) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/deleteItem/${params.documentId}/${params.itemId}`,
  );
  return response;
};

export const addItemsProjectDocument = async (params: IFormDocumentProject) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project-document/addItem/${params.id}`,
    params,
  );
};
export const getBlockBySalesProgramIds = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram/getBlockBySalesProgramIds`,
    payload,
  );
  return response;
};

export const getOrgChartSaleProgramId = async (params: unknown) => {
  const { idSaleProgram, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getOrgChartBySaleProgramId/${idSaleProgram}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};
export const getBlockBySaleProgramId = async (params: unknown) => {
  const { idSaleProgram, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getBlockBySaleProgramId/${idSaleProgram}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};
export const getListStatusSaleProgram = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getPrimaryStatuses/`,
    params as Record<string, unknown> | undefined,
  );
};
export const getFloorAndRoomByBlockId = async (params: unknown) => {
  const { saleProgramId, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getFloorBySaleProgramId/${saleProgramId}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};
export const getTemplateImportSP = async (params: { projectId: string; saleProgramId: string }) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/exportTemplateImport`,
    params,
  );
  return response;
};
export const getTemplateUpdateSP = async (params: { projectId: string; saleProgramId: string }) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/exportTemplateImportUpdate`,
    params,
  );
  return response;
};
export const getDetailProductUnit = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/${id}`);
  return response;
};

export const sendUpdateRootPos = async (payload?: UpdateOrgchartProjectPayload) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/updateRootPos/${payload?.projectId}?rootPosId=${payload?.rootPosId}`,
    payload,
  );
  return response;
};
export const sendUpdateMarketPos = async (payload?: UpdateOrgchartProjectPayload) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/updateMarketPos/${payload?.projectId}?marketPosId=${payload?.marketPosId}`,
    payload,
  );
  return response;
};

export const getAllOrgchart = async (params?: ParamsGetRequest) => {
  const { ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/getAllOrgchart`,
    { ...query },
  );
  return response;
};

export const sendUpdatePriorityPropertyUnit = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.domain}/propertyUnit/priority`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};
