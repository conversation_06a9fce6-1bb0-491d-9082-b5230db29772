import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getAllOfSalesPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailOfSalesPolicy = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
};

export const createSalesPolicy = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/sales-policy/create-sales-policy`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const updateSalesPolicy = async (payload: unknown) => {
  const response = await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/sales-policy/update-sales-policy`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendGetListOfDropdownProjects = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/get-dropdown-projects`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendGetDetailOfDropdownProject = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/get-one-project/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendGetListOfDropdownOrgCharts = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/get-dropdown-orgCharts`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendGetDetailOfDropdownOrgChart = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/get-oneOrg-chart/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const softDeleteSalesPolicy = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/sales-policy/delete-sales-policy`,
    payload as Record<string, unknown> | undefined,
  );
};

export const changeStatusPolicy = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/sales-policy/change-status-sales-policy`,
    data as Record<string, unknown>,
  );
};

export const getClonePolicy = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/clone-sales-policy/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
};
