import { RcFile } from 'antd/lib/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

// export const getUserProfile = async () => {
//   return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/getProfile`);
// };
export const getDetailProposal = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/${id}`);
};

export const getListProposal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal`,
    params as Record<string, unknown> | undefined,
  );
};

export const createProposal = async (data: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal`,
    data as Record<string, unknown>,
  );
};

export const updateProposal = async (data: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal`,
    data as Record<string, unknown>,
  );
};

export const deleteProposal = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal`, {
    id: payload.id,
    reasonDelete: payload.softDeleteReason,
  });
};

export const importProposal = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/import`,
    payload,
  );
};

export const uploadFile = async (files: RcFile[], isConvertPDF: string) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`files`, file);
  });

  formData.append('isConvertPDF', isConvertPDF);

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_partner_gateway}/api/v1/proposal/upload-file`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getProposalType = async () => {
  return await getRequest(`${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/list-type`);
};
export const getStatus = async () => {
  return await getRequest(`${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/list-status`);
};

export const createNonSampleProposal = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/non-sample-proposal`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const getSalePolicy = async () => {
  return await getRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/list-sale-policy`,
    { isActive: 1 },
  );
};

export const getCommissionPolicy = async () => {
  return await getRequest(
    `${urlDomainApi.msx_partner_gateway}/${typeQueryVersionApi.api_v1}/proposal/list-commission-policy`,
  );
};
