import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { TCustomerAttendance } from '../../../types/trainingUser';

const { Text } = Typography;

export const columns: ColumnsType<TCustomerAttendance> = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    align: 'center',
    width: 200,
    render: value => (value ? value : '-'),
  },
  {
    title: 'SĐT',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    width: 200,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Thời gian tham gia',
    dataIndex: 'checkInDate',
    key: 'checkInDate',
    width: 180,
    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
      </>
    ),
  },
  {
    title: 'Thời gian rời đi',
    dataIndex: 'checkOutDate',
    key: 'checkOutDate',
    width: 180,
    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
      </>
    ),
  },
  {
    title: 'Thời gian tham dự (phút)',
    dataIndex: 'timeAttend',
    key: 'timeAttend',
    width: 200,
    render: value => (value ? value : 0),
  },
  {
    title: 'Tỉ lệ',
    dataIndex: 'ratio',
    key: 'ratio',
    width: 200,
    render: (value: string) => (value ? value : '-'),
  },
];
