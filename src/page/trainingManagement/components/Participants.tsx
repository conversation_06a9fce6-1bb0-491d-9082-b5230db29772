import { DeleteOutlined } from '@ant-design/icons';
import {
  App,
  Button,
  Checkbox,
  Col,
  Flex,
  Form,
  notification,
  Radio,
  RadioChangeEvent,
  Table,
  TableColumnsType,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { OPTIONS_ATTEND_ACCORDINGLY } from '../../../constants/common';
import { useCreateField, useDeleteField, useFetch } from '../../../hooks';
import { getListAllEmployeeDropdown } from '../../../service/employee';
import { getOrgChartDropdown } from '../../../service/lead';
import { deleteUserEvent, getAllUserEvent, postUserToTraining } from '../../../service/training';
import { TEmployeeAll } from '../../../types/customers';
import { IUserEvent, TSubmitUserEvent, TTableUserEvent } from '../../../types/training/training';
import { useStoreTraining } from '../storeTraining';
import { TListDropdown } from '../../../types/salesPolicy';

const { Item } = Form;
const { Text } = Typography;

function groupByPosId(data: IUserEvent[]) {
  const map = new Map<string, TTableUserEvent>();
  for (const item of data) {
    const { posId, dvbh } = item;
    if (!map.has(posId)) {
      map.set(posId, {
        id: posId,
        posName: dvbh,
        children: [],
      });
    }
    map.get(posId)?.children?.push(item);
  }
  return Array.from(map.values());
}

const columns: TableColumnsType = [
  Table.EXPAND_COLUMN,
  {
    title: 'Sàn',
    dataIndex: 'posName',
    width: '30%',
    render: text => <Text>{text}</Text>,
  },
  {
    title: 'Tài khoản',
    width: '30%',
    dataIndex: 'username',
  },
  {
    title: 'Họ tên',
    width: '30%',
    dataIndex: 'name',
  },
];

const Participants = () => {
  const { id } = useParams();
  const { modal } = App.useApp();
  const { openModalCreate, initialValues, setIsModified } = useStoreTraining();
  const [dataAllUserEvent, setDataAllUserEvent] = useState<TTableUserEvent[]>();
  const [selectedAttend, setSelectedAttend] = useState<TEmployeeAll & TListDropdown>();
  const [attendType, setAttendType] = useState('orgChart');
  const form = Form.useFormInstance();

  const { data } = useFetch<IUserEvent[]>({
    api: getAllUserEvent,
    queryKeyArr: ['get-all-user-event', id],
    moreParams: { id },
    withFilter: false,
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const { data: listEmployees } = useFetch<TEmployeeAll[]>({
    api: getListAllEmployeeDropdown,
    queryKeyArr: ['employee-dropdown-nvkd', selectedAttend?.code],
    moreParams: { search: selectedAttend?.code },
    withFilter: false,
    enabled: attendType === 'orgChart' && !!selectedAttend?.code,
  });
  const listEmployeesData = listEmployees?.data?.data || [];

  const { mutateAsync: mutateAsyncUserEvent } = useCreateField({
    apiQuery: postUserToTraining,
    keyOfDetailQuery: ['get-all-user-event', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: deleteUser, isPending } = useDeleteField({
    apiQuery: deleteUserEvent,
    keyOfDetailQuery: ['get-all-user-event', id],
    isMessageError: false,
    isMessageSuccess: false,
  });
  const actionColumns: TableColumnsType = [
    ...columns,
    {
      title: '',
      width: '5%',
      dataIndex: 'actions',
      render: (_, record) => {
        const handleDelete = async () => {
          return modalConfirm({
            modal: modal,
            loading: isPending,
            title: `Xóa nhân viên`,
            content: `Bạn có muốn xóa nhân viên này không?`,
            handleConfirm: async () => {
              const res = await deleteUser(record?.id);
              if (res?.data?.statusCode === '0') {
                notification.success({ message: `xóa nhân viên thành công` });
              }
            },
          });
        };

        return !record?.children && <Button type="text" danger icon={<DeleteOutlined />} onClick={handleDelete} />;
      },
    },
  ];
  useEffect(() => {
    if (dataSource) {
      const newData = groupByPosId(dataSource) || [];
      setDataAllUserEvent(newData);
    }
  }, [dataSource]);

  const handleSelectListCustomer = (value: unknown) => {
    setSelectedAttend(value as TEmployeeAll & TListDropdown);
  };

  const handleSelectListAdmin = (values: { option: TEmployeeAll }[]) => {
    form.setFieldsValue({
      listAdmin: values?.map(item => ({
        ...item?.option,
      })),
    });
    setIsModified(true);
  };

  const handleAttendTypeChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setAttendType(value);
    setSelectedAttend(undefined);
  };

  const handleAddUserEvent = async () => {
    const newData: TSubmitUserEvent[] =
      attendType === 'orgChart'
        ? listEmployeesData?.map((item, index) => ({
            ...item,
            show: false,
            isFirst: index === 0 ? true : false,
          })) || []
        : [{ show: false, isFirst: false, ...selectedAttend } as TSubmitUserEvent];

    if (!newData || newData.length === 0) {
      notification.error({ message: 'Không có nhân viên nào trong đơn vị' });
      return;
    }
    const res = await mutateAsyncUserEvent({ id, payload: newData });
    if (res?.data?.statusCode === '0') {
      notification.success({ message: 'Thêm nhân viên thành công' });
      setSelectedAttend(undefined);
    }
  };

  return (
    <>
      <Col span={24}>
        <h3>Thành phần tham dự</h3>
      </Col>
      <Col span={24}>
        <Item label="Quản trị viên" name="listAdmin">
          <MultiSelectLazy
            enabled={!!openModalCreate || !!id}
            apiQuery={getListAllEmployeeDropdown}
            queryKey={['employee-dropdown']}
            keysLabel={['username', 'name']}
            handleListSelect={handleSelectListAdmin}
            defaultValues={
              initialValues?.listAdmin && initialValues?.listAdmin?.length > 0
                ? initialValues?.listAdmin.map(item => ({
                    ...item,
                    label: item?.name || '',
                    value: item?.id || '',
                  }))
                : []
            }
            placeholder="Chọn nhân viên"
            keysTag={['username']}
          />
        </Item>
      </Col>
      <Col span={24}>
        <Item label="Tham dự theo">
          <Radio.Group options={OPTIONS_ATTEND_ACCORDINGLY} value={attendType} onChange={handleAttendTypeChange} />
        </Item>
      </Col>
      <Col span={24}>
        <Item name="allowRegisterCustomer" valuePropName="checked">
          <Checkbox>Cho phép ĐVBH đăng ký khách hàng</Checkbox>
        </Item>
      </Col>
      <Col span={24}>
        <Item label="Đơn vị/ NVKD tham dự">
          <Flex>
            <SingleSelectLazy
              staleTime={Infinity}
              apiQuery={attendType === 'orgChart' ? getOrgChartDropdown : getListAllEmployeeDropdown}
              enabled={!!openModalCreate || !!id}
              queryKey={['dropdown', attendType]}
              keysLabel={attendType === 'orgChart' ? ['name'] : ['username', 'name']}
              handleSelect={handleSelectListCustomer}
              placeholder={attendType === 'orgChart' ? 'Chọn đơn vị' : 'Chọn nhân viên'}
              defaultValues={
                selectedAttend && {
                  ...selectedAttend,
                  value: selectedAttend?.id,
                  label: selectedAttend?.name,
                }
              }
            />
            <Button type="primary" onClick={() => handleAddUserEvent()} style={{ marginLeft: '10px' }}>
              Thêm
            </Button>
          </Flex>
        </Item>
      </Col>
      <Col span={24}>
        <>
          <Text strong>
            Đơn vị: {dataAllUserEvent?.length} &nbsp;&nbsp;&nbsp; NVKD: {dataSource?.length}
          </Text>
          <Table
            className="table-participants"
            columns={actionColumns}
            expandable={{
              expandedRowRender: () => <p></p>,
              rowExpandable: record => record.children?.length > 0,
            }}
            dataSource={dataAllUserEvent}
            pagination={false}
            rowKey="id"
          />
        </>
      </Col>
    </>
  );
};

export default Participants;
