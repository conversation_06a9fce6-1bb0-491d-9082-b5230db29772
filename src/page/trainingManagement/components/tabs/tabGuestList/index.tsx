import { useMutation } from '@tanstack/react-query';
import { App, Button, Input, notification, Space, Typography, Upload } from 'antd';
import { SearchProps } from 'antd/es/input';
import { ColumnsType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { RcFile } from 'antd/es/upload';
import { InputProps } from 'antd/lib';
import { AxiosError } from 'axios';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { Key, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../../../components/table';
import { FORMAT_DATE, FORMAT_DATE_TIME } from '../../../../../constants/common';
import { useD<PERSON><PERSON><PERSON>ield, useFetch, useUpdateField } from '../../../../../hooks';
import useFilter from '../../../../../hooks/filter';
import { handleErrors } from '../../../../../service/error/errorsService';
import {
  getAllUserEvent,
  getExportUserGuest,
  postImportUser,
  putUserGuestDeleteMany,
  reSendEmail,
} from '../../../../../service/training';
import { IUserEvent } from '../../../../../types/training/training';
import { normalizeString } from '../../../../../utilities/regex';
import { downloadArrayBufferFile } from '../../../../../utilities/shareFunc';

const TemplateUserGuest = '/assets/templateUserGuest.xlsx';

const columns: ColumnsType<IUserEvent> = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    render: text => text || '-',
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: text => text || '-',
  },
  {
    title: 'Yêu cầu tư vấn',
    dataIndex: 'description',
    key: 'description',
    render: text => text || '-',
  },
  {
    title: 'Check In',
    dataIndex: 'checkInDate',
    key: 'checkInDate',
    render: value => (value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'),
  },
  {
    title: 'Check Out',
    dataIndex: 'checkOutDate',
    key: 'checkOutDate',
    render: value => (value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record) =>
      value ? (
        <>
          <Typography.Text>{dayjs(value).format(FORMAT_DATE)}</Typography.Text>
          <br />
          <Typography.Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Typography.Text>
        </>
      ) : (
        '-'
      ),
  },
];
const TabGuestList = () => {
  const { id } = useParams();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [isLoadingImport, setIsLoadingImport] = useState(false);
  const { modal } = App.useApp();
  const [filter] = useFilter();

  const [search, setSearch] = useState<string>();

  const { data, isLoading, refetch } = useFetch<IUserEvent[]>({
    api: getAllUserEvent,
    queryKeyArrWithFilter: ['user-event-guest-list', id, search],
    moreParams: { id, isCustomer: true, query: search },
    enabled: !!id,
  });

  const dataSource = data?.data?.data?.rows || [];

  const { mutateAsync: deleteUser, isPending: isPendingUser } = useDeleteField({
    apiQuery: putUserGuestDeleteMany,
    keyOfListQuery: ['user-event-guest-list', id],
    isMessageError: false,
    label: 'Khách mời',
  });
  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: reSendEmail,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const exportUserGuest = useMutation({
    mutationFn: () => getExportUserGuest(id as string, { query: search, ...filter }),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportUserGuest.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Danh Sach Khach Moi.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const onSelectChange = (selectedRowKeys: Key[]) => {
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<IUserEvent> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[]) => onSelectChange(selectedRowKeys),
  };

  const debouncedHandleSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        const normalizeSearchTerm = normalizeString(searchTerm);
        setSearch(normalizeSearchTerm);
      }, 1000),
    [],
  );

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };

  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    setSearch(normalizeSearchTerm);
  };

  const handleReSendEmail = async () => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    return modalConfirm({
      modal: modal,
      loading: isPending,
      title: `Gửi lại email thư mời`,
      content: `Bạn có muốn gửi lại email thư mời tham gia sự kiện?`,
      handleConfirm: async () => {
        const res = await mutateAsync({ id, userIds: selectedRowKeys as string[] });
        const responseData = res?.data;

        if (responseData) {
          switch (responseData?.statusCode) {
            case '0':
              notification.success({
                message: 'Gửi email thành công',
              });
              setSelectedRowKeys([]);
              break;
            default:
              handleErrors(responseData);
          }
        }
      },
    });
  };

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleDelete = async () => {
    return modalConfirm({
      modal: modal,
      loading: isPending,
      title: `Xóa khách mời`,
      content: `Bạn có muốn xóa khách mời này không?`,
      handleConfirm: async () => {
        const res = await deleteUser({ lstId: selectedRowKeys as string[] });
        if (res?.data?.statusCode === '0') {
          notification.success({ message: `Xóa khách mời thành công` });
          setSearch('');
        }
      },
    });
  };

  // function stringToArrayBuffer(str: string): ArrayBuffer {
  //   const buf = new ArrayBuffer(str.length);
  //   const view = new Uint8Array(buf);
  //   for (let i = 0; i < str.length; i++) {
  //     view[i] = str.charCodeAt(i) & 0xff;
  //   }
  //   return buf;
  // }
  return (
    <div>
      <Space style={{ marginBottom: 16, justifyContent: 'space-between', display: 'flex' }}>
        <Input.Search
          onChange={handleChange}
          value={search}
          onSearch={handleOnSearch}
          placeholder="Search"
          allowClear
        />
        <Space>
          <Button
            type="primary"
            disabled={selectedRowKeys?.length === 0}
            onClick={handleReSendEmail}
            loading={isPending}
          >
            Gửi lại email
          </Button>
          <Button onClick={handleDelete} disabled={selectedRowKeys?.length === 0} loading={isPendingUser}>
            Xoá khách mời
          </Button>
          <Button type="default" href={TemplateUserGuest} download={'Template-Event-Customer.xlsx'}>
            Tải biểu mẫu
          </Button>
          <Upload
            beforeUpload={handleBeforeUpload}
            showUploadList={false}
            customRequest={async ({ file, onSuccess, onError }) => {
              try {
                setIsLoadingImport(true);
                const response = await postImportUser(file as RcFile, id as string);
                // const isBlob = response?.data instanceof Blob || typeof response?.data === 'string';
                if (Array.isArray(response?.data?.models)) {
                  refetch();
                  notification.success({ message: 'Tải file thành công' });
                }
                // else if (isBlob) {
                //   // const dataBuffer = stringToArrayBuffer(response.data);

                //   downloadArrayBufferFile({ data: response.data, fileName: `error-file-${Date.now()}.xlsx` });
                //   notification.error({ message: 'Upload lỗi, kiểm tra file tải về.' });
                // }
                else {
                  handleErrors(response.data);
                }
                onSuccess && onSuccess('ok');
              } catch (error: unknown) {
                onError && onError(error as AxiosError);
              } finally {
                setIsLoadingImport(false);
              }
            }}
            name="file-upload"
          >
            <Button type="default" loading={isLoadingImport}>
              Tải lên danh sách
            </Button>
          </Upload>

          <Button onClick={handleSubmitExport} loading={exportUserGuest?.isPending}>
            Tải xuống danh sách
          </Button>
        </Space>
      </Space>
      <TableComponent
        queryKeyArr={['user-event-guest-list', id, search]}
        rowKey="id"
        rowSelection={rowSelection}
        dataSource={dataSource}
        columns={columns}
        loading={isLoading}
      />
    </div>
  );
};

export default TabGuestList;
