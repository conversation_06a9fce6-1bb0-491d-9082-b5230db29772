import { Col, Form, Input } from 'antd';
import { useParams } from 'react-router-dom';
import SingleSelectLazy from '../../../../../../components/select/singleSelectLazy';
import SelectAddress from '../../../../../../components/selectAddress';
import { getListProject } from '../../../../../../service/project';
import { TProjectDropdown } from '../../../../../../types/common/common';
import { useStoreTraining } from '../../../../storeTraining';
import CustomDatePickerFormItem from '../../../CustomDatePickerFormItem';
import { SwitchFormItem } from '../../../SwitchFormItem';

const { Item } = Form;
const { TextArea } = Input;

const InformationTrainingOffline = () => {
  const { id } = useParams();
  const { openModalCreate, initialValues, setIsModified } = useStoreTraining();
  const form = Form.useFormInstance();

  const handleSelectProject = (values: TProjectDropdown) => {
    form.setFieldsValue({
      project: values
        ? {
            name: values?.name,
            id: values?.id,
          }
        : undefined,
    });
    setIsModified(true);
  };

  return (
    <>
      <CustomDatePickerFormItem />
      <Col span={24}>
        <Item name="project" label="Dự án áp dụng" rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}>
          <SingleSelectLazy
            apiQuery={getListProject}
            moreParams={{ status: '01' }}
            queryKey={['list-projects']}
            enabled={!!openModalCreate || !!id}
            placeholder="Chọn dự án áp dụng"
            keysLabel={['name']}
            handleSelect={handleSelectProject}
            defaultValues={
              initialValues?.project && {
                value: initialValues?.project?.id,
                label: initialValues?.project?.name,
              }
            }
          />
        </Item>
        <Item label="Địa điểm tổ chức" name="interestedArea">
          <SelectAddress
            placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
            parentName={'interestedArea'}
            address={initialValues?.interestedArea}
            handleAddressChange={() => {
              setIsModified(true);
            }}
          />
        </Item>
      </Col>
      <SwitchFormItem label="Kết thúc sự kiện" name="isEventOff" />
      <SwitchFormItem label="Đăng ký không bắt buộc email" name="unRequiredEmailRegister" />
      <Col span={24}>
        <Item label="Mô tả sự kiện" name="eventDescription">
          <TextArea placeholder="Nhập ghi chú" rows={2} />
        </Item>
      </Col>
    </>
  );
};

export default InformationTrainingOffline;
