import { PlusOutlined } from '@ant-design/icons';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Col, Flex, Form, notification, Row, Table, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { FormInstance } from 'antd/lib';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import FormUploadImage from '../../../../../components/upload/FormUploadImage';
import { OPTIONS_STATUS_PRIZES } from '../../../../../constants/common';
import { handleErrors } from '../../../../../service/error/errorsService';
import { getAllsSalesProgramDropdown, uploadSpinAdditional } from '../../../../../service/training';
import { useStoreTraining } from '../../../storeTraining';
import PrizeForm from '../../PrizeForm';
import { SwitchFormItem } from '../../SwitchFormItem';
import './styles.scss';

const TemplateEventCustomerAdditional = '/assets/Template-Event-Customer-Additional.xlsx';

const { Title } = Typography;
const { Item } = Form;
interface Props {
  form: FormInstance;
}

const columns = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    render: (text: string) => text || '-',
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    render: (text: number) => text || '-',
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: (text: string) => text || '-',
  },
];

const TabSpinThePrize = (props: Props) => {
  const { form } = props;
  const { id } = useParams();
  const queryClient = useQueryClient();
  const { openModalCreate, setIsModified, initialValues } = useStoreTraining();
  const [isLoadingImport, setIsLoadingImport] = useState(false);

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleSelectListPrizes = (values?: { label: string; value: string }[]) => {
    form.setFieldValue('transactionStatuses', values ? values.map(value => value?.value) : undefined);
    setIsModified(true);
  };

  const handleSelectSalesProgram = (values?: { name: string; value: string }) => {
    form.setFieldValue(
      'salesProgram',
      values
        ? {
            id: values?.value,
            name: values?.name,
          }
        : undefined,
    );
    setIsModified(true);
  };

  return (
    <>
      <Form
        form={form}
        layout="vertical"
        className="wrapper-tab-spin-the-prize"
        initialValues={{
          ...initialValues,
          colorTitle: initialValues?.colorTitle || '#000000',
          colorTitleTransaction: initialValues?.colorTitleTransaction || '#000000',
          colorPrize: initialValues?.colorPrize || '#000000',
          colorPrizeTransaction: initialValues?.colorPrizeTransaction || '#000000',
          colorWiner: initialValues?.colorWiner || '#000000',
          colorWinerTransaction: initialValues?.colorWinerTransaction || '#000000',
        }}
        onValuesChange={() => {
          setIsModified(true);
        }}
      >
        <Row gutter={24}>
          <Col xs={24} xl={12}>
            <Title level={5}>Quay số may mắn theo người tham dự</Title>
            <Row gutter={24}>
              <PrizeForm />
              <SwitchFormItem label="Cho phép admin xác nhận hộ" name="allowAdminConfirm" />
              <SwitchFormItem label="Quay thưởng bằng CMND/CCCD" name="allowIDCardSpin" />
              <SwitchFormItem label="Cho phép khách vãng lai quay thưởng" name="allowPassersby" />

              <Col span={12}>
                <FormUploadImage
                  path="image"
                  defaultImage={initialValues?.prizeBackground}
                  reset={false}
                  fieldName={'prizeBackground'}
                  label="Ảnh background"
                  fileSize={5}
                  textType="Tối đa 5 MB, định dạng: .jpeg, .jpg, .png"
                  onChange={() => {
                    setIsModified(true);
                  }}
                  iconUpload={<PlusOutlined style={{ color: '#00000073' }} />}
                />
              </Col>
              <Col span={12}>
                <FormUploadImage
                  path="image"
                  defaultImage={initialValues?.prizeBackgroundLive}
                  reset={false}
                  fieldName={'prizeBackgroundLive'}
                  label="Hình ảnh banner quay số "
                  fileSize={5}
                  textType="Tối đa 5 MB, định dạng: .jpeg, .jpg, .png, .svg, .gif"
                  moreTypes={['image/gif', 'image/svg+xml']}
                  onChange={() => {
                    setIsModified(true);
                  }}
                  iconUpload={<PlusOutlined style={{ color: '#00000073' }} />}
                />
              </Col>
            </Row>
            <Title level={5}>Quay số may mắn theo giao dịch</Title>
            <Row gutter={24}>
              <PrizeForm isTransaction={true} />
              <SwitchFormItem label="Cho phép admin xác nhận hộ" name="allowAdminConfirmTransaction" />
              <Col span={24}>
                <Item label="Chương trình bán hàng" name="salesProgram">
                  <SingleSelectLazy
                    apiQuery={getAllsSalesProgramDropdown}
                    queryKey={['sales-programs']}
                    keysLabel={['name']}
                    enabled={!!openModalCreate || !!id}
                    handleSelect={handleSelectSalesProgram}
                    placeholder="Chọn Chương trình bán hàng"
                    defaultValues={
                      initialValues?.salesProgram && {
                        value: initialValues?.salesProgram?.id,
                        label: initialValues?.salesProgram?.name,
                      }
                    }
                  />
                </Item>
              </Col>
              <Col span={24}>
                <Item label="Trạng thái được quay thưởng" name="transactionStatuses">
                  <MultiSelectLazy
                    externalData={OPTIONS_STATUS_PRIZES}
                    enabled={false}
                    keysLabel={'label'}
                    keysTag={['label']}
                    handleListSelect={handleSelectListPrizes}
                    placeholder="Chọn trạng thái được quay thưởng"
                    defaultValues={OPTIONS_STATUS_PRIZES.filter(item =>
                      initialValues?.transactionStatuses?.includes(item.value),
                    )}
                  />
                </Item>
              </Col>

              <Col span={12}>
                <FormUploadImage
                  path="image"
                  defaultImage={initialValues?.transactionPrizeBackground}
                  reset={false}
                  fieldName={'transactionPrizeBackground'}
                  label="Ảnh background"
                  fileSize={5}
                  textType="Tối đa 5 MB, định dạng: .jpeg, .jpg, .png"
                  onChange={() => {
                    setIsModified(true);
                  }}
                  iconUpload={<PlusOutlined style={{ color: '#00000073' }} />}
                />
              </Col>
            </Row>
            {id && (
              <>
                <Title level={5}>Danh sách bổ sung quay thưởng</Title>
                <Row gutter={[24, 16]}>
                  <Col span={24}>
                    <Flex gap={24}>
                      <Upload
                        beforeUpload={handleBeforeUpload}
                        showUploadList={false}
                        customRequest={async ({ file, onSuccess, onError }) => {
                          try {
                            setIsLoadingImport(true);
                            const response = await uploadSpinAdditional(file as RcFile, id as string);
                            if (response?.data?.statusCode === '0') {
                              notification.success({ message: 'tải file thành công' });
                              await queryClient.invalidateQueries({
                                queryKey: ['detail-of-training', id],
                              });
                            } else {
                              handleErrors(response.data);
                            }

                            onSuccess && onSuccess('ok');
                          } catch (error: unknown) {
                            onError && onError(error as AxiosError);
                          } finally {
                            setIsLoadingImport(false);
                          }
                        }}
                        name="file-upload"
                      >
                        <Button type="default" loading={isLoadingImport}>
                          Tải lên danh sách
                        </Button>
                      </Upload>
                      <Button
                        type="default"
                        href={TemplateEventCustomerAdditional}
                        download={'TemplateEventCustomerAdditional.xlsx'}
                      >
                        Tải nhập giao dịch
                      </Button>
                    </Flex>
                  </Col>
                  <Col span={24}>
                    <Table columns={columns} pagination={false} dataSource={initialValues?.listSpinAdditional} />
                  </Col>
                </Row>
              </>
            )}
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default TabSpinThePrize;
