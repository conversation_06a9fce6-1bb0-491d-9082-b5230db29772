import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown } from 'antd';
import { useParams } from 'react-router-dom';
import { TRAINING } from '../../../configs/path';
import { PERMISSION_TRAINING } from '../../../constants/permissions/training';
import { useCheckPermissions } from '../../../hooks';
import { useStoreTraining } from '../storeTraining';

const DropdownReport = () => {
  const { id } = useParams();
  const { getCountParticipantsDialing, getPrizes, eventChatAndReactDownloadImportFile } =
    useCheckPermissions(PERMISSION_TRAINING);
  const { setOpenReportChatAndReact } = useStoreTraining();

  const items = [
    getCountParticipantsDialing && {
      key: 'report-participants',
      label: (
        <a target="_blank" rel="noopener noreferrer" href={`${TRAINING}/${id}/report-participants`}>
          <PERSON><PERSON><PERSON> cáo ng<PERSON>ời tham dự
        </a>
      ),
    },
    eventChatAndReactDownloadImportFile && {
      key: 'report-interactions',
      label: <span onClick={() => setOpenReportChatAndReact(true)}>Báo cáo tương tác, bình luận</span>,
    },
    getPrizes && {
      key: 'report-prizes',
      label: (
        <a target="_blank" rel="noopener noreferrer" href={`${TRAINING}/${id}/report-prizes`}>
          Báo cáo giải thưởng
        </a>
      ),
    },
  ].filter((item): item is { key: string; label: JSX.Element } => Boolean(item));

  return (
    <>
      <Dropdown menu={{ items }} placement="bottomRight">
        <Button type="default">
          Báo cáo <DownOutlined />
        </Button>
      </Dropdown>
    </>
  );
};

export default DropdownReport;
