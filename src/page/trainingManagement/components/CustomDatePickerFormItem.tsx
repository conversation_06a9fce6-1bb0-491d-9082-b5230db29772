import { Col, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';

const { Item } = Form;

interface ICustomDatePickerFormItem {
  placeholderStart?: string;
  placeholderEnd?: string;
  format?: string;
  isShowTime?: boolean;
}

const CustomDatePickerFormItem = ({
  format = FORMAT_DATE_TIME,
  isShowTime = true,
  placeholderEnd = 'Chọn thời gian',
  placeholderStart = 'Chọn thời gian',
}: ICustomDatePickerFormItem) => {
  const form = Form.useFormInstance();

  const dateValueProps = (value: string | null) => ({
    value: value ? dayjs(value) : null,
  });

  const dateValueFromEvent = (date: dayjs.Dayjs | null) => (date ? date.toISOString() : null);

  return (
    <>
      <Col span={12}>
        <Item
          label="Thời gian bắt đầu sự kiện"
          name="displayBannerStartTime"
          rules={[{ required: true, message: 'Vui lòng chọn thời gian.' }]}
          getValueFromEvent={dateValueFromEvent}
          getValueProps={dateValueProps}
          dependencies={['displayBannerEndTime', 'startTimeCheckIn', 'endTimeCheckIn']}
        >
          <DatePicker
            format={format}
            showTime={isShowTime}
            disabledDate={current => {
              const endDate = form.getFieldValue('displayBannerEndTime');
              return endDate && dayjs(current).isAfter(dayjs(endDate), isShowTime ? 'minute' : 'day');
            }}
            placeholder={placeholderStart}
          />
        </Item>
      </Col>
      <Col span={12}>
        <Item
          label="Thời gian kết thúc sự kiện"
          name="displayBannerEndTime"
          rules={[
            { required: true, message: 'Vui lòng chọn thời gian.' },
            ({ getFieldValue }) => ({
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const startTime = getFieldValue('displayBannerStartTime');

                if (value <= startTime) {
                  return Promise.reject('Thời gian kết thúc phải lớn hơn thời gian bắt đầu.');
                }
                return Promise.resolve();
              },
            }),
          ]}
          getValueFromEvent={dateValueFromEvent}
          getValueProps={dateValueProps}
          dependencies={['displayBannerStartTime', 'startTimeCheckIn', 'endTimeCheckIn']}
        >
          <DatePicker
            format={format}
            showTime={isShowTime}
            disabledDate={current => {
              const startDate = form.getFieldValue('displayBannerStartTime');

              return startDate && dayjs(current).isBefore(dayjs(startDate), 'day');
            }}
            placeholder={placeholderEnd}
          />
        </Item>
      </Col>
      <Col span={12}>
        <Item
          label="Thời gian bắt đầu checkin"
          name="startTimeCheckIn"
          rules={[
            { required: true, message: 'Vui lòng chọn thời gian.' },
            ({ getFieldValue }) => ({
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const startTime = getFieldValue('displayBannerStartTime');
                const endTime = getFieldValue('displayBannerEndTime');

                if (value <= startTime || value >= endTime) {
                  return Promise.reject('Thời gian checkin phải nằm trong khoảng thời gian diễn ra sự kiện.');
                }
                return Promise.resolve();
              },
            }),
          ]}
          getValueFromEvent={dateValueFromEvent}
          getValueProps={dateValueProps}
          dependencies={['displayBannerStartTime', 'displayBannerEndTime', 'endTimeCheckIn']}
        >
          <DatePicker
            format={format}
            showTime={isShowTime}
            disabledDate={current => {
              const endDate = form.getFieldValue('endTimeCheckIn');
              return endDate && dayjs(current).isAfter(dayjs(endDate), isShowTime ? 'minute' : 'day');
            }}
            placeholder={placeholderStart}
          />
        </Item>
      </Col>
      <Col span={12}>
        <Item
          label="Thời gian kết thúc checkin"
          name="endTimeCheckIn"
          rules={[
            { required: true, message: 'Vui lòng chọn thời gian.' },
            ({ getFieldValue }) => ({
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const startTimeCheckIn = getFieldValue('startTimeCheckIn');
                const startTime = getFieldValue('displayBannerStartTime');
                const endTime = getFieldValue('displayBannerEndTime');

                if (value <= startTimeCheckIn) {
                  return Promise.reject('Thời gian checkout phải lớn hơn thời gian checkin.');
                }
                if (value <= startTime || value >= endTime) {
                  return Promise.reject('Thời gian checkout phải nằm trong khoảng thời gian diễn ra sự kiện.');
                }
                return Promise.resolve();
              },
            }),
          ]}
          getValueFromEvent={dateValueFromEvent}
          getValueProps={dateValueProps}
          dependencies={['displayBannerStartTime', 'displayBannerEndTime', 'startTimeCheckIn']}
        >
          <DatePicker
            format={format}
            showTime={isShowTime}
            disabledDate={current => {
              const startDate = form.getFieldValue('startTimeCheckIn');
              return startDate && dayjs(current).isBefore(dayjs(startDate), 'day');
            }}
            placeholder={placeholderEnd}
          />
        </Item>
      </Col>
    </>
  );
};

export default CustomDatePickerFormItem;
