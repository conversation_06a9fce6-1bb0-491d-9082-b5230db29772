import { App, Button, Checkbox, Col, Form, Input, Row, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import { useParams } from 'react-router-dom';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { useCreateField } from '../../../hooks';
import { sendNotification } from '../../../service/training';
import { useStoreTraining } from '../storeTraining';
import RichEditor from '../../../components/richEditor';

const { Item } = Form;
const { TextArea } = Input;
const { Text } = Typography;

const ContactTraining = () => {
  const { id } = useParams();
  const { notification } = App.useApp();
  const [notificationForm] = Form.useForm();
  const [isEmailEnabled, setEmailEnabled] = useState(true);
  const [emailContent, setEmailContent] = useState('');
  const quillRef = useRef<ReactQuill>(null);
  const { eventType, initialValues } = useStoreTraining();

  const sendNotificationTraining = useCreateField({
    apiQuery: sendNotification,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const handleQuillChange = (value: string) => {
    setEmailContent(value);
  };

  const handleSendNotification = async () => {
    try {
      const values = await notificationForm.validateFields();
      const res = await sendNotificationTraining.mutateAsync({
        eventId: id,
        content: values.notification,
        title: values.notification,
      });
      if (res?.data?.statusCode === '0') {
        notification.success({ message: 'Gửi thông báo thành công' });
      }
    } catch (err) {
      console.error('Lỗi validate:', err);
    }
  };

  return (
    <>
      {eventType === 'ONLINE' && id && (
        <>
          <h3>Gửi thông báo trực tiếp</h3>
          <Form form={notificationForm} layout="vertical">
            <Form.Item
              label="Nội dung thông báo"
              name="notification"
              rules={[{ required: true, message: 'Vui lòng nhập nội dung thông báo', whitespace: true }]}
            >
              <TextArea placeholder="Nhập nội dung thông báo cần gửi" rows={2} />
            </Form.Item>
            <Button type="primary" style={{ marginBottom: '16px' }} onClick={handleSendNotification}>
              Gửi
            </Button>
          </Form>
        </>
      )}
      {/* Mẫu SMS */}
      <h3>Mẫu SMS</h3>
      <Item label="Tiêu đề sms" name={['templateConfig', 'smsBrandName']}>
        <Input placeholder="Nhập tiêu đề sms" />
      </Item>
      <Item name={['templateConfig', 'smsContent']}>
        <TextArea placeholder="Nhập nội dung sms" rows={2} />
      </Item>
      {/* Mẫu email */}
      <h3>Mẫu email</h3>
      <Item
        name={['templateConfig', 'resend']}
        label="Gửi lại email"
        valuePropName="checked"
        labelCol={{ span: 6 }}
        labelAlign="left"
        layout="horizontal"
      >
        <Checkbox checked={isEmailEnabled} onChange={e => setEmailEnabled(e.target.checked)} />
      </Item>
      <Item label="Tiêu đề mail" name={['templateConfig', 'subject']}>
        <Input placeholder="Nhập tiêu đề mail" disabled={!isEmailEnabled} />
      </Item>
      <Item name={['templateConfig', 'body']}>
        <RichEditor
          onChange={handleQuillChange}
          value={emailContent}
          ref={quillRef}
          placeholder="Nhập nội dung email"
        />
      </Item>
      <Row gutter={24}>
        <Col lg={6} xs={8}>
          <Text disabled>Ngày cập nhật: </Text>
        </Col>
        <Col lg={18} xs={16}>
          <Text disabled>
            {dayjs(initialValues?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
            {`${initialValues?.modifiedBy?.userName || ''} - ${initialValues?.modifiedBy?.fullName || ''}`}
          </Text>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col lg={6} xs={8}>
          <Text disabled>Ngày tạo: </Text>
        </Col>
        <Col lg={18} xs={16}>
          <Text disabled>
            {dayjs(initialValues?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
            {`${initialValues?.createdBy?.userName || ''} - ${initialValues?.createdBy?.fullName || ''}`}
          </Text>
        </Col>
      </Row>
    </>
  );
};

export default ContactTraining;
