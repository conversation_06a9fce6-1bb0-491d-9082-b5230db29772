import { <PERSON><PERSON>, Card, Dropdown, Flex, Image, Row, Space, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import { GamePriceStatusEnum } from '../../../constants/common';
import { useCreateField } from '../../../hooks';
import { postConfirmSpin, postSelectedPrize, postStartSpin, postStopSpin } from '../../../service/prizeSpin';
import { TPrize, TUserData } from '../../../types/training/training';
import ModalResultPrize from './modalResultPrize';

interface IContentSPinProps {
  dataPrize?: TPrize[];
  namePrize?: string;
  type: 'customer' | 'transaction';
  key: string;
}
const { Title } = Typography;

// Randomly generate a 10-digit phone number starting with '09'
const randomPhoneNumber = () => {
  const prefix = '09';
  let number = prefix;
  for (let i = 0; i < 8; i++) {
    number += Math.floor(Math.random() * 10).toString();
  }
  return number;
};

const getLast6Digits = (phone: string) => phone.slice(-6).split('');

const DigitBox = ({ digit }: { digit: string }) => (
  <Card
    style={{
      width: 60,
      height: 80,
      backgroundColor: '#4ea8ff',
      color: 'white',
      fontSize: 40,
      textAlign: 'center',
      padding: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 5px',
      borderRadius: 8,
    }}
  >
    {digit}
  </Card>
);

const ContentSPin = ({ dataPrize, namePrize, type, key }: IContentSPinProps) => {
  const { id } = useParams();
  const [digits, setDigits] = useState(['0', '0', '0', '0', '0', '0']);
  const [selectedAward, setSelectedAward] = useState<TPrize | null>(null);
  const [statusPrize, setStatusPrize] = useState<string | null>(null);
  const [openModalConfirm, setOpenModalConfirm] = useState(false);
  const [userWinner, setUserWinner] = useState<TUserData | null>(null);

  const countPrize = useMemo(() => {
    const prizeEnds = selectedAward?.prizes?.filter(prize => prize.prizeStatus === GamePriceStatusEnum.END) || [];
    return (selectedAward?.amount || 0) - (prizeEnds?.length || 0) || 0;
  }, [selectedAward]);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startSpin = useCreateField({
    keyOfDetailQuery: ['detail-of-training', id],
    apiQuery: postStartSpin,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const selectPrize = useCreateField({
    keyOfDetailQuery: ['detail-of-training', id],
    apiQuery: postSelectedPrize,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const stopSpinMutation = useCreateField({
    keyOfDetailQuery: ['detail-of-training', id],
    apiQuery: postStopSpin,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const confirmPrize = useCreateField({
    keyOfDetailQuery: ['detail-of-training', id],
    apiQuery: postConfirmSpin,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const [availablePrizes, setAvailablePrizes] = useState<TPrize[] | undefined>(dataPrize);

  const handleSelectPrize = useCallback(
    async (award: TPrize) => {
      if (id) {
        const res = await selectPrize.mutateAsync({
          id,
          priceLevel: award?.prizeType,
          type,
        });
        if (res?.data?.statusCode === '0') {
          setSelectedAward(award);
          setAvailablePrizes(dataPrize?.filter(prize => prize.prizeName !== award.prizeName));
        }
      }
    },
    [dataPrize, id, selectPrize, type],
  );

  const itemsPrize = useMemo(
    () =>
      availablePrizes?.map((award: TPrize) => ({
        label: <span onClick={() => handleSelectPrize(award)}>{award?.prizeName}</span>,
        key: uuid(),
      })),
    [availablePrizes, handleSelectPrize],
  );

  const startVisualSpin = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      const randomDigits = Array.from({ length: 6 }, () => Math.floor(Math.random() * 10).toString());
      setDigits(randomDigits);
    }, 100);

    setStatusPrize(GamePriceStatusEnum.START_SPINING);
  }, []);

  useEffect(() => {
    if (dataPrize) {
      const initStartSpin = dataPrize.find(award => award?.isSelected);

      if (initStartSpin) {
        const prizes = initStartSpin.prizes || [];
        if (prizes.some(prize => prize.prizeStatus === GamePriceStatusEnum.START_SPINING)) {
          startVisualSpin();
        } else if (prizes.some(prize => prize.prizeStatus === GamePriceStatusEnum.STOP_SPINING)) {
          const itemStop = prizes.find(prize => prize.prizeStatus === GamePriceStatusEnum.STOP_SPINING);
          if (itemStop?.userData?.phoneNumber) {
            setDigits(getLast6Digits(itemStop.userData.phoneNumber));
            setStatusPrize(GamePriceStatusEnum.STOP_SPINING);
            setUserWinner(itemStop.userData);
          }
        }
      }
    }
  }, [dataPrize, startVisualSpin]);

  useEffect(() => {
    if (dataPrize && !selectedAward) {
      setAvailablePrizes(dataPrize);
    }
  }, [dataPrize, selectedAward]);

  const stopSpin = useCallback(async () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (!id || !selectedAward) return;

    const res = await stopSpinMutation.mutateAsync({
      id,
      priceLevel: selectedAward?.prizeType,
      type,
    });
    if (res?.data?.statusCode === '0') {
      const randomPhone = randomPhoneNumber();
      setDigits(getLast6Digits(randomPhone));
      setStatusPrize(GamePriceStatusEnum.STOP_SPINING);
      setOpenModalConfirm(true);
      setUserWinner(
        selectedAward?.prizes?.find(prize => prize.prizeStatus === GamePriceStatusEnum.STOP_SPINING)?.userData || null,
      );
    }
  }, [id, selectedAward, stopSpinMutation, type]);

  const spin = useCallback(async () => {
    if (!id || !selectedAward) return;

    const res = await startSpin.mutateAsync({
      id,
      priceLevel: selectedAward.prizeType,
      type,
    });

    if (res?.data?.statusCode === '0') {
      startVisualSpin();
    }
  }, [id, selectedAward, startVisualSpin, startSpin, type]);

  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  const handConfirmPrize = useCallback(async () => {
    if (!id || !selectedAward) return;
    const itemStop = selectedAward?.prizes.find(prize => prize.prizeStatus === 'STOP_SPINING');

    const res = await confirmPrize.mutateAsync({
      type,
      eventId: id,
      userEmail: itemStop?.userData?.email || '',
      winnerId: itemStop?.userData?.id || '',
    });

    if (res?.data?.statusCode === '0') {
      setStatusPrize(GamePriceStatusEnum.END);
      setUserWinner(null);
      setOpenModalConfirm(false);
      setSelectedAward(null);
      setDigits(['0', '0', '0', '0', '0', '0']);
    }
  }, [id, selectedAward, confirmPrize, type]);

  const getButtons = () => {
    switch (statusPrize) {
      case GamePriceStatusEnum.START_SPINING:
        return (
          <Button type="default" onClick={stopSpin}>
            Dừng quay
          </Button>
        );
      case GamePriceStatusEnum.END:
      default:
        return (
          <>
            {!!countPrize && (
              <Button type="default" onClick={spin}>
                Quay số
              </Button>
            )}
            <Dropdown menu={{ items: itemsPrize }} trigger={['click']} arrow>
              <Button type="primary">Chọn giải thưởng</Button>
            </Dropdown>
          </>
        );
    }
  };

  return (
    <Flex className="content-spin" vertical align="center" gap={24} justify="center">
      <Title level={1}>{namePrize}</Title>
      <div style={{ textAlign: 'center' }}>
        <Title level={3} style={{ marginTop: 0 }}>
          Giải thưởng: {selectedAward?.prizeName}
        </Title>
        <Title level={5} style={{ marginTop: 0 }}>
          Số lượng giải thưởng: {countPrize}
        </Title>
      </div>
      {selectedAward?.prizeImage && (
        <Image
          src={`${import.meta.env.VITE_S3_IMAGE_URL}/${selectedAward?.prizeImage}`}
          style={{ maxWidth: 600, maxHeight: 400 }}
          preview={false}
        />
      )}
      <Row justify="center">
        {digits.map((d, i) => (
          <DigitBox key={i} digit={d} />
        ))}
      </Row>
      <Space>{getButtons()}</Space>
      <ModalResultPrize
        open={openModalConfirm}
        onOk={handConfirmPrize}
        prizeType={key}
        winnerName={userWinner?.name}
        prize={selectedAward}
      />
    </Flex>
  );
};

export default ContentSPin;
