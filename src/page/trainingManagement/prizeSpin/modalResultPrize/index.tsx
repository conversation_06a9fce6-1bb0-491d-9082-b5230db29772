import { Button, Modal, Typography } from 'antd';
import backgroundImageResultPrize from '../../../../assets/images/resultPrize.png';
import backgroundImageResultTransactionPrize from '../../../../assets/images/resultTransacsionPrize.png';
import './style.scss';
import { TPrize } from '../../../../types/training/training';

const { Title } = Typography;

interface IModalResultPrizeProps {
  open: boolean;
  onOk?: () => void;
  winnerName?: string;
  prizeType?: string;
  prize: TPrize | null;
}

const ModalResultPrize = ({ open, onOk, winnerName, prizeType = 'customer', prize }: IModalResultPrizeProps) => {
  return (
    <Modal
      className="modal-result-prize"
      open={open}
      footer={null}
      closable={false}
      onOk={onOk}
      centered
      width={700}
      styles={{
        content: { padding: 0 },
        body: {
          backgroundImage: `url(${prizeType === 'customer' ? backgroundImageResultPrize : backgroundImageResultTransactionPrize})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          minHeight: 500,
        },
      }}
    >
      <div className="content-result-prize">
        <div className="title-result-prize">
          <Title level={1}>{prizeType}</Title>
          <Title
            level={1}
            style={{
              textAlign: 'center',
            }}
          >
            {prize?.prizeName}
          </Title>
        </div>

        <div className="info-prize">
          <div className="info-prize-title">THÔNG TIN GIẢI</div>
          <table
            style={{
              width: '100%',
              color: '#fff',
              borderCollapse: 'collapse',
              fontSize: 16,
            }}
          >
            <tbody>
              <tr>
                <td style={{ padding: '8px 12px', border: '1px solid #ccc' }}>Tên người trúng thưởng</td>
                <td style={{ padding: '8px 12px', border: '1px solid #ccc' }}>{winnerName}</td>
              </tr>
              <tr>
                <td style={{ padding: '8px 12px', border: '1px solid #ccc' }}>Giá trị giải thưởng</td>
                <td style={{ padding: '8px 12px', border: '1px solid #ccc' }}>{prize?.prizeValue}</td>
              </tr>
            </tbody>
          </table>
          <div className="btn-close">
            <Button type="primary" onClick={onOk}>
              Tiếp tục quay thưởng
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ModalResultPrize;
