import { Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useFetch } from '../../../hooks';
import { getDetailOfTraining } from '../../../service/training';
import { TTraining } from '../../../types/training/training';
import ContentSPin from './ContentSPin';
import './style.scss';

export default function PrizeSpin() {
  const { id } = useParams<{ id: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const [mode, setMode] = useState<string>(params?.mode || 'customer');

  const { data } = useFetch<TTraining>({
    api: () => getDetailOfTraining(id),
    queryKeyArr: ['detail-of-training', id],
  });
  const dataSource = data?.data?.data;
  const handleTabChange = (activeKey: string) => {
    setMode(activeKey);
    setSearchParams({ ...params, mode: activeKey });
  };
  const items: TabsProps['items'] = [
    {
      key: 'customer',
      label: 'Quay số theo người tham dự',
      children: (
        <ContentSPin key="userSpin" dataPrize={dataSource?.prizes} namePrize={dataSource?.prizeName} type="customer" />
      ),
    },
    {
      key: 'transaction',
      label: 'Quay số theo giao dịch',
      children: (
        <ContentSPin
          key="transactionSpin"
          type="transaction"
          dataPrize={dataSource?.transactionPrizes}
          namePrize={dataSource?.transactionPrizeName}
        />
      ),
    },
  ];
  return (
    <div className="wrapper-spin-prizes" style={{ padding: 42 }}>
      <Tabs
        activeKey={mode}
        type="card"
        className="tabs-spin-prizes"
        onChange={handleTabChange}
        items={items}
        size="small"
        tabBarStyle={{
          marginBottom: 16,
        }}
      />
    </div>
  );
}
