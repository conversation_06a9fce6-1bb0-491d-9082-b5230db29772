import { Form, FormInstance, Spin, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { useEffect } from 'react';
import { useBeforeUnload, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { useFetch, useUpdateField } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import { getDetailOfTraining, putTraining } from '../../../service/training';
import { TPrize, TTraining } from '../../../types/training/training';
import { slugify } from '../../../utilities/shareFunc';
import ModelReportChatAndReact from '../components/ModelReportChatAndReact';
import TabGeneralInfo from '../components/tabs/tabGeneralInfo';
import TabGuestList from '../components/tabs/tabGuestList';
import TabSpinThePrize from '../components/tabs/tabSpinThePrize';
import { useStoreTraining } from '../storeTraining';
import './styles.scss';
import DropdownReport from '../components/DropdownReport';

function filteredForm(form: FormInstance, initialValue: TTraining) {
  const fields = form.getFieldsValue();
  const filteredFields = Object.keys(fields).reduce(
    (acc, key) => {
      if (key in initialValue) {
        acc[key] = initialValue[key as keyof TTraining];
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );
  return filteredFields;
}

const DetailOfTraining = () => {
  const { id } = useParams();
  const [filter, setFilter] = useFilter();

  const [formGeneralInfo] = Form.useForm();
  const [formPrize] = Form.useForm();
  const { isModified, setInitialValues, setIsModified, setEventType } = useStoreTraining();

  const { data, isLoading } = useFetch<TTraining>({
    api: () => getDetailOfTraining(id),
    queryKeyArr: ['detail-of-training', id],
  });
  const dataSource = data?.data?.data;

  const { mutateAsync } = useUpdateField({
    apiQuery: putTraining,
    keyOfDetailQuery: ['detail-of-training', id],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataSource) {
      const initialValue = {
        ...dataSource,
        isActive: dataSource.isActive,
        eventType: dataSource.eventType || 'ONLINE',
      };
      const generalInfo = filteredForm(formGeneralInfo, initialValue);

      const prize = filteredForm(formPrize, initialValue);
      setEventType(initialValue.eventType);
      setInitialValues(dataSource);
      formGeneralInfo.setFieldsValue(generalInfo);
      formPrize.setFieldsValue(prize);
    }
  }, [dataSource, formGeneralInfo, formPrize, setEventType, setInitialValues]);

  const handleSubmit = async () => {
    const formPrizeFields = formPrize.getFieldsValue();

    const generalInfo = await formGeneralInfo.validateFields().catch(() => {
      return { error: true, form: 'general' };
    });
    if (generalInfo?.error) {
      formGeneralInfo.scrollToField(generalInfo.form);
      return;
    }
    const newData = {
      id,
      ...generalInfo,
      urlEvent: slugify(generalInfo?.urlEvent),
      ...formPrizeFields,
      prizes: formPrizeFields?.prizes?.map((prize: TPrize, index: number) => ({
        ...prize,
        prizeType: index + 1,
        prizeStatus: 'INIT',
      })),
      transactionPrizes: formPrizeFields?.transactionPrizes?.map((prize: TPrize, index: number) => ({
        ...prize,
        prizeType: index + 1,
        prizeStatus: 'INIT',
      })),
    };
    const res = await mutateAsync(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  useBeforeUnload(event => {
    if (isModified && (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched())) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });
  const handleCancel = () => {
    if (isModified && (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched())) {
      formGeneralInfo.resetFields();
      formPrize.resetFields();
      setIsModified(false);
    }
  };

  const items: TabsProps['items'] = [
    { label: 'Thông tin chung', key: 'general-info', children: <TabGeneralInfo form={formGeneralInfo} /> },
    { label: 'Quay thưởng', key: 'prize', children: <TabSpinThePrize form={formPrize} /> },
    { label: 'Danh sách khách mời', key: 'guest-list', children: <TabGuestList /> },
  ];

  return (
    <Spin spinning={isLoading}>
      <div className="wrapper-detail-of-training">
        <BreadCrumbComponent titleBread={dataSource?.name || 'Chi tiết sự kiện'} />
        <Tabs
          items={items}
          onChange={value => {
            setFilter({ ...filter, tab: value });
            const url = new URL(window.location.href);
            url.searchParams.set('tab', value);
            window.history.pushState({}, '', url);
          }}
          activeKey={filter.tab || 'general-info'}
          tabBarExtraContent={<DropdownReport />}
        />
        {isModified && (
          <ButtonOfPageDetail handleCancel={handleCancel} handleSubmit={handleSubmit} loadingSubmit={false} />
        )}
      </div>
      <ModelReportChatAndReact />
    </Spin>
  );
};

export default DetailOfTraining;
