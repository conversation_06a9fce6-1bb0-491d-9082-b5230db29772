import { useMutation } from '@tanstack/react-query';
import { Button, Input, notification } from 'antd';
import { SearchProps } from 'antd/es/input';
import { InputProps } from 'antd/lib';
import debounce from 'lodash/debounce';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { TRAINING } from '../../../configs/path';
import { useFetch } from '../../../hooks';
import { exportListOfReportPrizes, getListOfReportPrizes } from '../../../service/trainingUser';
import { ITrainingUser } from '../../../types/trainingUser';
import { normalizeString } from '../../../utilities/regex';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';
import { columns } from './columns';
import './styles.scss';

const ListPrizeReport = () => {
  const { id } = useParams();
  const [search, setSearch] = useState<string>();
  const [total, setTotal] = useState(0);

  const { data, isLoading } = useFetch<ITrainingUser[]>({
    queryKeyArrWithFilter: ['get-list-prizes-report', id, search],
    moreParams: { id, q: search },
    api: getListOfReportPrizes,
  });

  const dataSource = data?.data?.data?.rows || [];

  useEffect(() => {
    if (data?.data?.data?.total && data?.data?.data?.total > total) {
      setTotal(data?.data?.data?.total);
    }
  }, [data?.data?.data?.total, total]);

  const exportReportPrizesMutation = useMutation({
    mutationFn: () => exportListOfReportPrizes(id),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportReportPrizesMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Bao cao giai thuong.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const debouncedHandleSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        const normalizeSearchTerm = normalizeString(searchTerm);
        setSearch(normalizeSearchTerm || undefined);
      }, 1000),
    [],
  );

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };
  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    setSearch(normalizeSearchTerm || undefined);
  };

  return (
    <div className="wrapper-list-prizes-report">
      <BreadCrumbComponent
        customItems={[
          {
            label: 'Chi tiết sự kiện',
            key: 'Chi tiết sự kiện',
            path: `${TRAINING}/${id}`,
          },
          {
            label: 'Báo cáo giải thưởng',
            key: 'report-prizes',
          },
        ]}
      />
      <div className="header-content">
        <Input.Search onChange={handleChange} onSearch={handleOnSearch} placeholder="Search" allowClear />
        <Button type="default" onClick={handleSubmitExport} loading={exportReportPrizesMutation.isPending}>
          Tải xuống
        </Button>
      </div>
      <div className="table-prizes-report">
        <TableComponent
          queryKeyArr={['get-prizes-report']}
          columns={columns}
          loading={isLoading}
          dataSource={dataSource ?? []}
          rowKey="id"
          title={() => `Tổng số người tham gia quay số (đã điền form thông tin): ${total}`}
        />
      </div>
    </div>
  );
};

export default ListPrizeReport;
