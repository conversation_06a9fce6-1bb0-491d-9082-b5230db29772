import { Col, DatePicker, Form, FormInstance, Input, Row, Switch, Typography } from 'antd';
import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import PercentInput from '../../../components/input/PercentInput';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_TIME } from '../../../constants/common';
import { getOrgChartDropdown } from '../../../service/lead';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { DeliveryConfig, projectAllForDeliveryConfig } from '../../../types/deliveryConfig';
import { TListDropdown } from '../../../types/salesPolicy';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import TimeFrame from './TimeFrame';
import HandoverCategory from './handoverCategory';
import dayjs from 'dayjs';
import { useSubscribeQuery } from '../../../hooks';

const { Item } = Form;
const { RangePicker } = DatePicker;
const { Text } = Typography;

interface IGeneralInfoDeliveryConfig {
  form: FormInstance;
}

const GeneralInfoDeliveryConfig = (props: IGeneralInfoDeliveryConfig) => {
  const { id } = useParams();
  const { form } = props;
  const { openModalCreate, initialValueGen, setIsModified } = useStoreDeliveryConfig();

  const data = useSubscribeQuery<DeliveryConfig>(['detail-delivery-config', id]);
  const dataSource = data?.data?.data;

  const defaultValueOrgCharts = useMemo(
    () => initialValueGen?.orgCharts?.map(item => ({ ...item, value: item?.id, label: item?.name })),
    [initialValueGen?.orgCharts],
  );

  const handleSelectProject = (value: projectAllForDeliveryConfig) => {
    form.setFieldsValue({ project: value ? { name: value?.name, id: value?.id } : undefined });
    setIsModified(true);
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      orgCharts: values
        ? values?.map(item => ({
            id: item?.id,
            name: item?.name,
            code: item?.code,
          }))
        : undefined,
    });
    setIsModified(true);
  };
  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formGeneralInfo"
      onValuesChange={validateForm}
      initialValues={initialValueGen}
    >
      <Row gutter={{ md: 24, lg: 40, xl: 80 }}>
        <Col xl={12} lg={24}>
          <Item name="project" label="Dự án" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
            <SingleSelectLazy
              disabled={!!id}
              apiQuery={sendGetListOfDropdownProjects}
              queryKey={['list-projects']}
              enabled={openModalCreate || !!id}
              placeholder="Chọn dự án"
              keysLabel={['name']}
              handleSelect={handleSelectProject}
              defaultValues={
                initialValueGen?.project && {
                  value: initialValueGen?.project?.id,
                  label: initialValueGen?.project?.name,
                }
              }
            />
          </Item>
          <Item
            name="expectedDateRange"
            label="Thời gian bàn giao (dự kiến)"
            rules={[{ required: true, message: 'Vui lòng chọn thời gian bàn giao (dự kiến)' }]}
          >
            <RangePicker format={FORMAT_DATE} />
          </Item>
          <Item
            name="status"
            layout="horizontal"
            labelCol={{ span: 10 }}
            labelAlign="left"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch />
          </Item>

          <h3>Thông tin bàn giao</h3>
          <Item
            name="orgCharts"
            label="Đơn vị thực hiện"
            rules={[{ required: true, message: 'Vui lòng chọn đơn vị thực hiện' }]}
          >
            <MultiSelectLazy
              apiQuery={getOrgChartDropdown}
              queryKey={['orgChart-exchanges']}
              enabled={openModalCreate || !!id}
              keysTag={'name'}
              keysLabel={'name'}
              placeholder="Chọn đơn vị thực hiện"
              handleListSelect={handleSelectListPos}
              defaultValues={defaultValueOrgCharts}
            />
          </Item>
          <Item
            name="paymentPercent"
            label="Phần trăm thanh toán yêu cầu"
            rules={[{ required: true, message: 'Vui lòng nhập phần trăm thanh toán yêu cầu', whitespace: true }]}
          >
            <PercentInput suffix="%" placeholder="Nhập phần trăm thanh toán" max={100} />
          </Item>
          <Item
            name="hotline"
            label="Hotline"
            rules={[{ required: true, message: 'Vui lòng nhập hotline bàn giao', whitespace: true }]}
          >
            <Input placeholder="Nhập hotline bàn giao" onKeyDown={handleKeyDownEnterNumber} maxLength={13} />
          </Item>
          <Item
            name="accountingConfirm"
            layout="horizontal"
            labelCol={{ span: 10 }}
            labelAlign="left"
            label="Kế toán xác nhận bàn giao"
            valuePropName="checked"
          >
            <Switch />
          </Item>
          <TimeFrame />
          <HandoverCategory />
        </Col>
        {!!id && (
          <Col sm={12} xs={24}>
            <Row gutter={24}>
              <Col lg={6} xs={8}>
                <Text disabled>Ngày cập nhật: </Text>
              </Col>
              <Col lg={18} xs={16}>
                <Text disabled>
                  {dataSource?.updatedDate && dayjs(dataSource?.updatedDate).format(FORMAT_DATE_TIME)}
                  &nbsp;&nbsp;
                  {`${dataSource?.updatedBy?.userName || ''} - ${dataSource?.updatedBy?.name}`}
                </Text>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col lg={6} xs={8}>
                <Text disabled>Ngày tạo: </Text>
              </Col>
              <Col lg={18} xs={16}>
                <Text disabled>
                  {dataSource?.createdDate && dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}
                  &nbsp;&nbsp;
                  {`${dataSource?.createdBy?.userName || ''} - ${dataSource?.createdBy?.name}`}
                </Text>
              </Col>
            </Row>
          </Col>
        )}
      </Row>
    </Form>
  );
};

export default GeneralInfoDeliveryConfig;
