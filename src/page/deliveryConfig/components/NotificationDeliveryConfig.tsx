import { Col, Form, Input, Row } from 'antd';
import { FormInstance } from 'antd/lib';
import { useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import RichEditor from '../../../components/richEditor';
import { validateEmail, validateMultipleEmails } from '../../../utilities/shareFunc';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import './styles.scss';

const { Item } = Form;

interface INotificationDeliveryConfig {
  form: FormInstance;
}

const NotificationDeliveryConfig = (props: INotificationDeliveryConfig) => {
  const { form } = props;
  const [emailContent, setEmailContent] = useState('');
  const quillRef = useRef<ReactQuill>(null);
  const { initialValueNoti, setIsModified } = useStoreDeliveryConfig();

  const handleQuillChange = (value: string) => {
    setEmailContent(value);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formNotification"
      initialValues={initialValueNoti}
      onValuesChange={validateForm}
    >
      <Row gutter={[16, 16]}>
        <Col xl={12} lg={24}>
          <Item
            name="emailTitle"
            label="Tiêu đề email"
            rules={[{ required: true, message: 'Vui lòng nhập tiêu đề email', whitespace: true }]}
          >
            <Input placeholder="Nhập tiêu đề email" maxLength={200} />
          </Item>

          <Item
            name="emailFrom"
            label="Email gửi đi"
            rules={[
              { required: true, message: 'Vui lòng nhập email gửi đi', whitespace: true },
              {
                validator: (_, value) => validateEmail(value),
              },
            ]}
          >
            <Input placeholder="Nhập email gửi đi" maxLength={50} />
          </Item>

          <Item
            name="emailCC"
            label="Email CC"
            rules={[
              {
                validator: (_, value) => validateMultipleEmails(value),
              },
            ]}
          >
            <Input placeholder="Email CC nhiều email cách nhau dấu;" maxLength={500} />
          </Item>

          <Item
            name="emailBCC"
            label="Email BCC"
            rules={[
              { required: true, message: 'Vui lòng nhập Email BCC', whitespace: true },
              {
                validator: (_, value) => validateMultipleEmails(value),
              },
            ]}
          >
            <Input placeholder="Email BCC nhiều email cách nhau dấu;" maxLength={500} />
          </Item>

          <Item
            name="emailTemplate"
            label="Email thông báo"
            required
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  const strippedValue = value?.replace(/<(.|\n)*?>/g, '').trim();
                  if (!strippedValue) {
                    return Promise.reject('Vui lòng nhập nội dung email');
                  }
                  return Promise.resolve();
                },
                whitespace: true,
              },
            ]}
          >
            <RichEditor
              placeholder="Nhập nội dung email"
              onChange={handleQuillChange}
              value={emailContent}
              ref={quillRef}
            />
          </Item>

          <Item
            name="smsBrandName"
            label="SMS Brandname"
            rules={[{ required: true, message: 'Vui lòng nhập SMS Brandname', whitespace: true }]}
          >
            <Input placeholder="Nhập SMS Brandname" maxLength={50} />
          </Item>

          <Item
            name="smsTemplate"
            label="SMS thông báo"
            rules={[{ required: true, message: 'Vui lòng nhập SMS thông báo', whitespace: true }]}
          >
            <Input.TextArea rows={3} maxLength={500} placeholder="Nhập SMS thông báo" />
          </Item>
        </Col>
      </Row>
    </Form>
  );
};

export default NotificationDeliveryConfig;
