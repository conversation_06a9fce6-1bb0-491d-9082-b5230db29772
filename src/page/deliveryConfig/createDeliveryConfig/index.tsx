import { Button, Form, Tabs, TabsProps } from 'antd';
import dayjs from 'dayjs';
import ModalComponent from '../../../components/modal';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { FORMAT_DATE_API } from '../../../constants/common';
import { useCreateField } from '../../../hooks';
import { postCreateDeliveryConfig } from '../../../service/deliveryConfig';
import GeneralInfoDeliveryConfig from '../components/GeneralInfoDeliveryConfig';
import NotificationDeliveryConfig from '../components/NotificationDeliveryConfig';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import { formatTimeFrameToSubmit, validateForms } from '../utilities';
import { useBeforeUnload } from 'react-router-dom';

interface Props {
  onCancel: () => void;
}

const CreateDeliveryConfig = (props: Props) => {
  const { onCancel } = props;
  const { openModalCreate, setDataHandovers, dataHandovers } = useStoreDeliveryConfig();
  const [formGen] = Form.useForm();
  const [formNoti] = Form.useForm();

  const { mutateAsync, isPending } = useCreateField({
    keyOfListQuery: ['list-delivery-config'],
    apiQuery: postCreateDeliveryConfig,
    isMessageError: false,
  });

  const handleSubmit = async () => {
    try {
      const formResult = await validateForms(formGen, formNoti);
      if (!formResult) return;

      const { valuesGen, valuesNoti } = formResult;

      const formatExpectedDate = {
        expectedStartDate: dayjs(valuesGen?.expectedDateRange?.[0]).format(FORMAT_DATE_API),
        expectedEndDate: dayjs(valuesGen?.expectedDateRange?.[1]).format(FORMAT_DATE_API),
        expectedDateRange: undefined,
      };
      const formatTimeFrames = formatTimeFrameToSubmit(valuesGen?.timeFrames);

      const values = {
        ...valuesGen,
        ...valuesNoti,
        ...formatExpectedDate,
        items: dataHandovers,
        status: valuesGen?.status ? 1 : 2,
        accountingConfirm: valuesGen?.accountingConfirm ? 1 : 2,
        paymentPercent: valuesGen?.paymentPercent && parseFloat(valuesGen?.paymentPercent),
        ...formatTimeFrames,
        timeFrames: undefined,
      };
      const res = await mutateAsync(values);
      if (res?.data?.statusCode === '0') {
        onCancel();
        formGen.resetFields();
        formNoti.resetFields();
        setDataHandovers([]);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const items: TabsProps['items'] = [
    { label: 'Thông tin chung', key: 'general-info', children: <GeneralInfoDeliveryConfig form={formGen} /> },
    { label: 'Thông báo', key: 'notification', children: <NotificationDeliveryConfig form={formNoti} /> },
  ];

  useBeforeUnload(event => {
    if (openModalCreate && (formGen.isFieldsTouched() || formNoti.isFieldsTouched())) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <ModalComponent
      rootClassName="wrapper-create-of-delivery-config"
      open={openModalCreate}
      onCancel={() => {
        if (!formGen.isFieldsTouched() && !formNoti.isFieldsTouched()) {
          onCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: onCancel,
        });
      }}
      afterClose={() => {
        formGen.resetFields();
        formNoti.resetFields();
        setDataHandovers([]);
      }}
      title="Tạo mới thiết lập bàn giao"
      destroyOnClose
      footer={[
        <Button key="submit" type="primary" onClick={handleSubmit} loading={isPending}>
          Lưu
        </Button>,
      ]}
    >
      <Tabs items={items} />
    </ModalComponent>
  );
};

export default CreateDeliveryConfig;
