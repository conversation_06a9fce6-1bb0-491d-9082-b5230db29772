export const handleKeyDownAmountInterger = (event: React.KeyboardEvent<HTMLInputElement>) => {
  const isNumberKey = /^[0-9]$/.test(event.key);
  const isAllowedKey = ['Backspace', 'Tab', 'Delete', 'ArrowLeft', 'ArrowRight'].includes(event.key);
  const inputElement = event.currentTarget;

  // Prevent entering 0 as the first character
  if (inputElement.value === '' && event.key === '0') {
    event.preventDefault();
  }

  // Allow only numbers and control keys
  if (!isNumberKey && !isAllowedKey) {
    event.preventDefault();
  }
};
