import { Input } from 'antd';
import React, { useState } from 'react';

interface InptNameblockProps {
  name: string;
  handleUpdateBlockData: (value: string, id: string, key: string) => void;
  rowId: string;
}

const InptNameblock: React.FC<InptNameblockProps> = ({ name, rowId, handleUpdateBlockData }) => {
  const [value, setValue] = useState(name);
  const [isEnterPressed, setIsEnterPressed] = useState(false); // Flag để kiểm soát onBlur

  const handleOnChange = (value: string) => {
    if (!value.trim()) {
      setValue(name.trim());
      return;
    }
    if (name === value.trim()) {
      return;
    }
    handleUpdateBlockData(value.trim(), rowId, 'name');
  };

  return (
    <div>
      <Input
        disabled={true}
        maxLength={100}
        value={value}
        autoFocus
        onChange={e => setValue(e.target.value)}
        onBlur={e => {
          if (!isEnterPressed) {
            handleOnChange(e.target.value);
          }
          setIsEnterPressed(false); // Reset flag sau khi xử lý xong
        }}
        onPressEnter={e => {
          setIsEnterPressed(true); // Đánh dấu là Enter đã được nhấn
          handleOnChange(e.currentTarget.value);
        }}
        style={{ textAlign: 'center', fontSize: '12px', lineHeight: '20px' }}
      />
    </div>
  );
};
export default InptNameblock;
