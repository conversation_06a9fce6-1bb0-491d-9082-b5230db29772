import React, { useCallback, useState } from 'react';
import { Tag, Input, Modal, Button, Form, InputNumber, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface keyValue {
  id: string;
  name: string;
}
interface EditableTagProps {
  label: string;
  idRow: string;
  data: keyValue[];
  updateMasterBlock: (idRow: string, value: string, label: string) => void;
  updateBlock: (params: any) => void;
  projectId?: string;
  // setIsUpdateState: (value: boolean) => void;
}

const AddBlock: React.FC<EditableTagProps> = ({ label, idRow, updateMasterBlock, data, projectId, updateBlock }) => {
  const [editing, setEditing] = useState(false);
  const [value, setValue] = useState('');
  const [isShowModal, setIsShowModal] = useState(false);

  const [form] = Form.useForm();

  const stopEditing = () => {
    setEditing(false);
    updateMasterBlock(idRow, value.trim(), label);
    if (value) {
      // setIsUpdateState(true);
    }
    setValue('');
  };

  const addFloor = () => {
    if (!data || data.length <= 0) {
      setIsShowModal(true);
    } else {
      setEditing(!editing);
    }
  };

  const handleModalAddFBlock = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          setIsShowModal(!isShowModal);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      form.resetFields();
      setIsShowModal(!isShowModal);
    }
  }, [form, isShowModal]);

  const addBlock = () => {
    console.log('label', label);
    form.validateFields().then(values => {
      const numberData = values.numberData;
      const maxLength = label === 'rooms' ? 5 : 3;
      if (numberData && numberData > 0 && numberData <= 200) {
        let params = {
          projectId: projectId,
          id: idRow,
        } as { projectId: string; id: string; rooms?: string[]; floors?: string[] };
        let data = [];
        for (let i = 1; i <= numberData; i++) {
          data.push(i.toString().padStart(maxLength, '0'));
        }

        if (label === 'rooms') {
          params['rooms'] = data;
        } else {
          params['floors'] = data;
        }
        updateBlock(params);
      }
      form.resetFields();
    });
  };

  const tagName = label === 'rooms' ? `Tạo thêm sản phẩm` : `Tạo thêm tầng / đường`;
  const placeholde = label === 'rooms' ? `Nhập sản phẩm` : `Nhập tên tầng / đường`;
  const maxLength = label === 'rooms' ? 5 : 3;
  return (
    <div>
      <Modal
        title={label === 'rooms' ? 'Số lượng sản phẩm' : 'Số lượng tầng / đường'}
        open={isShowModal}
        onCancel={handleModalAddFBlock}
        // onOk={handleAddBlock}
        footer={[
          <Button
            onClick={() => {
              addBlock();
            }}
            key="submit"
            type="primary"
          >
            {label === 'rooms' ? 'Thêm sản phẩm' : 'Thêm tầng / đường'}
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            // label={label === 'rooms' ? 'Số lượng sản phẩm' : 'Số lượng tầng / đường'}
            name="numberData"
            rules={[
              { required: true, message: `Vui lòng nhập số lượng ${label === 'rooms' ? 'sản phẩm' : 'tầng / đường'}` },
            ]}
          >
            <InputNumber
              placeholder={label === 'rooms' ? 'Nhập số lượng sản phẩm' : 'Nhập số lượng tầng / đường'}
              min={0}
              max={200}
            />
          </Form.Item>
        </Form>
      </Modal>
      {editing ? (
        <Input
          value={value}
          onChange={e => setValue(e.target.value)}
          onBlur={stopEditing}
          onPressEnter={stopEditing}
          placeholder={placeholde}
          autoFocus
          style={{ width: '130px', height: '22px', marginRight: '8px' }}
          maxLength={maxLength}
        />
      ) : (
        <Tooltip title={tagName}>
          <Tag
            icon={<PlusOutlined />}
            onClick={() => addFloor()}
            style={{
              cursor: 'pointer',
              width: '130px',
              height: '22px',
              border: '1px dashed #d9d9d9',
              background: 'transparent',
              padding: '0 11px',
              textAlign: 'center',
              marginInlineEnd: '0',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              marginTop: '8px',
            }}
            className="site-tag-plus"
          >
            {tagName}
          </Tag>
        </Tooltip>
      )}
    </div>
  );
};
export default AddBlock;
