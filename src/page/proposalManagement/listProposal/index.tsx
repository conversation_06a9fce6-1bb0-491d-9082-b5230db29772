import { Button, Flex, TableColumnsType } from 'antd';
import { useMemo } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useFetch } from '../../../hooks';

import { columns } from './columns';
import './styles.scss';
import { PROPOSAL } from '../../../configs/path';
import FilterSearch from './components/filterSearch';
import React from 'react';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { deleteProposal, getListProposal } from '../../../service/proposal';
import { IProposal } from '../../../types/proposal';
import CreateModalProposalNoneTemplate from '../createProposalNoneTemplate';

function ListProposals() {
  const {
    data: listProposals,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IProposal[]>({
    queryKeyArrWithFilter: ['get-list-proposals'],
    api: getListProposal,
  });

  const [currentRecord, setCurrentRecord] = React.useState<IProposal>();
  const [isOpenModalDelete, setIsOpenModalDelete] = React.useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = React.useState<boolean>(false);

  const toggleModal = () => setIsModalVisible(!isModalVisible);

  const columnActions: TableColumnsType<IProposal> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: IProposal) => {
          const openViewDetail = () => {
            window.open(`${PROPOSAL}/${record?.id}`, '_blank', 'noopener noreferrer');
          };
          const handleDeleteUnitPartner = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              handleViewDetail={openViewDetail}
              handleDelete={record?.status === 'NEW' ? handleDeleteUnitPartner : undefined}
            />
          );
        },
      },
    ];
  }, [isOpenModalDelete]);
  return (
    <div className="wrapper-list-proposals">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          <Button type="primary" onClick={toggleModal}>
            Thêm mới
          </Button>
        </Flex>
      </div>
      <div className="table-business-proposals">
        <TableComponent
          queryKeyArr={['get-list-proposals']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listProposals?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ConfirmDeleteModal
        label="tờ trình"
        open={isOpenModalDelete}
        apiQuery={deleteProposal as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-list-proposals']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá tờ trình"
        maxLength={255}
        description="Vui lòng nhập lý do muốn xoá tờ trình"
      />
      <CreateModalProposalNoneTemplate
        keyQuery={['get-list-proposals']}
        visible={isModalVisible}
        onClose={toggleModal}
      />
    </div>
  );
}

export default ListProposals;
