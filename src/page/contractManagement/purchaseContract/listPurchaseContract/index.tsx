import { useMemo, useState } from 'react';
import TableComponent from '../../../../components/table';
import { useFetch, useUpdateField } from '../../../../hooks';
import { softDelete, getListContract, sendSAP } from '../../../../service/contract';
import { TListPurchaseContract } from '../../../../types/contract/purchaseContract.ts';
import { columns } from './columns';
import { Button, Col, Flex, Row, TableColumnsType, Typography } from 'antd';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import FilterPurchaseContract from './FilterPurchaseContract';
import ConfirmDeleteModal from '../../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import CreatePurchaseContract from '../createPurchaseContract';
import './styles.scss';
import useFilter from '../../../../hooks/filter';

const PurchaseContract = () => {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListPurchaseContract>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const [filterParams, setFilterParams] = useState<Record<string, unknown>>({});
  const [filter] = useFilter();

  const combinedFilter = useMemo(
    () => ({
      ...filterParams,
      type: filter.type || 'purchase,rent',
      page: filter.page || '1',
      pageSize: filter.pageSize || '10',
    }),
    [filterParams, filter.type, filter.page, filter.pageSize],
  );
  const { data, isFetching } = useFetch<TListPurchaseContract[]>({
    queryKeyArrWithFilter: ['list-purchase-contract', combinedFilter],
    defaultFilter: combinedFilter,
    api: getListContract,
    moreParams: combinedFilter,
  });

  const handleFilterChange = (newFilterParams: Record<string, unknown>) => {
    setFilterParams(newFilterParams);
  };

  const dataPurchaseContract = data?.data?.data?.rows || [];

  const sendDataSAP = useUpdateField({
    keyOfListQuery: ['list-deposit-contract'],
    apiQuery: sendSAP,
    isMessageError: false,
    messageSuccess: 'gửi thông tin SAP thành công',
  });

  const handleSendSAP = async (id: string) => {
    await sendDataSAP.mutateAsync({ id });
  };
  const actionsColumns: TableColumnsType<TListPurchaseContract> = useMemo(() => {
    return [
      ...columns,
      {
        title: '',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        width: '100px',
        render: (_, record) => {
          const handleDeletePurchaseContract = (): void => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };
          if (record.status === 'init') {
            return (
              <Typography.Link
                style={{ color: '#FF3B30' }}
                onClick={() => {
                  handleDeletePurchaseContract();
                }}
              >
                Xoá
              </Typography.Link>
            );
          }
          if (record?.sapCode === '' && (record.status === 'accountant_waiting' || record.status === 'approved')) {
            return (
              <Typography.Link
                style={{ color: '' }}
                onClick={() => {
                  handleSendSAP(record?.id as string);
                }}
              >
                Gửi SAP
              </Typography.Link>
            );
          }
        },
      },
    ];
  }, []);

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-purchase-contract">
      <BreadCrumbComponent />
      <div style={{ marginBottom: 16 }}>
        <Flex gap={17} justify="space-between">
          <FilterPurchaseContract onFilterChange={handleFilterChange} />

          <Row gutter={[16, 16]}>
            <Col>
              <Button onClick={() => setIsOpenModalCreate(true)}>Tạo mới hợp đồng thuê/ mua bán</Button>
            </Col>
            <Col>
              <Button onClick={() => {}}>Tải về</Button>
            </Col>
          </Row>
        </Flex>
      </div>
      <TableComponent
        queryKeyArr={['list-purchase-contract', combinedFilter]}
        loading={isFetching}
        className="table-purchase-contract"
        columns={actionsColumns}
        dataSource={dataPurchaseContract || []}
        rowKey={'id'}
        isPagination={true}
        defaultFilter={combinedFilter}
      />
      <ConfirmDeleteModal
        label="Hợp đồng thuê/ mua bán"
        open={isOpenModalDelete}
        apiQuery={softDelete as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-purchase-contract']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá hợp đồng thuê/ mua bán"
        description="Vui lòng nhập lý do muốn xoá "
      />
      <CreatePurchaseContract onClose={handleCancelModalCreate} visible={isOpenModalCreate} />
    </div>
  );
};

export default PurchaseContract;
