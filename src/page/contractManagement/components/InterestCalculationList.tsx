import React, { useState } from 'react';
import { Col, FormInstance, Row, Space, Switch, TableColumnsType, Typography } from 'antd';
import TableComponent from '../../../components/table';
import { TDepositContract, TInterestCalculation } from '../../../types/contract/depositContract';
import {
  EAPP_STATUS_COLOR,
  EAPP_STATUS_NAME,
  FORMAT_DATE,
  OPTIONS_STATUS_INTEREST_CALCULATION,
  OPTIONS_TYPE_PROPOSAL_INTEREST_CALCULATION,
} from '../../../constants/common';
import dayjs from 'dayjs';
import ModalDetailInterestCalculation from '../depositContracts/components/ModalDetailInterestCalculation';
import ModalConfirmCreateProposal from '../depositContracts/components/ModalConfirmCreateProposal';
import CreateModalProposalNoneTemplate from '../../proposalManagement/createProposalNoneTemplate';
import { TCreateProposal } from '../../../types/proposal';
import { useParams } from 'react-router-dom';

const { Text } = Typography;

interface InterestCalculationListProps {
  interestCalculationList: TInterestCalculation[];
  isLoadingTable?: boolean;
  formContract?: FormInstance;
  dataDetailContract?:
    | (TDepositContract & {
        id: string | undefined;
        message?: string;
        statusCode?: string;
        page?: number;
        pageSize?: number;
        rows?: TDepositContract | undefined;
        total?: number;
        totalPages?: number;
      })
    | undefined;
}

const InterestCalculationList: React.FC<InterestCalculationListProps> = ({
  interestCalculationList,
  isLoadingTable,
  formContract,
  dataDetailContract,
}) => {
  const { id } = useParams();

  const [currentRecord, setCurrentRecord] = useState<TInterestCalculation>();
  const [isOpenDetailInterestCalculation, setIsOpenDetailInterestCalculation] = useState<boolean>(false);

  const [isOpenModalCreateDebtWaiver, setIsOpenModalCreateDebtWaiver] = useState<boolean>(false);
  const [isOpenModalCreateDebtReinstatement, setIsOpenModalCreateDebtReinstatement] = useState<boolean>(false);

  const [isOpenModalCreateProposal, setIsOpenModalCreateProposal] = useState<boolean>(false);

  const [isChecked, setIsChecked] = useState<boolean>(false);

  const [initValueCreateProposal, setInitValueCreateProposal] = useState<TCreateProposal>();

  const toggleOpenDetailInterestCalculation = () =>
    setIsOpenDetailInterestCalculation(!isOpenDetailInterestCalculation);

  const toggleOpenModalCreateDebtWaiver = () => setIsOpenModalCreateDebtWaiver(!isOpenModalCreateDebtWaiver);
  const toggleOpenModalCreateDebtReinstatement = () =>
    setIsOpenModalCreateDebtReinstatement(!isOpenModalCreateDebtReinstatement);
  const toggleOpenModalCreateProposal = () => setIsOpenModalCreateProposal(!isOpenModalCreateProposal);

  const handleOpenModalCreateProposal = (proposalType: string) => {
    setInitValueCreateProposal({ type: proposalType, contract: { ...formContract?.getFieldsValue(), id: id } });
    toggleOpenModalCreateProposal();
  };

  const handleAfterSubmitProposal = () => {
    if (isOpenModalCreateDebtWaiver) toggleOpenModalCreateDebtWaiver();
    else if (isOpenModalCreateDebtReinstatement) toggleOpenModalCreateDebtReinstatement();
  };

  const handleCancelProposal = () => {
    if (isOpenModalCreateDebtWaiver) {
      toggleOpenModalCreateDebtWaiver();
      setIsChecked(!isChecked);
    } else if (isOpenModalCreateDebtReinstatement) {
      toggleOpenModalCreateDebtReinstatement();
      setIsChecked(!isChecked);
    }
  };

  // Cột cho bảng nhân viên
  const InterestCalculationListColumns: TableColumnsType = [
    {
      title: 'Mã phiếu công nợ',
      dataIndex: 'code',
      key: 'code',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Tên phiếu công nợ',
      dataIndex: 'title',
      key: 'title',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Loại nợ',
      dataIndex: 'debtType',
      key: 'debtType',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (value: string) => {
        return (
          <span>
            {value ? OPTIONS_STATUS_INTEREST_CALCULATION?.filter(item => item?.value === value)[0]?.label : '-'}
          </span>
        );
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'delayFrom',
      key: 'delayFrom',
      render: (value: string) => <span>{value ? dayjs(value)?.format(FORMAT_DATE) : '-'}</span>,
    },
    {
      title: 'Ngày duyệt',
      dataIndex: 'delayTo',
      key: 'delayTo',
      render: (value: string) => <span>{value ? dayjs(value)?.format(FORMAT_DATE) : '-'}</span>,
    },

    {
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 115,
      render: (_, record: TInterestCalculation) => (
        <a
          onClick={() => {
            setCurrentRecord(record);
            toggleOpenDetailInterestCalculation();
          }}
        >
          Xem chi tiết
        </a>
      ),
    },
  ];

  return (
    <>
      <Row gutter={{ md: 24 }}>
        <Col xs={24} md={12}>
          <Space size={16} style={{ marginBottom: '16px' }}>
            <Text>{isChecked ? 'Trạng thái miễn trừ công nợ' : 'Trạng thái phục hồi tính công nợ'}</Text>
            <Switch
              value={isChecked}
              onChange={checked => {
                setIsChecked(checked);
                if (checked) {
                  toggleOpenModalCreateDebtWaiver();
                  // do something when activated
                } else {
                  toggleOpenModalCreateDebtReinstatement();
                }
              }}
            />
            <span style={{ color: EAPP_STATUS_COLOR[dataDetailContract?.eapStatus as string] }}>
              {EAPP_STATUS_NAME[dataDetailContract?.eapStatus as string]}
            </span>
          </Space>
        </Col>
        <Col xs={24} md={24}>
          <TableComponent
            dataSource={interestCalculationList}
            columns={InterestCalculationListColumns}
            queryKeyArr={['']}
            loading={isLoadingTable}
            rowKey={'stt'}
            isPagination={false}
          />
        </Col>
      </Row>
      <ModalDetailInterestCalculation
        visible={isOpenDetailInterestCalculation}
        data={currentRecord}
        isChecked={isChecked}
        onClose={toggleOpenDetailInterestCalculation}
      />
      <ModalConfirmCreateProposal
        title="Tạo tờ trình miễn công nợ"
        description="Xác nhận tạo tờ trình miễn công nợ"
        onCancel={() => {
          setIsChecked(!isChecked);
          toggleOpenModalCreateDebtWaiver();
        }}
        open={isOpenModalCreateDebtWaiver}
        onConfirm={handleOpenModalCreateProposal}
        typeProposal="PROPOSAL_FOR_EXCLUDING_CONTRACT_FROM_DEBT_LIST"
      />
      <ModalConfirmCreateProposal
        title="Tạo tờ trình phục hồi tính công nợ"
        description="Xác nhận tạo tờ trình phục hồi tính công nợ"
        onCancel={() => {
          setIsChecked(!isChecked);
          toggleOpenModalCreateDebtReinstatement();
        }}
        open={isOpenModalCreateDebtReinstatement}
        onConfirm={handleOpenModalCreateProposal}
        typeProposal="PROPOSAL_FOR_REINSTATING_CONTRACT_INTO_DEBT_LIST"
      />
      <CreateModalProposalNoneTemplate
        keyQuery={['get-list-proposals']}
        visible={isOpenModalCreateProposal}
        onClose={toggleOpenModalCreateProposal}
        initialValues={initValueCreateProposal}
        listProposalTypeInterestCalculation={OPTIONS_TYPE_PROPOSAL_INTEREST_CALCULATION}
        handleAfterSubmitProposal={handleAfterSubmitProposal}
        handleCancelProposal={handleCancelProposal}
      />
    </>
  );
};

export default InterestCalculationList;
