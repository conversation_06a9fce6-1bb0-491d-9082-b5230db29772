import React from 'react';
import { Form, Select, InputNumber, Row, Col } from 'antd';
import { useFetch } from '../../../hooks';
import { TLoanBankInfo } from '../../../types/contract/depositContract';
import { getBanks } from '../../../service/bank';
import { FormInstance, Input } from 'antd/lib';
import { formatNumber } from '../../../utilities/regex';

interface LoanInfoFormProps {
  form: FormInstance;
  disabledForm?: boolean;
}

const LoanInfoForm: React.FC<LoanInfoFormProps> = ({ form, disabledForm = false }) => {
  const { data: dataBanks, isLoading } = useFetch<TLoanBankInfo[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
    moreParams: { branchIsActvie: true },
  });
  const listBank = dataBanks?.data?.data || [];

  return (
    <div style={{ background: '#********', padding: 16, border: '1px solid #********' }}>
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} md={11}>
          <Form.Item
            label="Ngân hàng"
            name="loanBankCode"
            rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
          >
            <Select
              disabled={disabledForm}
              options={listBank.map(item => ({
                value: item.bankCode,
                label: item.bankName,
              }))}
              loading={isLoading}
              placeholder="Chọn ngân hàng"
              onChange={bankCode => {
                const bankObj = listBank.find(b => b.bankCode === bankCode);
                if (bankObj) {
                  form.setFieldsValue({
                    loanBankInfo: {
                      bankCode: bankObj.bankCode,
                      bankName: bankObj.bankName,
                      id: bankObj?.id || '',
                      branchName: bankObj?.branchName || '',
                      houseBank: bankObj?.houseBank || '',
                    },
                  });
                } else {
                  form.setFieldsValue({ loanBankInfo: undefined });
                }
              }}
            />
          </Form.Item>
        </Col>
        <Form.Item name="loanBankInfo" style={{ display: 'none' }}>
          <Input type="hidden" />
        </Form.Item>
        <Col xs={12} md={5}>
          <Form.Item
            label="Thời hạn vay"
            name="loanTermYear"
            rules={[{ required: true, message: 'Vui lòng nhập thời hạn vay' }]}
          >
            <InputNumber
              min={0.1}
              max={100}
              disabled={disabledForm}
              maxLength={4}
              precision={1}
              formatter={value => `${value}`}
              style={{ width: '100%' }}
              suffix={<span style={{ color: '#********' }}>năm</span>}
            />
          </Form.Item>
        </Col>
        <Col xs={12} md={8}>
          <Form.Item
            label="Số tiền vay"
            name="loanAmount"
            rules={[{ required: true, message: 'Vui lòng nhập số tiền vay' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={formatNumber}
              maxLength={15}
              disabled={disabledForm}
              suffix={<span style={{ color: '#********' }}>VNĐ</span>}
            />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default LoanInfoForm;
