import { <PERSON>, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch } from '../../../hooks';
import { cancelOffer, getDetailOffer } from '../../../service/offer';
import { DetailOfferMoneyAccountancy } from '../../../types/offer';
import { Button, Col, Row, Spin, TableColumnsType, Typography } from 'antd';
import { PaperClipOutlined } from '@ant-design/icons';
import './styles.scss';
import TableComponent from '../../../components/table';
import { formatCurrency, getFullAddress } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';
import {
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  PAYMENT_METHOD_NAME,
  STATUS_COLORS,
  STATUS_LABELS,
} from '../../../constants/common';
import { OFFER_COLLECT_MONEY_MANAGEMENT } from '../../../configs/path';
import { formatNumber } from '../../../utilities/regex';
import { useCallback, useEffect, useRef, useState } from 'react';
import ConfirmActionModal from '../../../components/modal/specials/ConfirmActionModal';
import { MutationFunction, useQueryClient } from '@tanstack/react-query';

const { Title, Text } = Typography;
const DetailOfferMoney = () => {
  const { id } = useParams();
  const topRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const [isCancelOfferModal, setCancelOfferModal] = useState<boolean>(false);

  const { data: offerOrderData, isRefetching } = useFetch<DetailOfferMoneyAccountancy>({
    api: () => id && getDetailOffer({ id }),
    queryKeyArr: ['get-detail-offer-payment', id],
    enabled: !!id,
    cacheTime: 10,
  });

  useEffect(() => {
    if (isRefetching) {
      topRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [isRefetching]);

  const handleOfferCodeClick = useCallback(
    (recordId: string, event?: React.MouseEvent) => {
      if (recordId === id) {
        event?.preventDefault();

        queryClient.invalidateQueries({
          queryKey: ['get-detail-offer', id],
        });

        topRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    },
    [id, queryClient],
  );

  const offerOrder = offerOrderData?.data?.data;

  const columns: TableColumnsType<DetailOfferMoneyAccountancy> = [
    {
      title: 'Dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      render: (_, record: DetailOfferMoneyAccountancy) => <Text>{record?.propertyTicket?.project?.name || ''}</Text>,
    },
    {
      title: 'Mã YCĐCHO/ YCĐCO',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      render: (_, record: DetailOfferMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.ticketType === 'YCDCH'
            ? record?.propertyTicket?.bookingTicketCode
            : record?.propertyTicket?.escrowTicketCode || ''}
        </Text>
      ),
    },

    {
      title: 'Số sản phẩm',
      dataIndex: ['propertyTicket', 'propertyUnit', 'code'],
      key: 'propertyUnitCode',
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (value: string, record?: DetailOfferMoneyAccountancy) => (
        <Link
          to={`${OFFER_COLLECT_MONEY_MANAGEMENT}/${record?.id}`}
          onClick={e => handleOfferCodeClick(record?.id || '', e)}
        >
          {value || ''}
        </Link>
      ),
    },

    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 250,
      render: (value: number) => <Text>{formatCurrency(value.toString()) || ''}</Text>,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '10%',
      render: (status: string) => (
        <span style={{ color: STATUS_COLORS[status] || 'black' }}>{STATUS_LABELS[status] || 'Không xác định'}</span>
      ),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE) || ''}</Text>,
    },
  ];

  return (
    <>
      <Spin spinning={isRefetching}>
        <div ref={topRef}>
          <BreadCrumbComponent titleBread={offerOrder?.code} />
          <Row
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
              padding: '20px 0px',
            }}
          >
            <Title level={5}>Phiếu thu {offerOrder?.code}</Title>
            {offerOrder?.status === 'WAITING_TRANSFER' && (
              <Button onClick={() => setCancelOfferModal(true)}>Hủy phiếu</Button>
            )}
          </Row>
          <Title level={5} style={{ marginBottom: '16px', marginTop: '30px' }}>
            Thông tin chi tiết
          </Title>

          <Row>
            <Col span={12}>
              <Row gutter={[16, 16]}>
                <Col span={8}>Mã đề nghị:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.code || ''}
                </Col>
                <Col span={8}>Mã khách hàng:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.propertyTicket?.customer?.code || ''}
                </Col>
                <Col span={8}>Tên khách hàng:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.propertyTicket?.customer?.type === 'business'
                    ? offerOrder?.propertyTicket?.customer?.company?.name
                    : offerOrder?.propertyTicket?.customer?.personalInfo?.name || ''}
                </Col>
                <Col span={8}>Số giấy tờ:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.propertyTicket?.customer?.identityNumber || ''}
                </Col>
                <Col span={8}>Địa chỉ liên lạc:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.propertyTicket?.customer?.type === 'individual'
                    ? getFullAddress(offerOrder?.propertyTicket?.customer?.info?.rootAddress)
                    : getFullAddress(offerOrder?.propertyTicket?.customer?.company?.address) || ''}
                </Col>
                <Col span={8}>Số tiền:</Col>
                <Col span={16} className="info-value">
                  {formatNumber(offerOrder?.money) || ''}
                </Col>
                <Col span={8}>hình thức thanh toán:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.state ? PAYMENT_METHOD_NAME[offerOrder?.state] : ''}
                </Col>

                <Col span={8}>Ngày tạo đề nghị thu tiền:</Col>
                <Col span={16} className="info-value">
                  {dayjs(offerOrder?.createdAt).format(FORMAT_DATE_TIME) || ''}
                </Col>
                <Col span={8}>Ngày nộp tiền:</Col>
                <Col span={16} className="info-value">
                  {dayjs(offerOrder?.receiptDate).format(FORMAT_DATE_TIME) || ''}
                </Col>
                <Col span={8}>Ghi chú:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.description || ''}
                </Col>
                <Col span={8}>Ghi chú phiếu YC:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.propertyTicket?.note || ''}
                </Col>
                <Col span={8}>Lý do thanh toán:</Col>
                <Col span={16} className="info-value">
                  {offerOrder?.reason || ''}
                </Col>

                <Col span={8}>Phí giao dịch:</Col>
                <Col span={16} className="info-value">
                  {formatNumber(offerOrder?.transactionFee) || ''}
                </Col>
                <Col span={8}>Thông tin đính kèm:</Col>
                <Col span={16} className="info-value">
                  {Array.isArray(offerOrder?.files) ? (
                    <>
                      {offerOrder?.files?.map((file, index) => (
                        <div key={index}>
                          {file?.url ? (
                            <a
                              href={`${import.meta.env.VITE_S3_IMAGE_URL}/${file.url}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              style={{ color: '#1890ff' }}
                              download={file?.name}
                            >
                              <PaperClipOutlined />
                              {file.name}
                            </a>
                          ) : (
                            <span style={{ color: '#1890ff' }}>{file?.name}</span>
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    ''
                  )}
                </Col>
              </Row>
            </Col>
          </Row>

          <Title level={5} style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px' }}>
            Thông tin chung
          </Title>

          <TableComponent
            className="table-offer-order"
            columns={columns}
            queryKeyArr={['get-offer-order']}
            dataSource={offerOrder?.relatedTransactions}
            rowKey={'id'}
            isPagination={false}
          />
          <Row
            style={{
              backgroundColor: 'rgba(230, 244, 255, 1)',
              padding: '12px 8px',
              borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
              marginTop: '30px',
            }}
          >
            <Col span={8} style={{ textAlign: 'center' }}>
              <span>
                Số tiền: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPendingAmount)}</span>
              </span>
            </Col>
            <Col span={8} style={{ textAlign: 'center' }}>
              <span>
                Đã thanh toán: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPaidAmount)}</span>
              </span>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <span>
                Còn lại: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalRemainingAmount)}</span>
              </span>
            </Col>
          </Row>
        </div>
      </Spin>
      <ConfirmActionModal
        open={isCancelOfferModal}
        apiQuery={cancelOffer as MutationFunction<unknown, unknown>}
        keyOfDetailQuery={['get-offer-order']}
        onCancel={() => setCancelOfferModal(false)}
        title="Hủy đề nghị thu tiền"
        description="Vui lòng nhập lý do hủy đề nghị thu tiền này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: id }}
        showReasonField={true}
      />
    </>
  );
};

export default DetailOfferMoney;
