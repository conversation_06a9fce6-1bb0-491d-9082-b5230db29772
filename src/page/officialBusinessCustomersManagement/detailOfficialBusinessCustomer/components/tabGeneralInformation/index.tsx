import { Checkbox, Col, DatePicker, Form, Input, Row, Select, SelectProps, Typography } from 'antd';
import { FormProps } from 'antd/lib';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import dayjs from 'dayjs';
import React from 'react';
import { useParams } from 'react-router-dom';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import CurrencyInput from '../../../../../components/input/CurrencyInput';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import { FORMAT_DATE, FORMAT_DATE_TIME, REGEX_EMAIL } from '../../../../../constants/common';
import { PERMISSION_CUSTOMER } from '../../../../../constants/permissions/customer';
import { FetchResponse, useCheckPermissions, useUpdateField } from '../../../../../hooks';
import { handleErrors } from '../../../../../service/error/errorsService';
import { updateAllOfficialCustomer, updateCommonOfficialCustomer } from '../../../../../service/officialCustomers';
import { ICustomers, TDataDuplicate, TIdentities } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import ModalCheckDuplicate from '../../../listOfficialBusinessCustomer/components/modalCheckDuplicate';
import BankInfo from '../bankInfo';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;

type Props = {
  dataDetail?: FetchResponse<ICustomers> | undefined;
  dataProvinces?: FetchResponse<AddressType[]> | undefined;
  yearOnly?: boolean;
  isModified?: boolean;
  setIsModified: React.Dispatch<React.SetStateAction<boolean>>;
  setYearOnly: React.Dispatch<React.SetStateAction<boolean>>;
};

const OPTIONS_IDENTITIES: SelectProps['options'] = [
  {
    label: 'Giấy phép kinh doanh',
    value: 'GPKD',
  },
];

function TabGeneralInformation({ dataDetail, dataProvinces, yearOnly, isModified, setIsModified, setYearOnly }: Props) {
  const { id } = useParams();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);

  const { customerUpdateAll, customerUpdateCommon } = useCheckPermissions(PERMISSION_CUSTOMER);

  const [Provinces, setProvinces] = React.useState<SelectProps['options']>([]);
  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = React.useState(false);
  const [dataDuplicate, setDataDuplicate] = React.useState<{ duplicates: TDataDuplicate[] }>();
  const [dataSubmit, setDataSubmit] = React.useState<ICustomers>();
  const [initialValues, setInitialValues] = React.useState<ICustomers>();
  // const [valuesShareEmail, setValuesShareEmail] = React.useState<OptionType[]>();
  const dataOfficailCustomer = dataDetail?.data;

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: customerUpdateAll ? updateAllOfficialCustomer : updateCommonOfficialCustomer,
    keyOfListQuery: ['get-official-customers-business'],
    keyOfDetailQuery: ['get-detail-official-customer-business', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  // Lấy danh sách tỉnh thành
  React.useEffect(() => {
    if (dataProvinces?.data?.data) {
      const provinces = dataProvinces?.data?.data.map(province => ({
        label: province.nameVN,
        value: province.code,
      }));
      setProvinces(provinces);
    }
  }, [dataProvinces?.data?.data]);

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCloneAddress = React.useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  React.useEffect(() => {
    if (dataOfficailCustomer) {
      const initialData = {
        ...dataOfficailCustomer,
        bankInfo: Array.isArray(dataOfficailCustomer?.bankInfo) ? dataOfficailCustomer.bankInfo : [],
        info: {
          ...dataOfficailCustomer.info,
          birthday: dataOfficailCustomer?.info?.birthday
            ? dayjs(dataOfficailCustomer.info.birthday, FORMAT_DATE)
            : null,
          birthdayYear: dataOfficailCustomer?.info?.birthdayYear
            ? dayjs(dataOfficailCustomer.info.birthdayYear, 'YYYY')
            : null,
          onlyYear: dataOfficailCustomer?.info?.birthdayYear ? true : false,
        },
        company: {
          ...dataOfficailCustomer?.company,
          issueDate: dataOfficailCustomer?.company?.issueDate
            ? dayjs(dataOfficailCustomer?.company?.issueDate, FORMAT_DATE)
            : null,
        },
        personalInfo: {
          ...dataOfficailCustomer.personalInfo,
          income: dataOfficailCustomer?.personalInfo?.income
            ? dataOfficailCustomer?.personalInfo?.income?.toString()
            : undefined,
          identities: dataOfficailCustomer?.personalInfo?.identities?.map((item: TIdentities) => ({
            ...item,
            date: item.date ? dayjs(item.date, FORMAT_DATE) : null,
          })),
        },
        identities: {
          ...dataOfficailCustomer?.identities,
          type: 'GPKD',
          date: dataOfficailCustomer?.identities?.date
            ? dayjs(dataOfficailCustomer?.identities?.date, FORMAT_DATE)
            : null,
        },
        address: {
          ...dataOfficailCustomer?.info?.address,
        },
        rootAddress: {
          ...dataOfficailCustomer?.info?.rootAddress,
        },
      };
      setInitialValues(initialData as ICustomers);
      setIsModified(false);
      form.setFieldsValue(initialData);
      setYearOnly(!!dataOfficailCustomer.info?.birthdayYear);
      // setValuesShareEmail(dataOfficailCustomer?.share?.emails?.map(item => ({ label: item, value: item })) || []);
    }
  }, [dataOfficailCustomer, form, setIsModified, setYearOnly]);

  // const handleChangeListShare = (val: OptionType[]) => {
  //   setValuesShareEmail(val);
  //   setIsModified(true);
  // };

  React.useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  const onFinish: FormProps['onFinish'] = async (values: ICustomers) => {
    const checkChangePhone = form.isFieldTouched(['personalInfo', 'phone']);
    const newData = {
      id: id,
      companyName: values?.company?.name,
      company: {
        ...values?.company,
        issueDate: dayjs.isDayjs(values?.company?.issueDate) ? values?.company?.issueDate.format(FORMAT_DATE) : null,
      },
      taxCode: values?.taxCode,
      bankInfo: values?.bankInfo?.map(item => ({
        ...item,
        name: item?.bank?.label,
        branchCode: item?.bank?.branchCode,
        code: item?.bank?.code,
        bank: undefined,
      })),
      info: {
        ...values?.info,
        birthdayYear:
          typeof values?.info?.birthdayYear !== 'string' ? values?.info?.birthdayYear?.format('YYYY') : null,
        birthday: typeof values?.info?.birthday !== 'string' ? values?.info?.birthday?.format(FORMAT_DATE) : null,
        address: values?.address,
        rootAddress: values?.rootAddress,
      },
      identities: {
        type: values?.identities?.type,
        value: values?.identities?.value,
        date: typeof values?.identities?.date !== 'string' ? values?.identities?.date?.format(FORMAT_DATE) : null,
        place: values?.identities?.place,
      },
      takeNote: values?.takeNote,
      personalInfo: {
        // identities: values?.identities,
        email: values?.personalInfo?.email,
        position: values?.personalInfo?.position,
        phone: values?.personalInfo?.phone,
        job: values?.personalInfo?.job,
        incomeSource: values?.personalInfo?.incomeSource,
        income:
          typeof values?.personalInfo?.income === 'string'
            ? parseInt(values?.personalInfo?.income)
            : values?.personalInfo?.income,
        relationshipStatus: values?.personalInfo?.relationshipStatus,
        shortName: values?.personalInfo?.shortName,
        name: values?.personalInfo?.name,
      },
      type: 'business',
    }; //updateAll
    const continueUpdate = !(
      checkChangePhone && dataOfficailCustomer?.personalInfo?.phone !== values?.personalInfo?.phone
    );
    const response = await mutateAsync({ ...newData, continueUpdate });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        case 'CUS0007':
          setIsOpenCheckDuplicate(true);
          setDataDuplicate(response?.data?.data as { duplicates: TDataDuplicate[] });
          setDataSubmit(newData);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  React.useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <>
      {initialValues ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}></Col>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item label="Mã số khách hàng" name="code" required>
                    <Input placeholder="Mã số khách hàng" disabled />
                  </Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Item
                    label="Tên công ty"
                    name={['company', 'name']}
                    required
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập tên công ty',
                      },
                    ]}
                  >
                    <Input
                      placeholder="Tên công ty"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled={customerUpdateAll ? !customerUpdateAll : customerUpdateCommon}
                    />
                  </Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item label="Tên ngắn" name={['company', 'shortName']}>
                    <Input maxLength={25} placeholder="Nhập tên ngắn" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Mã Business Partner" name="bpID">
                    <Input placeholder="Mã Business Partner" disabled />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    label="Mã số thuế"
                    rules={[{ required: true, message: 'Vui lòng nhập mã số thuế' }]}
                    name="taxCode"
                  >
                    <Input maxLength={15} placeholder="Nhập mã số thuế" disabled={!customerUpdateAll} />
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-birthday">
                  <Item label="Ngày cấp mã số thuế" name={['company', 'issueDate']}>
                    <DatePicker format={FORMAT_DATE} placeholder="dd/mm/yyyy" disabled={!customerUpdateAll} />
                  </Item>
                </Col>
                {/*Cần check lại trường và singleList*/}
                <Col xs={24} sm={12}>
                  <Item label="Nơi cấp mã số thuế" name={['company', 'issueLocation']}>
                    <Input placeholder="Nhập nơi cấp mã số thuế" disabled={!customerUpdateAll} />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    label="Người đại diện"
                    name={['personalInfo', 'name']}
                    required
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập tên người đại diện',
                      },
                    ]}
                  >
                    <Input
                      placeholder="Người đại diện"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled={!customerUpdateAll}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Chức vụ" name={['personalInfo', 'position']}>
                    <Input placeholder="Chức vụ" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'phone']}
                    label="Số điện thoại"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số điện thoại',
                      },
                      {
                        pattern: /^0\d*$/,
                        message: 'Số điện thoại phải bắt đầu bằng số 0 và chỉ chứa chữ số!',
                      },
                    ]}
                  >
                    <Input
                      maxLength={15}
                      onKeyDown={handleKeyDownEnterNumber}
                      placeholder="Nhập số điện thoại"
                      disabled={!customerUpdateAll}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ pattern: REGEX_EMAIL, message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input maxLength={25} placeholder="Nhập địa chỉ email" />
                  </Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['info', 'onlyYear']}
                    label="Ngày sinh"
                    layout="horizontal"
                    labelCol={{ span: 10 }}
                    labelAlign="left"
                    valuePropName="checked"
                  >
                    <Checkbox
                      checked={yearOnly}
                      style={{ marginLeft: '4px' }}
                      onChange={e => setYearOnly(e.target.checked)}
                    >
                      Chỉ năm sinh
                    </Checkbox>
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-birthday">
                  {yearOnly ? (
                    <Item name={['info', 'birthdayYear']}>
                      <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                    </Item>
                  ) : (
                    <Item name={['info', 'birthday']}>
                      <DatePicker format={FORMAT_DATE} placeholder="dd/mm/yyyy" />
                    </Item>
                  )}
                </Col>
              </Row>
              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item label="Loại giấy tờ" name={['identities', 'type']}>
                    <Select
                      placeholder="Chọn loại giấy tờ"
                      options={OPTIONS_IDENTITIES}
                      disabled={!customerUpdateAll}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Số giấy tờ" name={['identities', 'value']}>
                    <Input maxLength={60} placeholder="Nhập số giấy tờ" disabled={!customerUpdateAll} />
                  </Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Item label="Ngày cấp giấy tờ" name={['identities', 'date']}>
                    <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} disabled={!customerUpdateAll} />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nơi cấp" name={['identities', 'place']}>
                    <Select
                      placeholder="Chọn nơi cấp"
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label as string).toLowerCase().includes(input.toLowerCase())
                      }
                      options={Provinces}
                      disabled={!customerUpdateAll}
                    />
                  </Item>
                </Col>
              </Row>

              <Title level={5}>Thông tin khác</Title>
              <Row gutter={24}>
                {/* <p style={{ marginBottom: 8 }}> Tài khoản thanh toán</p> */}
                <BankInfo />
                <Col xs={24} sm={12}>
                  <Item label="Ngành nghề" name={['personalInfo', 'job']}>
                    <Input maxLength={50} placeholder="Nhập ngành nghề" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nguồn thu nhập" name={['personalInfo', 'incomeSource']}>
                    <Input placeholder="VD: công việc hành chính" maxLength={255} />
                  </Item>
                </Col>{' '}
                <Col xs={24} sm={24}>
                  <Item label="Thu nhập / tháng (VNĐ)" name={['personalInfo', 'income']}>
                    <CurrencyInput placeholder="Nhập khoảng thu nhập" />
                  </Item>
                </Col>
                {/* <Col xs={24} sm={24}>
                  <Item name={['share', 'email']} label="Chia sẻ thông tin khách hàng với người khác">
                    <SelectEmployees
                      handleChangeListShare={handleChangeListShare}
                      defaultValues={initialValues?.share?.emails}
                    />
                  </Item>{' '}
                </Col> */}
              </Row>

              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
              <Item label="Địa chỉ" name="rootAddress">
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'rootAddress'}
                  address={rootAddress}
                  handleAddressChange={validateForm}
                  // isDisable={!customerUpdateAll}
                />
              </Item>
              <Item name={['rootAddress', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>

              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
              <Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
              </Item>
              <Item label="Địa chỉ" name={'address'}>
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'address'}
                  address={initialValues?.address}
                  handleAddressChange={validateForm}
                  // isDisable={!customerUpdateAll}
                />
              </Item>
              <Item name={['address', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Col xs={24} md={24}>
                <Title level={5}>Địa chỉ công ty</Title>
                <Item label="Địa chỉ" name={'address'}>
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={initialValues?.address}
                    handleAddressChange={validateForm}
                    isDisable={!customerUpdateAll}
                  />
                </Item>
                <Item name={['address', 'address']}>
                  <Input placeholder="Địa chỉ cụ thể" disabled={!customerUpdateAll} />
                </Item>{' '}
              </Col>
              <Col xs={24} md={24}>
                <Title level={5} style={{ marginBottom: '35px' }}>
                  Ghi chú
                </Title>
                <Item name="takeNote">
                  <TextArea rows={5} maxLength={500} placeholder="Nhập ghi chú nhanh" />
                </Item>
                <div className="info">
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày cập nhật: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataOfficailCustomer?.data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataOfficailCustomer?.data?.updatedByObj?.username || ''} - ${dataOfficailCustomer?.data?.updatedByObj?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày tạo: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataOfficailCustomer?.data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataOfficailCustomer?.data?.createdByObj?.username || ''} - ${dataOfficailCustomer?.data?.createdByObj?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Col>
          </Row>
        </Form>
      ) : (
        <></>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
      <ModalCheckDuplicate
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as ICustomers}
        removeDataSubmit={removeDataSubmit}
        type="update"
        setIsModified={setIsModified}
      />
    </>
  );
}
export default TabGeneralInformation;
