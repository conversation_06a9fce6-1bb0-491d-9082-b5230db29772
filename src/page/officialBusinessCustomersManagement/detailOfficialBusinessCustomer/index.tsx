import { Form, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import DocumentComponent from '../../../components/document';
import { AddressType } from '../../../components/selectAddress';
import { useFetch } from '../../../hooks';
import { getProvinces } from '../../../service/address';
import {
  addCustomerDocument,
  addItemsCustomerDocument,
  deleteCustomerDocument,
  deleteItemCustomerDocument,
  getCustomerDocument,
  getDocumentCustomerItems,
  updateCustomerDocument,
} from '../../../service/customers';
import { getDetailOfficialCustomer } from '../../../service/officialCustomers';
import { ICustomers, IDocumentCustomer } from '../../../types/customers';
import TabGeneralInformation from './components/tabGeneralInformation';
import TabTransactionInformation from './components/tabTransactionInformation';
import './styles.scss';

const DetailPersonalCustomer = () => {
  const { id } = useParams();
  const [form] = Form.useForm();

  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);

  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-province'],
    api: getProvinces,
  });

  const { data: dataDetail } = useFetch<ICustomers>({
    api: () => id && getDetailOfficialCustomer(id),
    queryKeyArr: ['get-detail-official-customer-business', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const dataOfficailCustomer = dataDetail?.data?.data;

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const itemsTabOfficialCustomerBusiness = [
    {
      key: '1',
      label: 'Thông tin khách hàng',
      children: (
        <TabGeneralInformation
          dataDetail={dataDetail}
          dataProvinces={dataProvinces}
          yearOnly={yearOnly}
          isModified={isModified}
          setIsModified={setIsModified}
          setYearOnly={setYearOnly}
        />
      ),
    },
    {
      key: '2',
      label: 'Tài liệu',
      children: (
        <DocumentComponent<IDocumentCustomer>
          // modelId={id}
          keyQueryList={'getDocument'}
          keyQueryItems={'getDocumentItems'}
          modelIdFieldName={'customer'}
          getDocument={getCustomerDocument}
          getDocumentItems={getDocumentCustomerItems}
          addDocument={addCustomerDocument}
          updateDocument={updateCustomerDocument}
          addItemsDocument={addItemsCustomerDocument}
          deleteDocument={deleteCustomerDocument}
          deleteItemDocument={deleteItemCustomerDocument}
        />
      ),
    },
    { key: '3', label: 'Thông tin giao dịch', children: <TabTransactionInformation /> },
  ];

  return (
    <div className="wrapper-detail-personal-customer">
      <BreadCrumbComponent titleBread={dataOfficailCustomer?.company?.name} />
      <Tabs defaultActiveKey="1" onChange={() => {}} items={itemsTabOfficialCustomerBusiness} />
    </div>
  );
};

export default DetailPersonalCustomer;
