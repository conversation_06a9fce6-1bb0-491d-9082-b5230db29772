import { Col, Form, FormInstance, Input, Row, Switch } from 'antd';
import { useMemo } from 'react';
import { useParams } from 'react-router-dom';

import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { getOrgChartDropdown } from '../../../../service/lead';
import { sendGetListOfDropdownProjects } from '../../../../service/salesPolicy';
import { projectAllForOwnershipCertificateConfig } from '../../../../types/ownershipCertificateConfig';
import { TListDropdown } from '../../../../types/salesPolicy';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { useStoreOwnershipCertificateConfig } from '../../storeOwnershipCertificateConfig';
import HandoverCategory from './handoverCategory';
import TimeFrame from './TimeFrame';

const { Item } = Form;

interface IGeneralTab {
  form: FormInstance;
}

const GeneralTab = (props: IGeneralTab) => {
  const { id } = useParams();
  const { form } = props;
  const { openModalCreate, initialValueGen, setIsModified } = useStoreOwnershipCertificateConfig();

  const isActive = Form.useWatch('isActive', form);

  const defaultValueOrgCharts = useMemo(
    () => initialValueGen?.orgCharts?.map(item => ({ ...item, value: item?.id, label: item?.name })),
    [initialValueGen?.orgCharts],
  );

  const handleSelectProject = (value: projectAllForOwnershipCertificateConfig) => {
    form.setFieldsValue({ project: { name: value?.name, id: value?.id } });
    setIsModified(true);
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      orgCharts: values?.map(item => ({
        id: item?.id,
        name: item?.name,
        code: item?.code,
      })),
    });
    setIsModified(true);
  };
  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formGeneralInfo"
      onValuesChange={validateForm}
      initialValues={initialValueGen}
    >
      <Row gutter={[16, 16]}>
        <Col xl={12} lg={24}>
          <Item name="project" label="Dự án" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
            <SingleSelectLazy
              disabled={!!id}
              apiQuery={sendGetListOfDropdownProjects}
              queryKey={['list-projects']}
              enabled={openModalCreate || !!id}
              placeholder="Chọn dự án"
              keysLabel={['name']}
              handleSelect={handleSelectProject}
              defaultValues={
                initialValueGen?.project && {
                  value: initialValueGen?.project?.id,
                  label: initialValueGen?.project?.name,
                }
              }
            />
          </Item>

          <Item
            name="isActive"
            layout="horizontal"
            labelCol={{ span: 10 }}
            labelAlign="left"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch value={isActive} />
          </Item>

          <h3>Thông tin bàn giao</h3>
          <Item
            name="orgCharts"
            label="Đơn vị thực hiện"
            rules={[{ required: true, message: 'Vui lòng chọn đơn vị thực hiện' }]}
          >
            <MultiSelectLazy
              apiQuery={getOrgChartDropdown}
              queryKey={['orgChart-exchanges']}
              enabled={openModalCreate || !!id}
              keysTag={'name'}
              keysLabel={'name'}
              placeholder="Chọn đơn vị thực hiện"
              handleListSelect={handleSelectListPos}
              defaultValues={defaultValueOrgCharts}
            />
          </Item>

          <Item
            name="hotline"
            label="Hotline"
            rules={[{ required: true, message: 'Vui lòng nhập hotline bàn giao', whitespace: true }]}
          >
            <Input placeholder="Nhập hotline bàn giao" onKeyDown={handleKeyDownEnterNumber} maxLength={13} />
          </Item>

          <TimeFrame />
          <HandoverCategory />
        </Col>
      </Row>
    </Form>
  );
};

export default GeneralTab;
