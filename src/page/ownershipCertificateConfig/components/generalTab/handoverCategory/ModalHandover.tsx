import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Modal, Row } from 'antd';
import { v4 as uuIdv4 } from 'uuid';
import { useEffect } from 'react';
import { useStoreOwnershipCertificateConfig } from '../../../storeOwnershipCertificateConfig';
import { THandover } from '../../../../../types/ownershipCertificateConfig';

interface IModalHandover {
  open: boolean;
  onCancel: () => void;
  defaultValue?: THandover;
  dataCategory?: THandover[];
  setDataCategory?: (data: THandover[]) => void;
}

const ModalHandover = (props: IModalHandover) => {
  const { open, onCancel, defaultValue, dataCategory, setDataCategory } = props;
  const [form] = Form.useForm();

  const { setIsModified } = useStoreOwnershipCertificateConfig();

  useEffect(() => {
    form.setFieldsValue({ items: defaultValue });
  }, [defaultValue, form]);

  const handleFinish = (values: { items?: THandover }) => {
    if (!values?.items) return;

    const newList = values.items?.list?.filter(item => item.title && item.description);

    if (defaultValue?.id && values?.items?.name) {
      const updatedItems =
        dataCategory?.map(item =>
          item.id === defaultValue.id && values.items
            ? {
                ...values.items,
                name: values.items?.name,
                id: defaultValue?.id,
                list: newList,
              }
            : item,
        ) || [];
      setDataCategory && setDataCategory(updatedItems);
    } else {
      setDataCategory && setDataCategory([...(dataCategory || []), { ...values.items, id: uuIdv4(), list: newList }]);
    }

    setIsModified(true);
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      className="modal-handover"
      open={open}
      title="Thêm hạng mục"
      width={700}
      onCancel={onCancel}
      afterClose={() => form.resetFields()}
      maskClosable={false}
      footer={
        <div>
          <Button type="primary" onClick={form.submit}>
            Lưu
          </Button>
        </div>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Form.Item
          label="Tên hạng mục"
          name={['items', 'name']}
          rules={[{ required: true, message: 'Vui lòng nhập tên hạng mục', whitespace: true }]}
        >
          <Input placeholder="Nhập tên hạng mục" maxLength={50} />
        </Form.Item>
        <Form.Item label="Chi tiết hạng mục" style={{ margin: 0 }} layout="horizontal" />

        <Form.List name={['items', 'list']} initialValue={[{}]}>
          {(fields, { add, remove }) => (
            <>
              <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />} style={{ marginBottom: 16 }} block>
                Thêm hạng mục
              </Button>
              {fields.map(({ key, name, ...restField }) => {
                return (
                  <Row gutter={12} key={key} justify="space-between" className="row-handover">
                    <Form.Item noStyle shouldUpdate>
                      {() => {
                        const row = form.getFieldValue(['items', 'list', name]) || {};
                        const { title, description } = row;

                        const hasAnyValue = title || description;
                        const hasMissingValue = hasAnyValue && (!title || !description);

                        const validationError = hasMissingValue ? 'Vui lòng nhập đủ 2 giá trị' : '';

                        return (
                          <>
                            <Col span={11}>
                              <Form.Item
                                {...restField}
                                name={[name, 'title']}
                                validateStatus={hasMissingValue ? 'error' : ''}
                                help={hasMissingValue ? validationError : ''}
                              >
                                <Input placeholder="Nhập nội dung" maxLength={50} />
                              </Form.Item>
                            </Col>
                            <Col span={11}>
                              <Form.Item
                                {...restField}
                                name={[name, 'description']}
                                validateStatus={hasMissingValue ? 'error' : ''}
                              >
                                <Input placeholder="Nhập mô tả" maxLength={50} />
                              </Form.Item>
                            </Col>
                            <Col span={2} className="delete-handover">
                              <DeleteOutlined onClick={() => remove(name)} style={{ color: 'red' }} />
                            </Col>
                          </>
                        );
                      }}
                    </Form.Item>
                  </Row>
                );
              })}
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default ModalHandover;
