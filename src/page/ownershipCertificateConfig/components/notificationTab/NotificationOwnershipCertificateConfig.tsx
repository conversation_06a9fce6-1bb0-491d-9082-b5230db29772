import { Col, Form, Input, Row } from 'antd';
import { useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import RichEditor from '../../../../components/richEditor';
import { validateEmail, validateMultipleEmails } from '../../../../utilities/shareFunc';

const { Item } = Form;

interface INotificationOwnershipCertificateConfig {
  fieldName: string;
}

const NotificationOwnershipCertificateConfig = (props: INotificationOwnershipCertificateConfig) => {
  const { fieldName } = props;
  const [emailContent, setEmailContent] = useState('');
  const quillRef = useRef<ReactQuill>(null);

  const handleQuillChange = (value: string) => {
    setEmailContent(value);
  };

  return (
    <Row gutter={[16, 16]}>
      <Col xl={12} lg={24}>
        <Item
          name={[fieldName, 'emailTitle']}
          label="Tiêu đề email"
          rules={[{ required: true, message: 'Vui lòng nhập tiêu đề email', whitespace: true }]}
        >
          <Input placeholder="Nhập tiêu đề email" maxLength={200} />
        </Item>

        <Item
          name={[fieldName, 'emailFrom']}
          label="Email gửi đi"
          rules={[
            { required: true, message: 'Vui lòng nhập email gửi đi', whitespace: true },

            {
              validator: (_, value) => validateEmail(value),
            },
          ]}
        >
          <Input placeholder="Nhập email gửi đi" maxLength={50} />
        </Item>

        <Item
          name={[fieldName, 'emailCC']}
          label="Email CC"
          rules={[
            {
              validator: (_, value) => validateMultipleEmails(value),
            },
          ]}
        >
          <Input placeholder="Email CC nhiều email cách nhau dấu;" maxLength={50} />
        </Item>

        <Item
          name={[fieldName, 'emailBCC']}
          label="Email BCC"
          rules={[
            {
              validator: (_, value) => validateMultipleEmails(value),
            },
          ]}
        >
          <Input placeholder="Email BCC nhiều email cách nhau dấu;" maxLength={50} />
        </Item>

        <Item
          name={[fieldName, 'emailTemplate']}
          label="Email thông báo"
          required
          rules={[
            {
              required: true,
              validator: (_, value) => {
                if (!value || value === '<p><br></p>') {
                  return Promise.reject('Vui lòng nhập nội dung email');
                }
                return Promise.resolve();
              },
              whitespace: true,
            },
          ]}
        >
          <RichEditor
            placeholder="Nhập nội dung email"
            onChange={handleQuillChange}
            value={emailContent}
            ref={quillRef}
          />
        </Item>

        <Item
          name={[fieldName, 'smsBrandName']}
          label="SMS Brandname"
          rules={[{ required: true, message: 'Vui lòng nhập SMS Brandname', whitespace: true }]}
        >
          <Input placeholder="Nhập SMS Brandname" maxLength={50} />
        </Item>

        <Item
          name={[fieldName, 'smsTemplate']}
          label="SMS thông báo"
          rules={[{ required: true, message: 'Vui lòng nhập SMS thông báo', whitespace: true }]}
        >
          <Input.TextArea rows={3} maxLength={500} placeholder="Nhập SMS thông báo" />
        </Item>
      </Col>
    </Row>
  );
};

export default NotificationOwnershipCertificateConfig;
