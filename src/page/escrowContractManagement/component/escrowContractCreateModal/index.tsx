import { Form, Modal } from 'antd';
import ModalComponent from '../../../../components/modal';
import { useCreateField } from '../../../../hooks';
import { createDepositContract } from '../../../../service/depositContract';
import { useCallback } from 'react';
import EscrowContractForm from '../escrowContractForm';
import { EscrowContract } from '../../../../types/depositContract';

interface EscrowContractCreateModalProps {
  visible: boolean;
  onClose: () => void;
}

const EscrowContractCreateModal = ({ visible, onClose }: EscrowContractCreateModalProps) => {
  const [form] = Form.useForm();

  const { mutateAsync: _createDepositContract, isPending: isPendingCreate } = useCreateField({
    apiQuery: createDepositContract,
    keyOfListQuery: ['get-deposit-contract'],
    isMessageError: false,
    messageSuccess: 'Tạo mới hợp đồng ký quỹ thành công!',
  });

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      onClose();
    }
  }, [form, onClose]);

  const handleCreate = useCallback(
    async (values: EscrowContract) => {
      try {
        const resp = await _createDepositContract(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [_createDepositContract, onClose],
  );
  return (
    <ModalComponent
      className="modal-deposit-contract"
      title="Tạo mới hợp đồng ký quỹ"
      open={visible}
      footer={null}
      onCancel={handleCancel}
      destroyOnClose
    >
      <EscrowContractForm form={form} onFinish={handleCreate} loading={isPendingCreate} />
    </ModalComponent>
  );
};

export default EscrowContractCreateModal;
