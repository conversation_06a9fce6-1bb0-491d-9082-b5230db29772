import { Form, Row } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import useFilter from '../../../../hooks/filter';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { getListProject } from '../../../../service/offer';

type TFilter = {
  projectIds?: string[];
  orgType?: string[];
  depositForm?: string[];
};

function FilterSearch() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();

  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);

  useEffect(() => {
    if (params) {
      setInitialValues({
        projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : [],
        orgType: params.get('orgType') ? params.get('orgType')?.split(',') : [],
        depositForm: params.get('depositForm') ? params.get('depositForm')?.split(',') : [],
      });
    }
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newImportFilter: Record<string, string> = {
      projectIds: values?.projectIds?.join(',') || '',
      orgType: values?.orgType?.join(',') || '',
      depositForm: values?.depositForm?.join(',') || '',
      page: '1',
    };
    setFilter({ ...filter, page: '1', ...newImportFilter });
    setIsOpenFilter(false);
  };
  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  const handleSelectOrgType = (values: unknown) => {
    const newOrgTypeFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ orgType: newOrgTypeFilter });
  };

  const handleSelectDepositForm = (values: unknown) => {
    const newDepositFormFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ depositForm: newDepositFormFilter });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-deposit-contract"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={() => {
          handleSelectOrgType([]);
          handleSelectProject([]);
          handleSelectDepositForm([]);
          const clearedValues = {
            orgType: [],
            projectIds: [],
            depositForm: [],
          };

          setInitialValues(clearedValues);
          form.setFieldsValue(clearedValues);
        }}
        extraFormItems={
          <>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProject}
                keysLabel={['name', 'code']}
                keysTag={['name', 'code']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>
            <Form.Item label="Loại đơn vị" name="orgType">
              <MultiSelectStatic
                data={[
                  { label: 'Đơn vị nội bộ', value: 'INTERNAL' },
                  { label: 'Đơn vị ĐTHT', value: 'EXTERNAL' },
                ]}
                handleListSelect={handleSelectOrgType}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>
            <Form.Item label="Hình thức ký quỹ" name="depositForm">
              <MultiSelectStatic
                data={[
                  { label: 'Ký quỹ thiện chí', value: 'GOODWILL_DEPOSIT' },
                  { label: 'Ký quỹ cam kết', value: 'COMMITTED_DEPOSIT' },
                  { label: 'Không ký quỹ', value: 'NO_DEPOSIT' },
                ]}
                handleListSelect={handleSelectDepositForm}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>

            <Row gutter={16}></Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;
