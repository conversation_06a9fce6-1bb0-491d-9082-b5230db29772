import { Button, Col, Form, Input, Row, Select, Typography } from 'antd';
import React from 'react';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { Bank, BankOption } from '../../../../types/bookingRequest';

const { Title } = Typography;

interface PaymentInfoSectionProps {
  form: any;
  banks: Bank[];
  isLoadingBanks: boolean;
  defaultBankOptions: BankOption[];
  handleRemoveBankAccount: (name: number) => void;
  handleSelectBankInfo: (value: string) => void;
}

const PaymentInfo: React.FC<PaymentInfoSectionProps> = ({
  form,
  banks,
  isLoadingBanks,
  defaultBankOptions,
  handleRemoveBankAccount,
  handleSelectBankInfo,
}) => {
  const containerStyle = {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
    padding: 20,
    margin: 0,
    marginBottom: 16,
  };

  return (
    <>
      <Title level={5}>Thông tin thanh toán</Title>
      <Form.Item label="Tài khoản giao dịch">
        <div style={containerStyle}>
          <Form.List name="bankAccount">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row align="middle" gutter={[8, 8]} key={key}>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, 'bankCode']}
                        label="Ngân hàng"
                        rules={[{ required: false }]}
                      >
                        <Select
                          placeholder="Chọn ngân hàng"
                          allowClear
                          filterOption={(input, option) =>
                            typeof option?.label === 'string'
                              ? option.label.toLowerCase().includes(input.toLowerCase())
                              : false
                          }
                          showSearch
                          loading={isLoadingBanks}
                          options={banks.map(item => ({
                            value: item?.bankCode,
                            label: item?.bankName,
                          }))}
                          onChange={(value, option) => {
                            const currentAccountNumber = form.getFieldValue(['bankAccount', name, 'accountNumber']);
                            const currentBeneciary = form.getFieldValue(['bankAccount', name, 'beneciary']);

                            // Set bankName riêng biệt để lưu tên ngân hàng
                            form.setFieldsValue({
                              bankAccount: {
                                [name]: {
                                  bankName: value ? (Array.isArray(option) ? '' : option?.label || '') : undefined,
                                  // Reset số tài khoản và tên người thụ hưởng khi bỏ chọn ngân hàng
                                  accountNumber: value ? currentAccountNumber || '' : undefined,
                                  beneciary: value ? currentBeneciary || '' : undefined,
                                },
                              },
                            });

                            // Chỉ clear errors khi bỏ chọn ngân hàng, không trigger validation ngay
                            if (!value) {
                              form.setFields([
                                {
                                  name: ['bankAccount', name, 'accountNumber'],
                                  errors: [],
                                },
                                {
                                  name: ['bankAccount', name, 'beneciary'],
                                  errors: [],
                                },
                              ]);
                            }
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => {
                        const prevBankCode = prevValues?.bankAccount?.[name]?.bankCode;
                        const currentBankCode = currentValues?.bankAccount?.[name]?.bankCode;
                        return prevBankCode !== currentBankCode;
                      }}
                    >
                      {() => {
                        const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                        const isDisabled = !bankCode || (typeof bankCode === 'string' && !bankCode.trim());

                        return (
                          <>
                            <Col span={6}>
                              <Form.Item
                                {...restField}
                                name={[name, 'accountNumber']}
                                label="Số tài khoản"
                                dependencies={[['bankAccount', name, 'bankCode']]}
                                validateTrigger={['onBlur', 'onSubmit']}
                                rules={[
                                  {
                                    message: 'Vui lòng nhập số tài khoản',
                                    validator: (_, value) => {
                                      const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                      if (
                                        bankCode &&
                                        typeof bankCode === 'string' &&
                                        bankCode.trim() &&
                                        !value?.trim()
                                      ) {
                                        return Promise.reject('Vui lòng nhập số tài khoản');
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <Input placeholder="Nhập số tài khoản" maxLength={20} disabled={isDisabled} />
                              </Form.Item>
                            </Col>
                            <Col span={7}>
                              <Form.Item
                                {...restField}
                                name={[name, 'beneciary']}
                                label="Tên người thụ hưởng"
                                dependencies={[['bankAccount', name, 'bankCode']]}
                                validateTrigger={['onBlur', 'onSubmit']}
                                rules={[
                                  {
                                    message: 'Vui lòng nhập tên người thụ hưởng',
                                    validator: (_, value) => {
                                      const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                      if (
                                        bankCode &&
                                        typeof bankCode === 'string' &&
                                        bankCode.trim() &&
                                        !value?.trim()
                                      ) {
                                        return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} disabled={isDisabled} />
                              </Form.Item>
                            </Col>
                          </>
                        );
                      }}
                    </Form.Item>
                    <Col span={1}>
                      <CloseOutlined
                        style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                        onClick={() => {
                          remove(name);
                          handleRemoveBankAccount(name);
                        }}
                      />
                    </Col>
                  </Row>
                ))}

                {fields.length < 10 ? (
                  <Col span={23}>
                    <Button
                      type="dashed"
                      onClick={() => {
                        add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                      }}
                      style={{ padding: 0 }}
                      block
                      icon={<PlusOutlined />}
                    >
                      Thêm tài khoản giao dịch
                    </Button>
                  </Col>
                ) : null}
              </>
            )}
          </Form.List>
        </div>
      </Form.Item>
      <p style={{ marginBottom: 8 }}>
        Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
      </p>
      <div style={containerStyle}>
        <Form.Item name="mainBankId" rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}>
          <Select
            placeholder="Chọn tài khoản giao dịch chính"
            options={defaultBankOptions}
            onChange={handleSelectBankInfo}
            showSearch
            optionFilterProp="label"
            filterOption={(input, option) =>
              typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
            }
          />
        </Form.Item>
        {/* Trường ẩn để lưu trữ đối tượng MainBank */}
        <Form.Item name="mainBank" hidden>
          <Input type="hidden" />
        </Form.Item>
      </div>
    </>
  );
};

export default PaymentInfo;
