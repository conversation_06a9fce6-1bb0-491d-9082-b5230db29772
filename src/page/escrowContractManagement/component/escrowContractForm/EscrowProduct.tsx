import React from 'react';
import { Row, Col, Typo<PERSON>, Button } from 'antd';
import TableComponent from '../../../../components/table';
import FilterAddProduct, { FilterValues } from './filterMoreProduct';

const { Title } = Typography;

interface EscrowProductProps {
  selectedProducts: any[];
  setSelectedProducts: React.Dispatch<React.SetStateAction<any[]>>;
  selectedPropertyUnitIds: string[];
  setSelectedPropertyUnitIds: React.Dispatch<React.SetStateAction<string[]>>;
  removedProductInfo: { salesProgramId: string; timestamp: number } | null;
  setRemovedProductInfo: React.Dispatch<React.SetStateAction<{ salesProgramId: string; timestamp: number } | null>>;
}

const EscrowProduct: React.FC<EscrowProductProps> = ({
  selectedProducts,
  setSelectedProducts,
  setSelectedPropertyUnitIds,
  removedProductInfo,
  setRemovedProductInfo,
}) => {
  const handleAddProducts = (values: FilterValues) => {
    const products = values.salesProgram || [];

    setSelectedProducts(prevProducts => {
      const existingIds = new Set(prevProducts.map(p => p.id));
      const newProducts = products.filter(p => !existingIds.has(p.id));
      return [...prevProducts, ...newProducts];
    });

    // Cập nhật selectedPropertyUnitIds với propertyUnitIds từ filter
    if (values.propertyUnitIds && values.propertyUnitIds.length > 0) {
      setSelectedPropertyUnitIds(prevIds => {
        const newIds = values.propertyUnitIds || [];
        const uniqueIds = [...new Set([...prevIds, ...newIds])];
        return uniqueIds;
      });
    }
  };

  const removeProduct = (productId: string) => {
    // Tìm sản phẩm cần xóa để lấy salesProgramId
    const productToRemove = selectedProducts.find(p => p.id === productId);

    // Xóa sản phẩm khỏi danh sách
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));

    // Xóa salesProgramId khỏi propertyUnitIds nếu không còn sản phẩm nào sử dụng
    if (productToRemove?.salesProgramId) {
      setSelectedPropertyUnitIds(prev => {
        // Kiểm tra xem còn sản phẩm nào khác sử dụng salesProgramId này không
        const remainingProducts = selectedProducts.filter(p => p.id !== productId);
        const isStillUsed = remainingProducts.some(p => p.salesProgramId === productToRemove.salesProgramId);

        if (!isStillUsed) {
          // Nếu không còn sản phẩm nào sử dụng, xóa khỏi propertyUnitIds
          const updatedIds = prev.filter(id => id !== productToRemove.salesProgramId);
          return updatedIds;
        }

        return prev;
      });
    }

    // Thông báo cho FilterAddProduct về salesProgramId đã bị xóa
    if (productToRemove?.salesProgramId) {
      setRemovedProductInfo({
        salesProgramId: productToRemove.salesProgramId,
        timestamp: Date.now(),
      });
    }
  };

  const customComponents = {
    header: {
      row: (props: React.HTMLAttributes<HTMLTableRowElement>) => (
        <>
          <tr {...props} />
          <tr>
            <td colSpan={3} className="ant-table-cell-total-product">
              <Typography.Text className="text-total-product">
                Tổng số sản phẩm ký quỹ: {selectedProducts.length}
              </Typography.Text>
            </td>
          </tr>
        </>
      ),
    },
  };

  const columnsEscrowProduct = [
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string) => <div>{code}</div>,
    },
    {
      title: 'Chương trình bán hàng',
      dataIndex: 'name',
      key: 'name',
      width: 360,
      render: (name: string) => <div>{name}</div>,
    },
    {
      title: '',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: (_: string, record: any) => (
        <Button type="link" danger onClick={() => removeProduct(record.id)} size="small">
          Xóa
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Title level={5}>Sản phẩm ký quỹ</Title>
      <Row gutter={16}>
        <Col span={24}>
          <p>
            <FilterAddProduct
              handleSubmit={handleAddProducts}
              initialSearchValues={undefined}
              removedProductInfo={removedProductInfo}
            />
          </p>
          <TableComponent
            queryKeyArr={['get-escrow-product']}
            columns={columnsEscrowProduct}
            dataSource={selectedProducts}
            pagination={false}
            className="unit-conversion-table"
            components={customComponents}
            rowKey="id"
            isPagination={false}
            style={{ marginBottom: 16 }}
          />
        </Col>
      </Row>
    </div>
  );
};

export default EscrowProduct;
