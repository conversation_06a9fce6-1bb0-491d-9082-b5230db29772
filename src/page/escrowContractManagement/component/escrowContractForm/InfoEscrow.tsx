import React from 'react'; //  useRef
import { Form, InputNumber, Select, Row, Col, Input, DatePicker, Typography } from 'antd';
import dayjs from 'dayjs'; // Dayjs
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import {
  // getListDistributionChannel,
  // getListDivision,
  getListDepositProject,
} from '../../../../service/depositContract';

const { Option } = Select;
const { Title } = Typography;
// const { RangePicker } = DatePicker;

interface InfoEscrowProps {
  form: any;
  // depositDates: [Dayjs | null, Dayjs | null];
  // setDepositDates: React.Dispatch<React.SetStateAction<[Dayjs | null, Dayjs | null]>>;
  defaultDistributionChannel: { code: string; name: string } | undefined;
  handleSelectDistributionChannel: (value: any) => void;
  handleSelectDivision: (value: any) => void;
  handleSelectDepositProject: (value: any) => void;
  formatNumber: (value: number | string | undefined) => string;
}

const InfoEscrow: React.FC<InfoEscrowProps> = ({
  form,
  // depositDates,
  // setDepositDates,
  // defaultDistributionChannel,
  // handleSelectDistributionChannel,
  // handleSelectDivision,
  handleSelectDepositProject,
  formatNumber,
}) => {
  // const depositRangePickerRef = useRef<any>(null);

  return (
    <div>
      <Title level={5}>Thông tin ký quỹ</Title>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Mã hợp đồng" name={['depositContract', 'code']}>
            <Input placeholder="Mã hợp đồng" maxLength={15} disabled />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Ngày ký kết" name="depositSignedDate" initialValue={dayjs()}>
            <DatePicker
              style={{ width: '100%' }}
              placeholder="Chọn ngày ký kết"
              format="DD/MM/YYYY"
              defaultValue={form.getFieldValue('depositSignedDate') || dayjs()}
            />
          </Form.Item>
        </Col>
        {/* <Col span={24}>
          <Form.Item
            label="Loại hợp đồng"
            name={'type'}
            initialValue={'ZH05'}
            rules={[{ required: true, message: 'Vui lòng chọn loại hợp đồng' }]}
          >
            <Select placeholder="Chọn loại hợp đồng">
              <Option value="ZH05">ZH05 - SO phí môi giới BĐS</Option>
              <Option value="ZL03">ZL03 - Leasing PMG</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngày hiệu lực hợp đồng"
            name="expiredDate"
            rules={[{ required: true, message: 'Vui lòng chọn ngày hiệu lực hợp đồng' }]}
          >
            <RangePicker
              ref={depositRangePickerRef}
              value={depositDates}
              defaultValue={[dayjs(), null]}
              onChange={dates => {
                setDepositDates(dates as [Dayjs | null, Dayjs | null]);
              }}
              allowClear
              placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
              format="DD/MM/YYYY"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Mã hợp đồng bản cứng"
            name="POnumber"
            rules={[{ required: true, message: 'Vui lòng nhập mã hợp đồng bản cứng' }]}
          >
            <Input placeholder="Mã hợp đồng bản cứng" maxLength={35} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Kênh phân phối"
            name="distributionChannel"
            rules={[{ required: true, message: 'Vui lòng chọn kênh phân phối' }]}
          >
            <SingleSelectLazy
              apiQuery={getListDistributionChannel}
              queryKey={['get-distribution-channel']}
              keysLabel={['code', 'name']}
              placeholder="Chọn kênh phân phối"
              handleSelect={handleSelectDistributionChannel}
              defaultValues={
                defaultDistributionChannel
                  ? {
                      label: `${defaultDistributionChannel?.code} - ${defaultDistributionChannel?.name}`,
                      value: defaultDistributionChannel?.code,
                    }
                  : undefined
              }
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngành hàng"
            name="division"
            rules={[{ required: true, message: 'Vui lòng chọn ngành hàng' }]}
          >
            <SingleSelectLazy
              apiQuery={getListDivision}
              queryKey={['get-division']}
              keysLabel={['code', 'name']}
              handleSelect={handleSelectDivision}
              placeholder="Chọn ngành hàng"
            />
          </Form.Item>
        </Col> */}
        <Col span={12}>
          <Form.Item
            label="Hình thức ký quỹ"
            name="depositForm"
            rules={[{ required: true, message: 'Vui lòng chọn hình thức ký quý' }]}
          >
            <Select
              placeholder="Chọn hình thức ký quỹ"
              onChange={value => {
                // Reset số tiền ký quỹ khi thay đổi hình thức ký quỹ
                if (value === 'NO_DEPOSIT') {
                  form.setFieldValue('depositAmount', undefined);
                }
              }}
            >
              <Option value="NO_DEPOSIT">Không ký quỹ</Option>
              <Option value="GOODWILL_DEPOSIT">Ký quỹ thiện chí</Option>
              <Option value="COMMITTED_DEPOSIT">Ký quỹ cam kết</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Dự án ký quỹ"
            name="projectId"
            rules={[{ required: true, message: 'Vui lòng chọn dự án ký quỹ' }]}
          >
            <SingleSelectLazy
              apiQuery={getListDepositProject}
              queryKey={['get-deposit-project']}
              keysLabel={'name'}
              placeholder="Chọn dự án ký quỹ"
              handleSelect={handleSelectDepositProject}
              moreParams={{ status: '01' }}
              defaultValues={{
                value: form.getFieldValue(['project', 'id']),
                label: form.getFieldValue(['project', 'name']),
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Thời gian ký quỹ (tháng)" name="depositTime">
            <InputNumber
              placeholder="Nhập thời gian ký quỹ"
              style={{ width: '100%' }}
              min={1}
              max={9999}
              precision={0}
              parser={value => {
                if (!value) return '';
                const numericValue = value.replace(/\D/g, '');
                if (numericValue.length > 4) {
                  return parseInt(numericValue.slice(0, 4)) || '';
                }
                return parseInt(numericValue) || '';
              }}
              formatter={formatNumber}
              onKeyDown={e => {
                const allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'];
                if (!/[0-9]/.test(e.key) && !allowedKeys.includes(e.key)) {
                  e.preventDefault();
                }

                const currentValue = e.currentTarget.value;
                if (currentValue && currentValue.length >= 4 && /[0-9]/.test(e.key)) {
                  e.preventDefault();
                }
              }}
              onChange={value => {
                if (value && (value < 1 || value > 9999)) {
                  return;
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Số tiền ký quỹ"
            name="depositAmount"
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const depositForm = getFieldValue('depositForm');

                  // Chỉ validate khi hình thức ký quỹ là "Ký quỹ thiện chí" hoặc "Ký quỹ cam kết"
                  if (depositForm === 'GOODWILL_DEPOSIT' || depositForm === 'COMMITTED_DEPOSIT') {
                    if (!value) {
                      return Promise.reject(new Error('Vui lòng nhập số tiền ký quỹ'));
                    }
                    if (value <= 0) {
                      return Promise.reject(new Error('Số tiền ký quỹ phải lớn hơn 0'));
                    }
                  }

                  return Promise.resolve();
                },
              }),
            ]}
          >
            <InputNumber
              placeholder="Nhập số tiền ký quỹ"
              maxLength={15}
              style={{ width: '100%' }}
              formatter={formatNumber}
            />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default InfoEscrow;
