import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Form } from 'antd';
import './styles.scss';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
import {
  getBlockBySaleProgramId,
  getFloorAndRoomByBlockId,
  getListSaleProgram,
} from '../../../../service/depositContract';

export interface FilterValues {
  propertyUnitIds?: string[];
  statues?: string[];
  blocks?: string[];
  floors?: string[];
  rooms?: string[];
  salesProgram?: SaleProgram[];
  salesProgramIds?: string[];
  initialSearchValues?: FilterValues;
}

interface SaleProgram {
  id: string;
  code: string;
  name: string;
  [key: string]: unknown;
}
interface Block {
  id: string;
  block: string;
  [key: string]: unknown;
}

const FilterAddProduct = ({
  handleSubmit,
  initialSearchValues,
  removedProductInfo,
}: {
  handleSubmit: (values: FilterValues) => void;
  initialSearchValues?: FilterValues;
  removedProductInfo?: { salesProgramId: string; timestamp: number } | null;
}) => {
  const { id: projectId } = useParams<{ id: string }>();

  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [floorOptions, setFloorOptions] = useState<{ value: string; label: string }[]>([]);
  const [roomOptions, setRoomOptions] = useState<{ value: string; label: string }[]>([]);
  const [blockMap, setBlockMap] = useState<Record<string, string>>({});
  const [selectedSalePrograms, setSelectedSalePrograms] = useState<SaleProgram[]>([]);
  const [currentSalesProgramIds, setCurrentSalesProgramIds] = useState<string[]>([]);
  const [componentKey, setComponentKey] = useState(0);
  const salesProgram = Form.useWatch('salesProgram', form);
  const salesProgramIds = Form.useWatch('salesProgramIds', form);

  const [selectedBlockOptions, setSelectedBlockOptions] = useState<
    Array<{
      option: Block;
      value: string;
      label: string;
    }>
  >([]);

  const [blockOptions, setBlockOptions] = useState<{ value: string; label: string; block: string }[]>([]);

  const [blockLoading, setBlockLoading] = useState(false);

  useEffect(() => {
    // Chỉ reset khi không có salesProgramIds
    if (!salesProgramIds || salesProgramIds.length === 0) {
      // Reset block options khi không có CTBH được chọn
      setBlockOptions([]);
      setSelectedBlockOptions([]);
      // Reset các field phụ thuộc
      setFloorOptions([]);
      setRoomOptions([]);
      setBlockMap({});
      form.setFieldsValue({
        blocks: [],
        floors: [],
        rooms: [],
      });
    }
    // Không fetch blocks ở đây nữa vì đã fetch trong handleSelectSalePrograms
  }, [salesProgramIds, form]);

  useEffect(() => {
    if (
      initialSearchValues?.blocks?.length &&
      initialSearchValues?.floors?.length &&
      initialSearchValues?.rooms?.length
    ) {
      setTimeout(() => {
        const currentValues = form.getFieldsValue();
        handleSubmitFilter(currentValues);
      }, 500);
    }
  }, [initialSearchValues, salesProgram]);

  // Xử lý khi có product bị xóa
  useEffect(() => {
    if (removedProductInfo) {
      const currentSalesPrograms = form.getFieldValue('salesProgram') || [];
      const currentSalesProgramIds = form.getFieldValue('salesProgramIds') || [];

      // Kiểm tra xem salesProgramId bị xóa có trong form hiện tại không
      const isRemovedProgramInForm = currentSalesProgramIds.includes(removedProductInfo.salesProgramId);

      if (isRemovedProgramInForm) {
        // Xóa salesProgram và salesProgramId khỏi form
        const updatedSalesPrograms = currentSalesPrograms.filter(
          (sp: any) => sp.id !== removedProductInfo.salesProgramId,
        );
        const updatedSalesProgramIds = currentSalesProgramIds.filter(
          (id: string) => id !== removedProductInfo.salesProgramId,
        );

        // Cập nhật form
        form.setFieldsValue({
          salesProgram: updatedSalesPrograms,
          salesProgramIds: updatedSalesProgramIds,
        });

        // Cập nhật state
        setSelectedSalePrograms(updatedSalesPrograms);
        setCurrentSalesProgramIds(updatedSalesProgramIds);

        // Force re-render component để đảm bảo UI cập nhật
        setComponentKey(prev => prev + 1);

        // Reset các field phụ thuộc nếu không còn salesProgram nào
        if (updatedSalesProgramIds.length === 0) {
          form.setFieldsValue({
            blocks: [],
            floors: [],
            rooms: [],
          });
          setBlockOptions([]);
          setSelectedBlockOptions([]);
          setFloorOptions([]);
          setRoomOptions([]);
          setBlockMap({});
        }
      }
    }
  }, [
    removedProductInfo,
    form,
    setSelectedSalePrograms,
    setCurrentSalesProgramIds,
    setBlockOptions,
    setSelectedBlockOptions,
    setFloorOptions,
    setRoomOptions,
    setBlockMap,
  ]);

  const handleSubmitFilter = (values: FilterValues) => {
    const formattedValues = {
      ...values,
      // Đảm bảo có cả thông tin đầy đủ và IDs của sale programs
      salesProgram: values.salesProgram || selectedSalePrograms,
      salesProgramIds: values.salesProgramIds,
      blocks: values.blocks?.map(blockId => blockMap[blockId] || blockId),
      floors: values.floors?.map(floor => floor.split('-').pop()).filter((floor): floor is string => !!floor),
      rooms: values.rooms?.map(room => room.split('-').pop()).filter((room): room is string => !!room),
      // Tạo propertyUnitIds từ rooms được chọn
      propertyUnitIds: values.rooms?.map(room => room.split('-').pop()).filter((room): room is string => !!room) || [],
    };

    handleSubmit(formattedValues);
    setIsOpenFilter(false);
  };

  const handleSelectSalePrograms = async (values: { value: string; option: SaleProgram }[]) => {
    // Lấy thông tin đầy đủ của sale programs
    const selectedSaleProgramsData = values.map(item => item.option);
    const selectedSaleProgramIds = selectedSaleProgramsData.map(item => item.id);

    // Cập nhật state local
    setSelectedSalePrograms(selectedSaleProgramsData);
    setCurrentSalesProgramIds(selectedSaleProgramIds);

    // Cập nhật form với cả thông tin đầy đủ và IDs
    form.setFieldsValue({
      salesProgram: selectedSaleProgramsData,
      salesProgramIds: selectedSaleProgramIds,
      // Reset các field phụ thuộc khi thay đổi CTBH
      blocks: [],
      floors: [],
      rooms: [],
    });

    // Reset state khi thay đổi CTBH
    setBlockOptions([]);
    setSelectedBlockOptions([]);
    setFloorOptions([]);
    setRoomOptions([]);
    setBlockMap({});

    // Reset currentSalesProgramIds nếu không có selection
    if (selectedSaleProgramIds.length === 0) {
      setCurrentSalesProgramIds([]);
    }

    // Fetch blocks ngay sau khi chọn sale programs
    if (selectedSaleProgramIds.length > 0 && isOpenFilter) {
      setBlockLoading(true);
      try {
        const response = await getBlockBySaleProgramId({
          idSaleProgram: selectedSaleProgramIds,
        });

        const blocks = response.data.data || [];
        const blockOptions = blocks.map((block: Block) => ({
          value: block.id,
          label: block.block,
          block: block.block,
        }));

        setBlockOptions(blockOptions);
      } catch (error) {
        setBlockOptions([]);
      } finally {
        setBlockLoading(false);
      }
    }
  };

  const handleSelectBlockBySaleProgram = async (values: { option: Record<string, unknown>; value: string }[]) => {
    const newValues = values.map(item => item.value); // Block IDs

    const blockLabels: string[] = values.map(item => {
      const blockValue = item.option?.block;
      return typeof blockValue === 'string' ? blockValue : '';
    });

    form.setFieldsValue({
      blocks: newValues,
    });

    const newBlockMap = newValues.reduce(
      (acc, id, index) => {
        acc[id] = blockLabels[index];
        return acc;
      },
      {} as Record<string, string>,
    );
    setBlockMap(newBlockMap);

    if (newValues.length > 0) {
      try {
        const floorOptions: { value: string; label: string }[] = [];
        const roomOptions: { value: string; label: string }[] = [];

        for (const blockId of newValues) {
          const response = await getFloorAndRoomByBlockId({
            saleProgramId:
              currentSalesProgramIds.length > 0
                ? currentSalesProgramIds
                : form.getFieldValue('salesProgramIds') || salesProgramIds || [],
            blockIdsInput: blockId,
          });

          const blockName = newBlockMap[blockId];

          const floors = response.data.data.floors || [];
          const rooms = response.data.data.rooms || [];

          floors.forEach((floor: string) => {
            const floorValue = `${blockName}-${floor}`;
            floorOptions.push({
              value: floorValue,
              label: `Block ${blockName} - Tầng ${floor}`,
            });
          });

          rooms.forEach((room: string) => {
            const roomValue = `${blockName}-${room}`;
            roomOptions.push({
              value: roomValue,
              label: `Block ${blockName} - Phòng ${room}`,
            });
          });
        }

        setFloorOptions(floorOptions);
        setRoomOptions(roomOptions);

        // Lấy giá trị hiện tại của floors và rooms từ form
        const currentFloors = form.getFieldValue('floors') || [];
        const currentRooms = form.getFieldValue('rooms') || [];

        // Lọc lại để chỉ giữ các giá trị hợp lệ
        const validFloors = currentFloors.filter((floor: string) =>
          floorOptions.some(option => option.value === floor),
        );
        const validRooms = currentRooms.filter((room: string) => roomOptions.some(option => option.value === room));

        // Cập nhật form với các giá trị đã lọc
        form.setFieldsValue({
          blocks: newValues,
          floors: validFloors,
          rooms: validRooms,
        });
      } catch (error) {
        setFloorOptions([]);
        setRoomOptions([]);
        form.setFieldsValue({
          blocks: newValues,
          floors: [],
          rooms: [],
        });
      }
    } else {
      setFloorOptions([]);
      setRoomOptions([]);
      setBlockMap({});
      form.setFieldsValue({
        blocks: [],
        floors: [],
        rooms: [],
      });
    }
  };

  const handleSelectFloors = (values: { value: string }[]) => {
    form.setFieldsValue({
      floors: values.map(item => item.value),
    });
  };

  const handleSelectRooms = (values: { value: string }[]) => {
    form.setFieldsValue({ rooms: values.map(item => item.value) });
  };

  const handleClearFilters = () => {
    setFloorOptions([]);
    setRoomOptions([]);
    setBlockMap({});
    setSelectedBlockOptions([]);
    setSelectedSalePrograms([]);
    setCurrentSalesProgramIds([]);
    form.resetFields();
  };

  return (
    <div className="display-icon-search">
      <DropdownFilterSearch
        rootClassName="filter-more-product"
        searchButtonText="Thêm sản phẩm"
        onClearFilters={handleClearFilters}
        placeholder="Lựa chọn lọc nâng cao"
        submitFilter={handleSubmitFilter}
        handleOpenChange={setIsOpenFilter}
        isOpenFilter={isOpenFilter}
        showParams={true}
        form={form}
        isReadOnly={true}
        extraFormItems={
          <>
            <Form.Item label="Chương trình bán hàng" name="salesProgram">
              <MultiSelectLazy
                key={`sales-program-${componentKey}-${removedProductInfo?.timestamp || 0}`} // Force re-render khi có product bị xóa
                enabled={isOpenFilter}
                apiQuery={getListSaleProgram}
                queryKey={['project-sale-program', projectId]}
                keysLabel={['code', 'name']}
                handleListSelect={handleSelectSalePrograms}
                placeholder="Chọn chương trình bán hàng"
                keysTag={['code', 'name']}
                defaultValues={selectedSalePrograms.map(sp => ({
                  value: sp.id,
                  label: `${sp.code} - ${sp.name}`,
                  ...sp,
                }))}
              />
            </Form.Item>

            <Form.Item label="Block" name="blocks">
              <MultiSelectStatic
                data={blockOptions}
                handleListSelect={handleSelectBlockBySaleProgram}
                placeholder={'Chọn block'}
                keysTag={['block']}
                disabled={!salesProgram || salesProgram.length === 0 || blockLoading}
                defaultValues={selectedBlockOptions.map(item => ({
                  value: item.value,
                  label: item.label,
                  block: item.option.block,
                }))}
              />
            </Form.Item>
            <Form.Item label="Tầng" name="floors">
              <MultiSelectStatic
                data={floorOptions}
                handleListSelect={handleSelectFloors}
                placeholder={'Chọn tầng'}
                showSelectAll
                disabled={!Form.useWatch('blocks', form)?.length}
                keysTag={['label', 'value']}
              />
            </Form.Item>
            <Form.Item label="Phòng" name="rooms">
              <MultiSelectStatic
                data={roomOptions}
                handleListSelect={handleSelectRooms}
                placeholder={'Chọn phòng'}
                showSelectAll
                disabled={!Form.useWatch('blocks', form)?.length}
                keysTag={['label', 'value']}
              />
            </Form.Item>
          </>
        }
      />
    </div>
  );
};

export default FilterAddProduct;
