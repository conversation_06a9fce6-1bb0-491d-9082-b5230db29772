import React from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import EscrowContractForm from '../component/escrowContractForm';
import { getDetailDepositContract } from '../../../service/depositContract';
import { useFetch } from '../../../hooks';
import { EscrowContract } from '../../../types/depositContract';
import { useParams } from 'react-router-dom';
import './styles.scss';

const EscrowContractDetail: React.FC = () => {
  const { id } = useParams();

  const { data } = useFetch<EscrowContract>({
    queryKeyArr: ['get-detail-deposit-contract'],
    api: () => id && getDetailDepositContract(id),
    enabled: !!id,
    cacheTime: 10,
  });

  console.log(data);
  return (
    <div className="deposit-contract-detail-page">
      <BreadCrumbComponent />
      <EscrowContractForm initialValues={data?.data?.data} isViewMode={true} />
    </div>
  );
};

export default EscrowContractDetail;
