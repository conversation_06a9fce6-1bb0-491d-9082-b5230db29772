import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>ert, Typo<PERSON>, Modal } from 'antd';
import ModalComponent from '../../../../../components/modal';
import TableComponent from '../../../../../components/table';
import { DuplicateCustomer, DuplicateResp } from '../../../../../types/bookingRequest';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { PROPOSAL_DOCUMENT } from '../../../../../configs/path';
import { useParams } from 'react-router-dom';
import { useSellProgramStore } from '../../../../Store';
import { useProjectStore } from '../../../store';

const { Text } = Typography;

interface DuplicateModalProps {
  visible: boolean;
  duplicateData: DuplicateResp | null;
  onConfirm: (selectedCustomer: DuplicateCustomer | null) => void;
  onCancel: () => void;
}

const DuplicateModal: React.FC<DuplicateModalProps> = ({ visible, onConfirm, onCancel, duplicateData }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<DuplicateCustomer | null>(null);
  const [showTakeCareModal, setShowTakeCareModal] = useState(false);
  const { id } = useParams<{ id: string }>();
  const SetProjectId = useSellProgramStore(state => state.SetProjectId);
  const { setSelectedCustomerStore } = useProjectStore();

  useEffect(() => {
    if (duplicateData?.data?.length) {
      const defaultSelected = duplicateData.data[0]; // Chọn bản ghi đầu tiên
      setSelectedRowKeys([defaultSelected.id || defaultSelected.identityNo || '']);
      setSelectedCustomer(defaultSelected);
    }
  }, [duplicateData]);

  const columns = [
    {
      title: 'Khách hàng',
      dataIndex: 'personInfo',
      key: 'personInfo',
      width: 160,
      render: (_: unknown, record: DuplicateCustomer) => {
        return <Text>{record?.type === 'individual' ? record?.personalInfo?.name : record?.company?.name || ''}</Text>;
      },
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'code',
      key: 'code',
      width: 140,
      render: (value: string) => {
        return <Text>{value || '-'}</Text>;
      },
    },
    {
      title: 'Số giấy tờ',
      dataIndex: 'identityNo',
      key: 'identityNo',
      width: 120,
      render: (_: unknown, record: DuplicateCustomer) => {
        return (
          <Text>
            {record?.type === 'individual' ? record?.personalInfo?.identities?.[0]?.value : record?.taxCode || '-'}
          </Text>
        );
      },
    },
    {
      title: 'Loại khách hàng',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (value: string) => {
        return <Text>{value === 'individual' ? 'Cá nhân' : 'Doanh nghiệp'}</Text>;
      },
    },
    {
      title: 'Đối tượng',
      dataIndex: 'customerType',
      key: 'customerType',
      width: 171,
      render: (value: string) => {
        return <Text>{value === 'customer' ? 'Khách hàng chính thức' : 'Khách hàng tiềm năng'}</Text>;
      },
    },
  ];

  const rowSelection = {
    type: 'radio' as const,
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: DuplicateCustomer[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedCustomer(selectedRows[0] || null);
    },
  };
  const handleContinue = () => {
    if (!selectedCustomer) return;

    if (selectedCustomer.isTakeCare === false && selectedCustomer?.customerType === 'customer') {
      setShowTakeCareModal(true);
    } else {
      onConfirm(selectedCustomer);
    }
  };

  const handleTakeCareConfirm = () => {
    setShowTakeCareModal(false);
    onConfirm(selectedCustomer);
  };

  const handleCreateRequest = () => {
    window.open(PROPOSAL_DOCUMENT);
    setShowTakeCareModal(false);
    SetProjectId?.(id || '');
    if (selectedCustomer) {
      setSelectedCustomerStore({
        id: selectedCustomer.id || null,
        code: selectedCustomer.code,
        name:
          selectedCustomer.type === 'individual'
            ? selectedCustomer.personalInfo?.name || ''
            : selectedCustomer.company?.name || '',
        type: selectedCustomer.type,
        personalInfo: selectedCustomer.personalInfo,
      });
    }
  };

  return (
    <>
      <ModalComponent
        title="Tạo mới yêu cầu đặt cọc 2/3"
        open={visible}
        onCancel={onCancel}
        footer={[
          <Button key="next" type="primary" onClick={handleContinue} disabled={!selectedCustomer}>
            Tiếp tục
          </Button>,
        ]}
      >
        <Alert
          message="Số giấy tờ đã có trên hệ thống, vui lòng chọn 01 bản ghi để tiếp tục tạo phiếu"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <TableComponent
          className="table-booking-request"
          queryKeyArr={['exampleKey']}
          rowSelection={rowSelection}
          columns={columns}
          dataSource={duplicateData?.data || []}
          pagination={false}
          isPagination={false}
          rowKey={record => record.id || record.identityNo}
          scroll={{ x: 'max-content' }}
        />
      </ModalComponent>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ExclamationCircleFilled style={{ color: '#faad14', fontSize: '20px' }} />
            <span>Chưa có thông tin chia sẻ khách hàng</span>
          </div>
        }
        closeIcon={false}
        open={showTakeCareModal}
        onCancel={() => setShowTakeCareModal(false)}
        footer={[
          <Button key="continue" type="primary" onClick={handleTakeCareConfirm}>
            Tiếp tục
          </Button>,
          <Button key="create-request" onClick={handleCreateRequest}>
            Tạo tờ trình xin khai thác
          </Button>,
        ]}
        width={400}
      >
        <p style={{ marginLeft: 30 }}>Khách hàng chưa được chia sẻ, bạn có muốn tiếp tục không?</p>
      </Modal>
    </>
  );
};

export default DuplicateModal;
