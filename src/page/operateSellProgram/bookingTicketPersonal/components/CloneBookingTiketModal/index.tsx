import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, Select, DatePicker, Checkbox, Button, Row, Col, Radio, UploadFile, Typography } from 'antd';
// import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { use<PERSON>reateField, useFetch } from '../../../../../hooks';
import {
  BookingTicket,
  // Bank,
  // BankOption,
  // BookingTicket,
  BookingTicketInfo,
  CreateBookingTicket,
  CreateDemandBusiness,
  CreateDemandIndividual,
  Employee,
  PropertyUnit,
  SaleProgram,
  UpdateCustomer,
  UpdateDemand,
} from '../../../../../types/bookingRequest';
// import { getBanks } from '../../../../../service/bank';
import ModalComponent from '../../../../../components/modal';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import dayjs from 'dayjs';
import { FORMAT_DATE, OPTIONS_GENDER, REGEX_PHONE_VN } from '../../../../../constants/common';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import {
  createBookingTicket,
  createDemandBusiness,
  createDemandIndividual,
  getDetailBookingTicket,
  getEmployees,
  getProductUnits,
  getSalePrograms,
  updateCustomer,
  updateDemand,
} from '../../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import UploadFileBookingTicket from '../UploadFileBookingTicket';
import { getProvinces } from '../../../../../service/address';
import { hasNonNullValue } from '../../../../../utilities/shareFunc';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface CloneBookingRequestModalProps {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onSave: (values: Record<string, any>) => void;
  initData?: BookingTicket;
}

const CloneBookingRequestModal: React.FC<CloneBookingRequestModalProps> = ({ visible, onCancel, onSave, initData }) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const issueDate = Form.useWatch('issueDate', form);
  const issueLocation = Form.useWatch('issueLocation', form);
  const address = Form.useWatch(['address'], form);
  const isAddressNonNull = hasNonNullValue(address);
  const companyAddress = Form.useWatch('companyAdress', form);
  const isCompanyAdressNonNull = hasNonNullValue(companyAddress);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const isRootAddressNonNull = hasNonNullValue(rootAddress);

  const type = Form.useWatch(['type'], form);
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];
  const bookingTicketId = initData?.id;

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);

  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const isKHCT = initData?.customer?.type === 'customer';

  const [propertyUnitParams, setPropertyUnitParams] = useState<{
    salesProgramIds: string | undefined;
    projectId: string | undefined;
  }>({
    salesProgramIds: undefined,
    projectId: undefined,
  });

  // const defaultBankOptions: BankOption[] = useMemo(
  //   () =>
  //     bankInfoOptions
  //       .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
  //       .map((bank: Bank, index: number) => ({
  //         value: `${bank.bankCode}-${index}`,
  //         label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
  //         originalBankCode: bank.bankCode,
  //       })),
  //   [bankInfoOptions],
  // );

  const { data: bookingTicketData } = useFetch<BookingTicketInfo>({
    api: () => bookingTicketId && getDetailBookingTicket({ id: bookingTicketId }),
    queryKeyArr: ['get-detail-booking-ticket', bookingTicketId],
    enabled: !!bookingTicketId,
    cacheTime: 10,
  });

  const bookingTicket = bookingTicketData?.data?.data;

  const amountTemplateFiles = bookingTicket?.project?.setting?.templateFiles;

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });
  const provinces = dataProvinces?.data?.data;

  // const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
  //   queryKeyArrWithFilter: ['get-list-banks'],
  //   api: getBanks,
  // });
  // const banks = dataBanks?.data?.data || [];

  const { mutateAsync: _createBookingTicket } = useCreateField<CreateBookingTicket>({
    apiQuery: createBookingTicket,
    keyOfListQuery: ['get-booking-ticket'],
    isMessageError: false,
  });

  const { mutateAsync: _createDemandIndividual } = useCreateField<CreateDemandIndividual>({
    apiQuery: createDemandIndividual,
    keyOfListQuery: ['get-create-demand-individual'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _createDemandBusiness } = useCreateField<CreateDemandBusiness>({
    apiQuery: createDemandBusiness,
    keyOfListQuery: ['get-create-demand-business'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _updateCustomer } = useCreateField<UpdateCustomer>({
    apiQuery: updateCustomer,
    keyOfListQuery: ['get-update-customer'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _updateDemand } = useCreateField<UpdateDemand>({
    apiQuery: updateDemand,
    keyOfListQuery: ['get-update-demand'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleSave = async () => {
    try {
      const identitiesOld = bookingTicket?.customer?.personalInfo?.identities;
      const customerId = bookingTicket?.id;

      const values = await form.validateFields();
      const { salesProgramId, demandPropertyId, employeeTakeCareId } = values;
      const payloadCreateTicket: CreateBookingTicket = {
        ticketType: 'YCDCH',
        employeeTakeCareId: employeeTakeCareId,
        projectId: projectId,
        salesProgramId: salesProgramId || '',
        demandPropertyId: demandPropertyId || '',
        customer: {
          useResidentialAddress: values?.info?.cloneAddress,
          code: values?.code,
          gender: values?.info?.gender,
          onlyYear: values?.info?.onlyYear,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          name: values.personalInfo?.name,
          phone: values.personalInfo?.phone,
          email: values.personalInfo?.email,
          identityType: values?.identityType,
          identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNo,
          identityIssueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          identityIssueLocation:
            typeof values?.issueLocation === 'object' ? values?.issueLocation?.value : values?.issueLocation,
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
          taxCode: values?.taxCode,
          bankInfo: {
            name: values?.name || '',
            accountNumber: values?.accountNumber || '',
            beneciary: values?.beneciary || '',
          },
          position: values?.position,
          type: values?.type,
          company: { ...values?.company, address: companyAddress },
        },
        files: fileList.map(file => ({
          uid: file.uid || '',
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),

        note: values?.description,
      };

      const payloadCreateDemandIndividual: CreateDemandIndividual = {
        type: values?.type,
        name: values.personalInfo?.name,
        phone: values.personalInfo?.phone,
        gender: values?.info?.gender,
        leadCode: customerId,
        continueCreate: true,
        bankInfo:
          values?.bankAccount?.map((bank: any) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        info: {
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: [
            {
              type: values?.identityType,
              value: values?.identityNo,
              date: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
              place: values?.issueLocation?.value,
            },
          ],
        },
      };

      const payloadCreateDemandBusiness: CreateDemandBusiness = {
        type: values?.type,
        name: values.personalInfo?.name,
        phone: values.personalInfo?.phone,
        leadCode: customerId,
        continueCreate: true,
        taxCode: values?.taxCode,
        companyName: values?.company?.name,
        company: {
          ...values?.company,
          issueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          issueLocation: values?.issueLocation,
          address: values?.companyAdress,
        },
        bankInfo:
          values?.bankAccount?.map((bank: any) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        info: {
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: identitiesOld,
        },
      };

      let resp;
      if (type === 'individual') {
        resp = await _createDemandIndividual(payloadCreateDemandIndividual);
      } else if (type === 'business') {
        resp = await _createDemandBusiness(payloadCreateDemandBusiness);
      }

      if (resp?.data?.statusCode === '0') {
        const ticketResp = await _createBookingTicket(payloadCreateTicket);
        if (ticketResp?.data?.statusCode === '0') {
          onSave(payloadCreateTicket);
        }
      }
    } catch (e) {
      console.log('Validation failed:', e);
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  const handleSelectSaleProgram = (value: SaleProgram) => {
    const newParams = {
      salesProgramIds: value?.id || '',
      projectId: value?.project?.id || '',
    };

    setPropertyUnitParams(newParams);

    form.setFieldsValue({ salesProgramId: value?.id });
    form.validateFields(['salesProgramId']);
  };

  const handleSelectProductCode = (value: PropertyUnit) => {
    form.setFieldsValue({ demandPropertyId: value?.id });
    form.validateFields(['demandPropertyId']);
  };

  const handleSelectEmployee = (value: Employee) => {
    form.setFieldsValue({ employeeTakeCareId: value?.id });
    form.validateFields(['employeeTakeCareId']);
  };

  useEffect(() => {
    if (bookingTicket) {
      const bankInfoDefault = `${bookingTicket?.customer?.bankInfo[0]?.name} - ${bookingTicket?.customer?.bankInfo[0]?.accountNumber} - ${bookingTicket?.customer?.bankInfo[0]?.beneciary}`;
      const transformedBookingTicket = {
        type: bookingTicket.customer?.type,
        customer: { code: bookingTicket?.customer?.code || '' },
        identityType: bookingTicket.customer?.identityType || '',
        identityNo: bookingTicket.customer?.identityNumber || '',
        issueDate: bookingTicket.customer?.identityDate
          ? dayjs(bookingTicket.customer.identityDate, FORMAT_DATE)
          : null,
        issueLocation: bookingTicket.customer?.identityPlace || '',

        taxCode: bookingTicket.customer?.taxCode || '',

        company: {
          name: bookingTicket.customer?.company?.name || '',
        },
        personalInfo: {
          name: bookingTicket.customer?.personalInfo?.name || '',
          phone: bookingTicket.customer?.personalInfo?.phone || '',
          email: bookingTicket.customer?.personalInfo?.email || '',
        },
        info: {
          gender: bookingTicket.customer?.info?.gender,
          onlyYear: bookingTicket.customer?.info?.onlyYear || false,
          birthday: bookingTicket.customer?.info?.birthday
            ? dayjs(bookingTicket.customer?.info?.birthday, FORMAT_DATE)
            : null,
          birthdayYear: bookingTicket.customer?.info?.birthdayYear
            ? dayjs(bookingTicket.customer?.info?.birthdayYear)
            : null,
          cloneAddress: bookingTicket.customer?.info?.useResidentialAddress || false,
        },
        employeeTakeCareId: bookingTicket.employee?.id || '',
        companyAdress: bookingTicket.customer?.company?.address || '',
        address: bookingTicket.customer?.info?.address || '',
        rootAddress: bookingTicket.customer?.info?.rootAddress || '',
        bankInfo: bankInfoDefault || '',
        salesProgram: {
          id: bookingTicket?.salesProgram?.id || '',
          name: bookingTicket?.salesProgram?.name || '',
        },
        salesProgramId: bookingTicket?.salesProgramId || '',
        propertyUnit: { code: bookingTicket.propertyUnit?.code || '' },
        demandCategory: bookingTicket?.demandCategory,
        note: bookingTicket.note || '',
        files: bookingTicket.files || [],
        demandPropertyId: bookingTicket.propertyUnit?.code,
      };
      // Cập nhật giá trị form
      form.setFieldsValue(transformedBookingTicket);
      setFileList((bookingTicket?.files || []) as ExtendedUploadFile[]);
      setYearOnly(bookingTicket.customer?.info?.onlyYear || false);
      setPropertyUnitParams({
        salesProgramIds: bookingTicket.salesProgramId || undefined,
        projectId: projectId || undefined,
      });
    }
  }, [bookingTicket, form]);

  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (currentMainBankId) {
      const [bankCode, index] = currentMainBankId.split('-');
      const bankAccount = form.getFieldValue('bankAccount') || [];
      const selectedBank = bankAccount[parseInt(index)];

      // Chỉ clear mainBankId nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp
      if (!selectedBank || selectedBank.bankCode !== bankCode) {
        form.setFieldsValue({
          mainBankId: undefined,
          mainBank: {
            name: '',
            accountNumber: '',
            beneciary: '',
          },
        });
      }
    }
  }, [bankInfoOptions, form]);

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  return (
    <ModalComponent
      title="Tạo mới yêu cầu đặt chỗ 2/2"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" initialValues={bookingTicket} onValuesChange={validateForm}>
        <Row gutter={32}>
          <Col span={16}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="Hộ chiếu">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNo'}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled={isKHCT} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="issueDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled={isKHCT && issueDate != null} />
                </Form.Item>
              </Col>
              <Col span={6}>
                {type === 'individual' ? (
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                  >
                    <Select
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      allowClear
                      options={provinces?.map(item => ({
                        value: item.code,
                        label: item.nameVN,
                      }))}
                      labelInValue
                      showSearch
                      placeholder="Chọn nơi cấp"
                      onChange={value => {
                        form.setFieldsValue({
                          issueLocation: value || undefined,
                        });
                      }}
                      disabled={isKHCT && issueLocation != null}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng nhập nơi cấp' }]}
                  >
                    <Input placeholder="Nhập nơi cấp" maxLength={255} />
                  </Form.Item>
                )}
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        // Nếu giá trị chỉ chứa khoảng trống (sau khi trim còn rỗng)
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được nhập khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    disabled={isKHCT}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã khách hàng"
                  name="code"
                  required
                  // rules={[{ required: true, message: 'Vui lòng mã khách hàng' }]}
                >
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      disabled={isKHCT}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name={['personalInfo', 'phone']}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số điện thoại',
                    },
                    {
                      pattern: REGEX_PHONE_VN,
                      message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số điện thoại"
                    maxLength={15}
                    onKeyDown={handleKeyDownEnterNumber}
                    disabled={isKHCT}
                  />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              {type === 'individual' && (
                <Col span={24}>
                  <Form.Item
                    name={['info', 'gender']}
                    label="Giới tính"
                    rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
                    layout="horizontal"
                  >
                    <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} />
                  </Form.Item>
                </Col>
              )}

              <Col span={24}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>

            <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
              <SingleSelectLazy
                apiQuery={getEmployees}
                queryKey={['get-list-employee']}
                keysLabel={'name'}
                placeholder="Chọn mã nhân viên chăm sóc"
                handleSelect={handleSelectEmployee}
                defaultValues={{
                  value: bookingTicket?.employee?.id,
                  label: bookingTicket?.employee?.name,
                }}
              />
            </Form.Item>

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAdress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAdress'}
                        address={form.getFieldValue('companyAdress')}
                        handleAddressChange={validateForm}
                        isDisable={isKHCT && isCompanyAdressNonNull}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAdress', 'address']}>
                      <Input
                        placeholder="Nhập địa chỉ cụ thể"
                        maxLength={155}
                        disabled={isKHCT && isCompanyAdressNonNull}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    handleAddressChange={validateForm}
                    isDisable={isKHCT && isAddressNonNull}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT && isAddressNonNull} />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName="rootAddress"
                    address={rootAddress}
                    handleAddressChange={validateForm}
                    isDisable={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input
                    placeholder="Nhập địa chỉ cụ thể"
                    maxLength={155}
                    disabled={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            {/* <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankName']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={banks.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankCode: value,
                                        bankName: Array.isArray(option) ? '' : option?.label || '',
                                        accountNumber: form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                        beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                      },
                                    },
                                  });
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...restField}
                              name={[name, 'accountNumber']}
                              label="Số tài khoản"
                              rules={[
                                {
                                  message: 'Vui lòng nhập số tài khoản',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập số tài khoản');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập số tài khoản" maxLength={20} />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'beneciary']}
                              label="Tên người thụ hưởng"
                              rules={[
                                {
                                  message: 'Vui lòng nhập tên người thụ hưởng',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <CloseOutlined
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name); // Gọi hàm xử lý xóa
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item> */}
            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>

            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name={'bankInfo'}
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select placeholder="Chọn tài khoản giao dịch chính" disabled></Select>
              </Form.Item>
            </div>

            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Chương trình bán hàng" name="salesProgramId">
                  <SingleSelectLazy
                    apiQuery={getSalePrograms}
                    queryKey={['get-list-sale-programs']}
                    keysLabel={'name'}
                    placeholder="Chọn chương trình bán hàng"
                    handleSelect={handleSelectSaleProgram}
                    moreParams={{ projectId: projectId, allowBookingPriority: true }}
                    defaultValues={
                      bookingTicket?.salesProgram
                        ? {
                            value: bookingTicket.salesProgram.id,
                            label: bookingTicket.salesProgram.name,
                          }
                        : undefined
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name="demandPropertyId">
                  <SingleSelectLazy
                    apiQuery={getProductUnits}
                    queryKey={['get-list-product-units']}
                    keysLabel={'code'}
                    placeholder="Chọn mã sản phẩm"
                    handleSelect={handleSelectProductCode}
                    moreParams={
                      propertyUnitParams.salesProgramIds || propertyUnitParams.projectId
                        ? propertyUnitParams
                        : undefined
                    }
                    defaultValues={
                      bookingTicket?.propertyUnit
                        ? {
                            value: bookingTicket.propertyUnit.code,
                            label: bookingTicket.propertyUnit?.code,
                          }
                        : undefined
                    }
                    enabled={!!(propertyUnitParams.salesProgramIds && propertyUnitParams.projectId)}
                  />
                </Form.Item>
              </Col>
              {amountTemplateFiles && amountTemplateFiles.length > 0 && (
                <Col span={12}>
                  <Form.Item
                    label="Số tiền đăng ký"
                    name="demandCategory"
                    rules={[{ required: true, message: 'Vui lòng nhập số tiền đăng ký' }]}
                  >
                    <Select
                      placeholder="Chọn số tiền đăng ký"
                      allowClear
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      showSearch
                      options={amountTemplateFiles.map(item => ({
                        value: item?.projectCustomFormId,
                        label: item?.amountLabel,
                      }))}
                      onChange={(_, option) => {
                        const selectedOption = Array.isArray(option) ? option[0] : option;
                        form.setFieldsValue({ demandCategory: selectedOption?.label || '' });
                      }}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="description">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="property/primarty-transaction"
                size={25}
                key={bookingTicket?.id}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default CloneBookingRequestModal;
