import { <PERSON><PERSON>, Button, Col, Row, Space, Spin, Timeline, Typography } from 'antd';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import './styles.scss';
import { useCreateField, useFetch } from '../../../../hooks';
import { getDetailProductUnit, sendUpdatePriorityPropertyUnit } from '../../../../service/project';
import { useNavigate, useParams } from 'react-router-dom';
import FPTLogo from '../../../../assets/images/Default_Logo_Project.png';
import { formatNumber } from '../../../../utilities/regex';
import { getPrimaryStatusLabel } from '../../../../constants/common';
import { useEffect, useState } from 'react';
import ButtonOfPageDetail from '../../../../components/button/buttonOfPageDetail';
import { getListBookingTicket } from '../../../../service/offer';
import { useSellProgramStore } from '../../../Store';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT } from '../../../../configs/path';

const { Title, Text } = Typography;

interface IDetailProductUnit {
  histories: {
    modifiedDate: string | null;
    primaryStatus: string | null;
    actionName: string | null;
    posName: string | null;
    modifiedByName: string | null;
    reason: string | null;
    prevPosName: string | null;
  }[];
  contractPrice: string | number | undefined;
  priceAboveVat: string | number | undefined;
  priceAbove: string | number | undefined;
  priceVat: string | number | undefined;
  id: string;
  code: string;
  name: string;
  status: string;
  projectId: string;
  projectName: string;
  projectCode: string;
  productId: string;
  project: {
    id: string;
    name: string;
    imageUrl: string;
  };
  price: number;
  priorities: IBookingTicket[];
}

interface IBookingTicket {
  priority?: number;
  id: string;
  bookingTicketCode: string;
  customer?: {
    personalInfo?: {
      name: string;
    };
  };
  employee?: {
    name: string;
  };
  pos?: {
    id: string;
    name: string;
  };
  customerName?: string;
  employeeName?: string;
  posName?: string;
  posId?: string;
}

const ViewProductDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const projectId = useSellProgramStore(state => state.projectId);
  const [selectedPriority1, setSelectedPriority1] = useState<IBookingTicket | null>(null);
  const [selectedPriority2, setSelectedPriority2] = useState<IBookingTicket | null>(null);
  const [selectedPriority3, setSelectedPriority3] = useState<IBookingTicket | null>(null);
  const [showDuplicateError, setShowDuplicateError] = useState(false);

  const { data: dataDetailProductUnit, isLoading } = useFetch<IDetailProductUnit>({
    queryKeyArr: ['detail-product-unit', id],
    api: () => getDetailProductUnit(id),
    cacheTime: 1,
  });

  const { mutateAsync: updatePriority, isPending } = useCreateField({
    keyOfListQuery: ['get-payment-policy'],
    apiQuery: sendUpdatePriorityPropertyUnit,
    isMessageError: false,
    messageSuccess: 'Cập nhật ưu tiên thành công!',
  });

  useEffect(() => {
    if (dataDetailProductUnit?.data?.data?.priorities) {
      const priorities = dataDetailProductUnit.data.data.priorities;
      setSelectedPriority1(priorities.find(p => p.priority === 1) || null);
      setSelectedPriority2(priorities.find(p => p.priority === 2) || null);
      setSelectedPriority3(priorities.find(p => p.priority === 3) || null);
    }
  }, [dataDetailProductUnit]);

  const handleSelectPriority1 = (value: any | null) => {
    if (value) {
      setSelectedPriority1({
        id: value.value,
        bookingTicketCode: value.label,
        customerName: value.customer.personalInfo.name || '',
        employeeName: value.employee?.name || '',
        posName: value.pos?.name || '',
        posId: value.pos?.id || '',
      });
    } else {
      setSelectedPriority1(null);
    }
    setShowDuplicateError(false);
  };

  const handleSelectPriority2 = (value: any | null) => {
    if (value) {
      setSelectedPriority2({
        id: value.value,
        bookingTicketCode: value.label,
        customerName: value.customer.personalInfo.name || '',
        employeeName: value.employee?.name || '',
        posName: value.pos?.name || '',
      });
    } else {
      setSelectedPriority2(null);
    }
    setShowDuplicateError(false);
  };

  const handleSelectPriority3 = (value: any | null) => {
    if (value) {
      setSelectedPriority3({
        id: value.value,
        bookingTicketCode: value.label,
        customerName: value?.customer?.personalInfo?.name || '',
        employeeName: value?.employee?.name || '',
        posName: value.pos?.name || '',
      });
    } else {
      setSelectedPriority3(null);
    }
    setShowDuplicateError(false);
  };

  const hasDuplicatePriorities = () => {
    const priorities = [selectedPriority1, selectedPriority2, selectedPriority3].filter(p => p?.id);
    const uniqueIds = new Set(priorities.map(p => p?.id));
    return priorities.length !== uniqueIds.size;
  };

  const handleSubmit = () => {
    if (hasDuplicatePriorities()) {
      setShowDuplicateError(true);
      return;
    }

    const priorities = [];

    if (selectedPriority1) {
      priorities.push({
        id: '',
        ticketId: selectedPriority1.id,
        posId: selectedPriority1.posId || '',
        propertyUnitId: id,
        priority: 1,
      });
    } else {
      const oldPriority1 = dataDetailProductUnit?.data?.data?.priorities?.find(p => p.priority === 1);
      if (oldPriority1) {
        priorities.push({
          id: oldPriority1.id,
          posId: oldPriority1.posId || '',
          propertyUnitId: id,
          priority: 1,
          ticketId: '',
        });
      }
    }

    if (selectedPriority2) {
      priorities.push({
        id: '',
        ticketId: selectedPriority2.id,
        posId: selectedPriority2.posId || '',
        propertyUnitId: id,
        priority: 2,
      });
    } else {
      const oldPriority2 = dataDetailProductUnit?.data?.data?.priorities?.find(p => p.priority === 2);
      if (oldPriority2) {
        priorities.push({
          id: oldPriority2.id,
          posId: oldPriority2.posId || '',
          propertyUnitId: id,
          priority: 2,
          ticketId: '',
        });
      }
    }

    if (selectedPriority3) {
      priorities.push({
        id: '',
        ticketId: selectedPriority3.id,
        posId: selectedPriority3.posId || '',
        propertyUnitId: id,
        priority: 3,
      });
    } else {
      const oldPriority3 = dataDetailProductUnit?.data?.data?.priorities?.find(p => p.priority === 3);
      if (oldPriority3) {
        priorities.push({
          id: oldPriority3.id,
          posId: oldPriority3.posId || '',
          propertyUnitId: id,
          priority: 3,
          ticketId: '',
        });
      }
    }

    updatePriority(priorities);
  };

  const handleCancel = () => {
    navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/${projectId}`);
  };

  const detailProduct: IDetailProductUnit = dataDetailProductUnit?.data?.data || ({} as IDetailProductUnit);

  const timelineData =
    detailProduct.histories?.map(history => ({
      time: history.modifiedDate ? new Date(history.modifiedDate).toLocaleString() : '-',
      primaryStatus: history.primaryStatus || '',
      actionName: history.actionName || '',
      posName: history.posName || '',
      modifiedByName: history.modifiedByName || '',
      content: history.actionName || '',
      reason: history.reason || '',
      prevPosName: history.prevPosName || '',
    })) || [];

  return (
    <Spin spinning={isLoading || isPending}>
      <BreadCrumbComponent
        customItems={[
          { label: 'Quản lý dự án' },
          { label: 'Chi tiết dự án' },
          { label: `Chi tiết sản phẩm ${detailProduct.code}` },
        ]}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>Sản phẩm {detailProduct.code}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{detailProduct?.project?.name}</span>
          </Text>
        </div>
        <div className="button-actions-control-project">
          <Button size="small">Thu hồi sản phẩm</Button>
          <Button size="small">Chuyển sang đơn vị khác</Button>
          <Button size="small">Chuyển sang CTBH khác</Button>
        </div>
        <div className="project-image">
          <img
            src={
              detailProduct?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${detailProduct?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Row style={{ marginBottom: 100 }}>
        <Col xs={24} md={12} style={{ paddingRight: 60 }}>
          <Col xs={24} md={24}>
            <div className="project_detail-page__content-left">
              <div className="project_detail-page__title">
                <Title level={5}>Thông tin sản phẩm</Title>
              </div>
              <div className="project_detail-page__content">
                <p>
                  <strong>Số sản phẩm</strong> <span>{detailProduct.code || '-'}</span>
                </p>
                <p>
                  <strong>Giá bán chưa VAT</strong> <span>{formatNumber(detailProduct.price) || '-'}</span>
                </p>
                <p>
                  <strong>Giá bán có VAT</strong> <span>{formatNumber(detailProduct.priceVat) || '-'}</span>
                </p>
                <p>
                  <strong>Đơn giá chưa VAT</strong> <span>{formatNumber(detailProduct.priceAbove) || '-'}</span>
                </p>
                <p>
                  <strong>Đơn giá có VAT</strong> <span>{formatNumber(detailProduct.priceAboveVat) || '-'}</span>
                </p>
                <p>
                  <strong>Tổng giá trị hợp đồng</strong>
                  <span>{formatNumber(detailProduct.contractPrice) || '-'}</span>
                </p>
              </div>
            </div>
          </Col>
          <Col xs={24} md={24}>
            <div className="project_detail-page__content-left">
              <div className="project_detail-page__title">
                <Title level={5}>Chọn loại ưu tiên</Title>
              </div>
              <Space direction="vertical" size={10} style={{ width: '100%' }}>
                {/* Ưu tiên 1 */}
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 1</Text>
                  </Col>
                  <Col span={20}>
                    <SingleSelectLazy
                      apiQuery={getListBookingTicket}
                      queryKey={['priority-1-booking-ticket']}
                      keysLabel={'bookingTicketCode'}
                      placeholder="Chọn ưu tiên 1"
                      handleSelect={handleSelectPriority1}
                      allowClear={true}
                      defaultValues={
                        selectedPriority1
                          ? { label: selectedPriority1.bookingTicketCode, value: selectedPriority1.id }
                          : undefined
                      }
                      moreParams={{
                        id: projectId,
                        ticketType: 'YCDCH',
                        isAccounted: true,
                        status: 'BOOKING_APPROVED',
                      }}
                    />
                  </Col>
                </Row>
                {selectedPriority1 && (
                  <Row style={{ paddingLeft: '19%' }}>
                    <Col>
                      <div className="info-customer-priority">
                        <div>
                          <Text type="secondary">Khách hàng:</Text>
                          <Text className="text-right">{selectedPriority1.customerName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">Tư vấn viên:</Text>
                          <Text className="text-right">{selectedPriority1?.employeeName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">ĐVBH:</Text>
                          <Text className="text-right">{selectedPriority1?.posName}</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                )}

                {/* Ưu tiên 2 */}
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 2</Text>
                  </Col>
                  <Col span={20}>
                    <SingleSelectLazy
                      apiQuery={getListBookingTicket}
                      queryKey={['priority-2-booking-ticket']}
                      keysLabel={'bookingTicketCode'}
                      placeholder="Chọn ưu tiên 2"
                      handleSelect={handleSelectPriority2}
                      allowClear={true}
                      defaultValues={
                        selectedPriority2
                          ? { label: selectedPriority2.bookingTicketCode, value: selectedPriority2.id }
                          : undefined
                      }
                      moreParams={{
                        id: projectId,
                        ticketType: 'YCDCH',
                        isAccounted: true,
                      }}
                    />
                  </Col>
                </Row>
                {selectedPriority2 && (
                  <Row style={{ paddingLeft: '19%' }}>
                    <Col>
                      <div className="info-customer-priority">
                        <div>
                          <Text type="secondary">Khách hàng:</Text>
                          <Text className="text-right">{selectedPriority2.customerName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">Tư vấn viên:</Text>
                          <Text className="text-right">{selectedPriority2?.employeeName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">ĐVBH:</Text>
                          <Text className="text-right">{selectedPriority2?.posName}</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                )}

                {/* Ưu tiên 3 */}
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 3</Text>
                  </Col>
                  <Col span={20}>
                    <SingleSelectLazy
                      apiQuery={getListBookingTicket}
                      queryKey={['priority-3-booking-ticket']}
                      keysLabel={'bookingTicketCode'}
                      placeholder="Chọn ưu tiên 3"
                      handleSelect={handleSelectPriority3}
                      allowClear={true}
                      defaultValues={
                        selectedPriority3
                          ? { label: selectedPriority3.bookingTicketCode, value: selectedPriority3.id }
                          : undefined
                      }
                      moreParams={{
                        id: projectId,
                        ticketType: 'YCDCH',
                        isAccounted: true,
                      }}
                    />
                  </Col>
                </Row>
                {selectedPriority3 && (
                  <Row style={{ paddingLeft: '19%' }}>
                    <Col>
                      <div className="info-customer-priority">
                        <div>
                          <Text type="secondary">Khách hàng:</Text>
                          <Text className="text-right">{selectedPriority3.customerName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">Tư vấn viên:</Text>
                          <Text className="text-right">{selectedPriority3?.employeeName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">ĐVBH:</Text>
                          <Text className="text-right">{selectedPriority3?.posName}</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                )}

                {showDuplicateError && (
                  <Row>
                    <Col span={24}>
                      <Alert
                        message="Mỗi ưu tiên chỉ được gán với 1 YCDCHO"
                        type="error"
                        showIcon
                        style={{ marginTop: 8 }}
                      />
                    </Col>
                  </Row>
                )}
              </Space>
            </div>
          </Col>
        </Col>
        <Col xs={24} md={12} style={{ paddingLeft: 60, height: '500' }}>
          <div className="project_detail-page__content-right">
            <div className="project_detail-page__title">
              <Title level={5}>Lịch sử</Title>
            </div>
            <Timeline
              style={{ height: '500px', overflowY: 'auto', padding: '20px 0' }}
              items={timelineData.map(item => ({
                children: (
                  <div>
                    <div className="text-history-time">{item.time}</div>
                    <div className="text-history">Trạng thái: {getPrimaryStatusLabel(item.primaryStatus)?.label}</div>
                    <div className="text-history">Hành động: {item.actionName}</div>
                    <div className="text-history">Đơn vị giữ sản phẩm: {item.prevPosName}</div>
                    <div className="text-history">Người thực hiện: {item.modifiedByName}</div>
                    <div className="text-history">Đơn vị chuyển tới: {item.posName}</div>
                    <div className="text-history">Ghi chú: {item.reason}</div>
                  </div>
                ),
                color: 'blue',
              }))}
            />
          </div>
        </Col>
      </Row>
      <ButtonOfPageDetail handleCancel={handleCancel} isShowModal={false} handleSubmit={handleSubmit} />
    </Spin>
  );
};

export default ViewProductDetails;
