import { Form, FormInstance } from 'antd';
import { useEffect, useState } from 'react';
import { getBlockBySalesProgramIds } from '../../../service/project';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { useSellProgramStore } from '../../Store';
import MultiSelectStatic from '../../../components/select/mutilSelectStatic';
import { PRIMARY_STATUS } from '../../../constants/common';

interface Status {
  label: string;
  value: string;
}

type TFilter = {
  salesProgramIds?: string[];
};

function FilterRowTableOperate({ handleSubmit }: { handleSubmit: (values: Record<string, unknown>) => void }) {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [blockOptions, setBlockOptions] = useState<{ label: string; value: string }[]>([]);
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const fetchBlocks = async (
    salesProgramIds: string[],
    form: FormInstance,
    setBlockOptions: (options: { label: string; value: string }[]) => void,
  ) => {
    if (!salesProgramIds.length) {
      setBlockOptions([]);
      form.setFieldsValue({ block: [] });
      return;
    }

    try {
      const blocks = await getBlockBySalesProgramIds({ salesProgramIds });
      if (!blocks?.data?.data) return;

      const formattedBlocks = blocks.data.data.map((block: { id: string; code: string }) => ({
        label: block.code,
        value: block.id,
        block: block.code,
      }));

      setBlockOptions(formattedBlocks);
      form.setFieldsValue({ block: formattedBlocks.map((b: { value: unknown }) => b.value) });
    } catch (error) {
      console.error('Error fetching blocks by sales program ids:', error);
    }
  };
  useEffect(() => {
    fetchBlocks(salesProgramIds, form, setBlockOptions);
  }, [salesProgramIds, form]);

  const handleSubmitFilter = (values: TFilter) => {
    handleSubmit({ ...values });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectBlock = (values: unknown[]) => {
    form.setFieldsValue({ block: values });
  };

  return (
    <>
      <DropdownFilterSearch
        onClearFilters={() => {
          form.resetFields(); // Xóa tất cả giá trị trong form
          setBlockOptions([]); // Xóa block đã chọn
        }}
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        extraFormItems={
          <>
            <Form.Item
              label="Block"
              name="block"
              rules={[{ required: true, message: 'Vui lòng chọn ít nhất một block' }]}
            >
              <MultiSelectLazy
                enabled={isOpenFilter}
                moreParams={{ salesProgramIds }}
                apiQuery={getBlockBySalesProgramIds}
                queryKey={['getBlockBySalesProgramIds', salesProgramIds]}
                keysLabel={['code']}
                handleListSelect={handleSelectBlock}
                placeholder="Chọn block"
                keysTag={['block']}
                defaultValues={blockOptions}
                showSelectAll={true}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="primaryStatus">
              <MultiSelectStatic
                data={PRIMARY_STATUS}
                handleListSelect={(values: Status[]) => {
                  const statusString = values.map(item => item.value).join(',');
                  form.setFieldsValue({ primaryStatus: statusString });
                }}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

export default FilterRowTableOperate;
