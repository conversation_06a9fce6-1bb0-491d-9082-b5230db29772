import React, { useEffect, useMemo, useState } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Row,
  Col,
  Radio,
  Typography,
  Button,
  UploadFile,
  Modal,
  Spin,
} from 'antd';
import { FORMAT_DATE, OPTIONS_GENDER_STRING, TXN_STATUS } from '../../../../constants/common';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import SelectAddress from '../../../../components/selectAddress';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import { useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import { getDetailDepositRequired, getListBookingApproved, updateYCDC } from '../../../../service/depositRequired';
import { useNavigate, useParams } from 'react-router-dom';
import { Bank, BankAccount, BankOption, BookingTicketInfo, Employee } from '../../../../types/bookingRequest';
import UploadFileBookingTicket from '../../bookingTicketPersonal/components/UploadFileBookingTicket';
import { formatCurrency } from '../../../../utilities/shareFunc';
import dayjs from 'dayjs';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { getBanks } from '../../../../service/bank';
import {
  adminApproveCancelRequestTicket,
  adminApproveTicket,
  adminCancelRequestTicket,
  cancelRequestTicket,
  createUrlPayment,
  csApproveCancelRequestTicket,
  csApproveTicket,
  csCancelRequestTicket,
  getEmployees,
} from '../../../../service/bookingTicket';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import ButtonOfPageDetail from '../../../../components/button/buttonOfPageDetail';
import { MutationFunction } from '@tanstack/react-query';
import ConfirmActionModal from '../../../../components/modal/specials/ConfirmActionModal';
// import { PERMISSION_PROPERTY_TICKET } from '../../../../constants/permissions/propertyTicket';
import ConfirmApproveModal from '../../bookingTicketPersonal/components/ConfirmApproveModal';
import CreateDepositContractModal from '../../../contractManagement/depositContracts/createDepositContract';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const DepositRequiredDetail = () => {
  const navigate = useNavigate();
  const { depositId } = useParams<{ depositId: string }>();
  const [form] = Form.useForm();
  const type = Form.useWatch(['type'], form);
  const [yearOnly, setYearOnly] = useState(false);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];
  const [urlPayment, setUrlPayment] = useState<string>('');
  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);
  const [isOpenModalCancelConfirm, setIsOpenModalCancelConfirm] = useState(false);
  const [isOpenModalCancelReject, setIsOpenModalCancelReject] = useState(false);
  // const { csApprovedTicket, adminApprovedTicket } = useCheckPermissions(PERMISSION_PROPERTY_TICKET);
  const [isOpenModalReject, setIsOpenModalReject] = useState(false);
  const [isOpenModalApprove, setIsOpenModalApprove] = useState(false);

  const { data: DepositRequiredData, isLoading } = useFetch<BookingTicketInfo>({
    api: () => depositId && getDetailDepositRequired({ id: depositId }),
    queryKeyArr: ['detail-deposit-required', depositId],
    enabled: !!depositId,
    cacheTime: 10,
  });
  const depositRequired = DepositRequiredData?.data?.data;

  const idProject = depositRequired?.project?.id;

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
    moreParams: { branchIsActvie: true },
  });

  const { mutateAsync: updateDeposit } = useUpdateField({
    apiQuery: updateYCDC,
    keyOfListQuery: ['get-request-deposit', idProject],
    keyOfDetailQuery: ['get-request-deposit', idProject],
    messageSuccess: 'Cập nhật phiếu yêu cầu đặt cọc thành công!',
    messageError: 'Cập nhật phiếu yêu cầu đặt cọc thất bại!',
    isShowMessage: false,
  });

  const { mutateAsync: _adminApproveTicket } = useCreateField({
    apiQuery: adminApproveTicket,
    keyOfDetailQuery: ['detail-deposit-required', depositId],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { mutateAsync: _csApproveTicket } = useCreateField({
    apiQuery: csApproveTicket,
    keyOfDetailQuery: ['detail-deposit-required', depositId],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const listBank = dataBanks?.data?.data || [];

  useEffect(() => {
    if (depositRequired) {
      const bankInfoDefault = `${depositRequired?.customer?.mainBank?.name} - ${depositRequired?.customer?.mainBank?.accountNumber} - ${depositRequired?.customer?.mainBank?.beneciary}`;
      const transformeddepositRequired = {
        type: depositRequired.customer?.type || 'business',
        customer: {
          code: depositRequired?.customer?.code || '',
        },
        bankAccount: depositRequired?.customer?.bankInfo?.map((bank: BankAccount) => ({
          bankName: bank.bankName || '',
          bankCode: bank.bankCode || '',
          accountNumber: bank.accountNumber || '',
          beneciary: bank.beneciary || '',
        })),
        identityType: depositRequired.customer?.identityType || '',
        taxCode: depositRequired.customer?.taxCode || '',
        identityNumber: depositRequired.customer?.identityNumber || '',
        identityDate: depositRequired.customer?.identityDate
          ? dayjs(depositRequired.customer.identityDate, FORMAT_DATE)
          : null,
        identityPlace: depositRequired.customer?.identityPlace || '',
        company: {
          name: depositRequired.customer?.company?.name || '',
        },
        personalInfo: {
          name: depositRequired.customer?.personalInfo?.name || '',
          phone: depositRequired.customer?.personalInfo?.phone || '',
          email: depositRequired.customer?.personalInfo?.email || '',
        },
        info: {
          gender: depositRequired.customer?.info?.gender,
          onlyYear: depositRequired.customer?.info?.onlyYear || false,
          birthday: depositRequired.customer?.info?.birthday
            ? dayjs(depositRequired.customer?.info?.birthday, FORMAT_DATE)
            : null,
          birthdayYear: depositRequired.customer?.info?.birthdayYear
            ? dayjs(depositRequired.customer?.info?.birthdayYear)
            : null,
          cloneAddress: depositRequired.customer?.info?.useResidentialAddress || false,
        },
        companyAdress: depositRequired.customer?.company?.address || '',
        address: depositRequired.customer?.info?.address || '',
        rootAddress: depositRequired.customer?.info?.rootAddress || '',
        mainBankId: bankInfoDefault || '',
        salesProgram: { ...depositRequired?.salesProgram },
        propertyUnit: { code: depositRequired.propertyUnit?.code || '' },
        amountRegistration: formatCurrency((depositRequired?.project?.setting?.amountRegistration ?? 0).toString()),
        note: depositRequired.note || '',
        files: depositRequired.files || [],
        salesProgramId: depositRequired?.propertyUnit?.salesProgram?.name || '',
        status: depositRequired?.status || 'pending',
        mainBank: {
          name: depositRequired?.customer?.mainBank?.name || '',
          accountNumber: depositRequired?.customer?.mainBank?.accountNumber || '',
          beneciary: depositRequired?.customer?.mainBank?.beneciary || '',
        },
        loanType: depositRequired?.loanType || false,
      };
      // Cập nhật giá trị form
      form.setFieldsValue(transformeddepositRequired);
      setFileList((depositRequired?.files || []) as ExtendedUploadFile[]);
      setYearOnly(depositRequired.customer?.info?.onlyYear || false);
    }
  }, [depositRequired, form, type]);
  const handleFinish = async () => {
    try {
      const values = await form.validateFields();
      const { mainBank, employeeTakeCareId, status } = values;

      const payloadUpdateTicket = {
        id: depositId,
        ticketType: 'YCDC',
        employeeTakeCareId: depositRequired?.employee?.id || employeeTakeCareId,
        projectId: idProject as string,
        projectType: depositRequired?.project?.type || 'Căn hộ',
        demandCategory: depositRequired?.demandCategory || null,
        bookingTicketCode: depositRequired?.bookingTicketCode || null,
        escrowTicketCode: depositRequired?.escrowTicketCode || null,
        customer: {
          code: values?.customer?.code || null,
          name: values?.personalInfo?.name || values?.company?.name,
          gender: Number(values?.info?.gender),
          birthday: !yearOnly
            ? values?.info?.birthday
              ? dayjs(values?.info?.birthday).format(FORMAT_DATE)
              : null
            : null,
          birthdayYear:
            yearOnly && values?.info?.birthdayYear ? dayjs(values?.info?.birthdayYear).format('YYYY') : null,
          onlyYear: yearOnly,
          phone: values?.personalInfo?.phone,
          email: values?.personalInfo?.email || undefined,
          identityType: values?.identityType,
          identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNumber,
          identityIssueDate: values?.identityDate ? dayjs(values?.identityDate).format(FORMAT_DATE) : '',
          identityIssueLocation: values?.identityPlace || '',
          address: {
            country: {
              code: 'VN',
              name: 'VN',
            },
            province: {
              code: values?.address?.province?.code || null,
              name: values?.address?.province?.name || null,
            },
            district: {
              code: values?.address?.district?.code || null,
              name: values?.address?.district?.name || null,
            },
            ward: {
              code: values?.address?.ward?.code || null,
              name: values?.address?.ward?.name || null,
            },
            address: values?.address?.address || null,
          },
          rootAddress: {
            country: {
              code: 'VN',
              name: 'VN',
            },
            province: {
              code: values?.rootAddress?.province?.code || null,
              name: values?.rootAddress?.province?.name || null,
            },
            district: {
              code: values?.rootAddress?.district?.code || null,
              name: values?.rootAddress?.district?.name || null,
            },
            ward: {
              code: values?.rootAddress?.ward?.code || null,
              name: values?.rootAddress?.ward?.name || null,
            },
            address: values?.rootAddress?.address || null,
          },
          taxCode: values?.taxCode || '',
          bankInfo: bankInfoOptions || [],
          mainBank: {
            name: mainBank?.name || '',
            accountNumber: mainBank?.accountNumber || '',
            beneciary: mainBank?.beneciary || '',
          },
          position: values?.position || '',
          type: values?.type,
          company:
            values?.type === 'business'
              ? {
                  name: values?.company?.name || '',
                  issueType: 'MST',
                  taxCode: values?.taxCode || '',
                  issueDate: values?.identityDate ? dayjs(values?.identityDate).format(FORMAT_DATE) : '',
                  issueLocation: values?.identityPlace || '',
                }
              : null,
        },
        amountRegistration: depositRequired?.amountRegistration || 0,
        files: fileList.map(file => ({
          uid: file.uid || '',
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),
        status: status,
        isCreateAtStage2: true,
        reason: values?.reason || null,
        loanType: values?.loanType || null,
      };
      const ticketResp = await updateDeposit(payloadUpdateTicket);
      if (ticketResp?.data?.statusCode === '0') {
        navigate(`/projects/operate-sell-program/${idProject}?tab=3`, {
          replace: true,
        });
      }
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    if (!bankAccount || !currentMainBankId) return;

    const removedBank = bankAccount[name];
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    if (isMainBankRemoved) {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: { name: '', accountNumber: '', beneciary: '' },
      });
    } else if (currentMainBankId) {
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);
      if (currentIndexNum > name) {
        form.setFieldsValue({
          mainBankId: `${bankCode}-${currentIndexNum - 1}`,
        });
      }
    }
  };
  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);

    form.setFieldsValue({
      mainBankId: selectedBank ? value : undefined,
      mainBank: selectedBank
        ? {
            name: selectedBank.bankName || '',
            accountNumber: selectedBank.accountNumber || '',
            beneciary: selectedBank.beneciary || '',
          }
        : { name: '', accountNumber: '', beneciary: '' },
    });
  };
  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );
  const handleCreateUrlPayment = async () => {
    const payload = {
      amount: depositRequired?.amountRegistration,
      orderId: depositRequired?.code,
      bankCode: depositRequired?.customer?.mainBank?.bankCode,
    };
    const resp = await createUrlPayment(payload);
    setUrlPayment(String(resp?.data?.data ?? ''));
  };
  const handleSelectEmployee = (value: Employee) => {
    form.validateFields(['employeeTakeCareId']);
    form.setFieldsValue({
      employeeTakeCareId: value,
    });
  };
  const handleSelectBookingTicketCode = (value: Employee) => {
    form.validateFields(['depositRequiredCode']);
    form.setFieldsValue({
      depositRequiredCode: value,
    });
  };
  const employeeDefaultValue = useMemo(() => {
    return depositRequired?.employee
      ? {
          label: `${depositRequired?.employee?.code} - ${depositRequired?.employee?.name}`,
          value: depositRequired?.employee?.id,
        }
      : undefined;
  }, [depositRequired?.employee]);

  const depositRequiredCodeDefaultValue = useMemo(() => {
    return depositRequired && depositRequired.bookingTicketCode && depositRequired.bookingTicketCode.trim()
      ? {
          label: `${depositRequired.bookingTicketCode}`,
          value: depositRequired.id,
        }
      : undefined;
  }, [depositRequired]);
  const handleCancel = () => {
    if (isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          navigate(`/projects/operate-sell-program/${idProject}?tab=3`, {
            replace: true,
          });
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(`/projects/operate-sell-program/${idProject}?tab=3`, {
        replace: true,
      });
    }
  };
  const [isOpenDepositContractModal, setIsOpenDepositContractModal] = useState(false);

  // ✅ Handler đơn giản - chỉ mở modal
  const handleCreateDepositContract = () => {
    setIsOpenDepositContractModal(true);
  };

  // ✅ Handler đóng modal
  const handleCloseDepositContractModal = () => {
    setIsOpenDepositContractModal(false);
  };

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent
        customItems={[
          { label: 'Dự án và sản phẩm' },
          { label: 'Danh sách dự án', path: '/projects' },
          { label: `Thông tin dự án - ${depositRequired?.project?.name || ''}`, path: `/projects` },
          { label: `Vận hành bán hàng dự án ${depositRequired?.project?.name || ''}`, path: '#' },
        ]}
        titleBread={depositRequired?.escrowTicketCode}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Phiếu yêu cầu đặt cọc ${depositRequired?.escrowTicketCode}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{depositRequired?.project?.name}</span>
          </Text>
        </div>
        <div className="project-actions">
          {depositRequired?.status === 'SUCCESS' && (
            <Button onClick={handleCreateDepositContract}>Tạo hợp đồng cọc</Button>
          )}
          {/* {csApprovedTicket && adminApprovedTicket && (
            <>
              {(depositRequired?.status === 'ADMIN_APPROVED_TICKET' || depositRequired?.status === 'CLOSE') && (
                <Button onClick={() => setIsOpenModalApprove(true)}>Xác nhận</Button>
              )}
              {depositRequired?.status !== 'CS_APPROVED_TICKET' &&
                depositRequired?.status !== 'CS_REJECTED_TICKET' &&
                depositRequired?.status !== 'ADMIN_REJECTED_TICKET' &&
                depositRequired?.status !== 'BOOKING_APPROVED' &&
                depositRequired?.status !== 'CANCEL_REQUESTED' &&
                depositRequired?.status !== 'ADMIN_APPROVED_CANCEL_REQUESTED' &&
                depositRequired?.status !== 'CS_APPROVED_CANCEL_REQUESTED' && (
                  <Button onClick={() => setIsOpenModalReject(true)}>Từ chối</Button>
                )}
              {(depositRequired?.status === 'CANCEL_REQUESTED' ||
                depositRequired?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED') && (
                <>
                  <Button onClick={() => setIsOpenModalCancelConfirm(true)}>Xác nhận đề nghị hủy</Button>
                  <Button onClick={() => setIsOpenModalCancelReject(true)}>Từ chối đề nghị hủy</Button>
                </>
              )}
            </>
          )}
          {depositRequired?.status === 'UNSUCCESS' && (
            <Button onClick={() => setIsOpenModalCancelConfirm(true)}>Đề nghị hủy</Button>
          )} */}
        </div>
        <div className="project-image">
          <img
            src={
              depositRequired?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${depositRequired?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: 32 }}
        onFinish={handleFinish}
        initialValues={depositRequired}
        onValuesChange={() => setIsFormChanged(true)}
      >
        <Row gutter={32}>
          <Col span={16}>
            <Title level={5}>Thông tin nhân viên</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                  <SingleSelectLazy
                    moreParams={{ isActive: true }}
                    apiQuery={getEmployees}
                    queryKey={['get-list-employee']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn mã nhân viên chăm sóc"
                    handleSelect={handleSelectEmployee}
                    defaultValues={employeeDefaultValue}
                    disabled
                  />
                </Form.Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Mã phiếu YCDCHO/YCDCO" name="depositRequiredCode">
                  <SingleSelectLazy
                    apiQuery={getListBookingApproved}
                    moreParams={{ idProject: idProject }}
                    queryKey={['get-request-deposit']}
                    keysLabel={['bookingTicketCode']}
                    placeholder="Chọn mã YCDCHO/YCDCO"
                    enabled={!!idProject}
                    key={'depositRequiredCode'}
                    handleSelect={handleSelectBookingTicketCode}
                    defaultValues={depositRequiredCodeDefaultValue}
                    disabled
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Khách hàng cá nhân</Option>
                    <Option value="business">Khách hàng doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="PASSPORT">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNumber'}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="identityDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Nơi cấp"
                  name="identityPlace"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được nhập khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã khách hàng"
                  name={['customer', 'code']}
                  required
                  // rules={[{ required: true, message: 'Vui lòng mã khách hàng' }]}
                >
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item label="Số điện thoại" name={['personalInfo', 'phone']}>
                  <Input placeholder="Nhập số điện thoại" maxLength={15} disabled />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} disabled />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              <Col span={14}>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                        disabled
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
              <Col span={10}>
                <Form.Item
                  name={['info', 'gender']}
                  label="Giới tính"
                  rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                  layout="horizontal"
                >
                  <Radio.Group options={OPTIONS_GENDER_STRING} style={{ marginLeft: 30 }} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAdress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAdress'}
                        address={form.getFieldValue('companyAdress')}
                        isDisable
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAdress', 'address']}>
                      <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}

            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox disabled>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'rootAddress'}
                    address={form.getFieldValue('rootAddress')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankName']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                disabled
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={listBank.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankCode: value,
                                        bankName: Array.isArray(option) ? '' : option?.label || '',
                                        accountNumber: form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                        beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                      },
                                    },
                                  });
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...restField}
                              name={[name, 'accountNumber']}
                              label="Số tài khoản"
                              rules={[
                                {
                                  message: 'Vui lòng nhập số tài khoản',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập số tài khoản');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input
                                placeholder="Nhập số tài khoản"
                                maxLength={20}
                                onBlur={e => {
                                  const fieldPath = ['bankAccount', name, 'accountNumber'];
                                  form.setFieldValue(fieldPath, e.target.value.trim());
                                }}
                                disabled
                              />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'beneciary']}
                              label="Tên người thụ hưởng"
                              rules={[
                                {
                                  message: 'Vui lòng nhập tên người thụ hưởng',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} disabled />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <CloseOutlined
                              disabled
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name);
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            disabled
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: '', accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  disabled
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={defaultBankOptions}
                  onChange={handleSelectBankInfo}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              {/* Trường ẩn để lưu trữ đối tượng MainBank */}
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>
            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Chương trình bán hàng" name="salesProgramId">
                  <Select placeholder="Chọn chương trình bán hàng" disabled>
                    <Option value="program1">Chương trình 1</Option>
                    <Option value="program2">Chương trình 2</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name={['propertyUnit', 'code']}>
                  <Input placeholder="Nhập mã sản phẩm" maxLength={50} disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Số tiền đăng ký" name="amountRegistration">
                  <Input placeholder="Nhập số tiền đăng ký" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>
            {type === 'business' && (
              <>
                <Title level={5}>Thanh toán online</Title>
                <Text>
                  Mã phiếu thu:{' '}
                  <Text style={{ marginLeft: 15 }}>
                    {Array.isArray(depositRequired?.reciept)
                      ? depositRequired?.reciept?.map((item: { code: string }) => item?.code).join(', ')
                      : depositRequired?.reciept || 'Chưa có'}
                  </Text>
                  <Button
                    type="default"
                    style={{ marginLeft: 30 }}
                    onClick={handleCreateUrlPayment}
                    disabled={depositRequired?.status === 'CS_APPROVED_TICKET'}
                  >
                    Tạo link thanh toán
                  </Button>
                </Text>
                {urlPayment && (
                  <a
                    href={urlPayment}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      marginTop: 10,
                      overflowWrap: 'break-word',
                      wordBreak: 'break-all',
                      display: 'inline-block',
                      maxWidth: '100%',
                    }}
                  >
                    {urlPayment}
                  </a>
                )}
              </>
            )}
            <Title level={5} style={{ marginBottom: 16, marginTop: 16 }}>
              Thông tin vay
            </Title>
            <Form.Item style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
              <span style={{ minWidth: 120 }}>Vay ngân hàng</span>
              <Form.Item name="loanType" noStyle>
                <Radio.Group disabled style={{ marginLeft: 16 }}>
                  <Radio value="yes">Có</Radio>
                  <Radio value="no">Không</Radio>
                </Radio.Group>
              </Form.Item>
            </Form.Item>
          </Col>

          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Trạng thái" name="status">
              <Select
                placeholder="Chọn trạng thái"
                options={Object.entries(TXN_STATUS).map(([key, value]) => ({ value: key, label: value }))}
                disabled
              ></Select>
            </Form.Item>
            <Form.Item label="Ghi chú" name="note">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} disabled />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={type === 'business' ? 'deposit-request/business' : 'deposit-request/personal'}
                size={25}
                isDetail
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <ButtonOfPageDetail
        handleSubmit={() => form.submit()}
        handleCancel={handleCancel}
        isShowModal={false}
        disabled={true}
      />
      <ConfirmApproveModal
        open={isOpenModalApprove}
        apiQuery={
          depositRequired?.status === 'ADMIN_APPROVED_TICKET'
            ? (_csApproveTicket as MutationFunction<unknown, unknown>)
            : (_adminApproveTicket as MutationFunction<unknown, unknown>)
        }
        keyOfListQuery={['detail-deposit-required', depositId]}
        onCancel={() => {
          setIsOpenModalApprove(false);
        }}
        title="Xác nhận duyệt phiếu"
        isTitlePlaceholder
        labelConfirm="Xác nhận"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        record={depositRequired}
        actionType="approve"
      />
      <ConfirmApproveModal
        open={isOpenModalReject}
        apiQuery={
          depositRequired?.status === 'ADMIN_APPROVED_TICKET'
            ? (_csApproveTicket as MutationFunction<unknown, unknown>)
            : (_adminApproveTicket as MutationFunction<unknown, unknown>)
        }
        keyOfListQuery={['detail-deposit-required', depositId]}
        onCancel={() => {
          setIsOpenModalReject(false);
        }}
        title="Xác nhận từ chối duyệt phiếu"
        isTitlePlaceholder
        labelConfirm="Xác nhận"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        record={depositRequired}
        actionType="reject"
      />
      <ConfirmActionModal
        open={isOpenModalCancelConfirm && depositRequired?.status === 'UNSUCCESS'}
        apiQuery={cancelRequestTicket as MutationFunction<unknown, unknown>}
        keyOfDetailQuery={['detail-deposit-required', depositId]}
        onCancel={() => {
          setIsOpenModalCancelConfirm(false);
        }}
        title="Đề nghị hủy YCĐCO"
        description="Vui lòng nhập lý do hủy yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: depositId }}
        showReasonField={true}
      />
      <ConfirmActionModal
        open={
          isOpenModalCancelConfirm &&
          (depositRequired?.status === 'CANCEL_REQUESTED' ||
            depositRequired?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED')
        }
        apiQuery={
          depositRequired?.status === 'CANCEL_REQUESTED'
            ? (adminApproveCancelRequestTicket as MutationFunction<unknown, unknown>)
            : (csApproveCancelRequestTicket as MutationFunction<unknown, unknown>)
        }
        keyOfDetailQuery={['detail-deposit-required', depositId]}
        onCancel={() => {
          setIsOpenModalCancelConfirm(false);
        }}
        title="Duyệt đề nghị hủy YCĐCO"
        description="Vui lòng nhập lý do duyệt yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: depositId }}
        showReasonField={true}
      />

      <ConfirmActionModal
        open={
          isOpenModalCancelReject &&
          (depositRequired?.status === 'CANCEL_REQUESTED' ||
            depositRequired?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED')
        }
        apiQuery={
          depositRequired?.status === 'CANCEL_REQUESTED'
            ? (adminCancelRequestTicket as MutationFunction<unknown, unknown>)
            : (csCancelRequestTicket as MutationFunction<unknown, unknown>)
        }
        keyOfDetailQuery={['detail-deposit-required', depositId]}
        onCancel={() => setIsOpenModalCancelReject(false)}
        title="Từ chối đề nghị hủy YCĐCO"
        description="Vui lòng nhập lý do từ chối yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: depositId }}
        showReasonField={true}
      />
      <CreateDepositContractModal
        visible={isOpenDepositContractModal}
        onClose={handleCloseDepositContractModal}
        preSelectedYCDCId={depositRequired?.id}
      />
    </Spin>
  );
};

export default DepositRequiredDetail;
