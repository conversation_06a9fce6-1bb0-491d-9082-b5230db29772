import { Form } from 'antd';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterPeriod from '../../components/dropdown/dropdownFilterPeriod';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import { DEFAULT_PARAMS } from '../../constants/common';
import useFilter from '../../hooks/filter';
import { IFilterPersonalCommission } from '../../types/personalCommission';

const FilterPersonalCommission = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<IFilterPersonalCommission>>;
}) => {
  const [form] = Form.useForm();
  const [, setFilter] = useFilter();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: IFilterPersonalCommission) => {
    const newFilter: IFilterPersonalCommission = {
      periodName: values?.period ? values.period : undefined,
      year: values?.year ? String(values.year) : undefined,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setFilter(DEFAULT_PARAMS);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilterParams(prev => ({ search: prev?.search }));
      setFilter({});
    }, 100);
  };

  const handleSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
    setFilter(DEFAULT_PARAMS);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={<DropdownFilterPeriod label="Kỳ tính hoa hồng" fieldName="period" />}
      />
    </>
  );
};

export default FilterPersonalCommission;
