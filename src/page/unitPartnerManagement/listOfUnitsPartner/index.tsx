import { Button, TableColumnsType, Typography } from 'antd';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import InputSearch from '../../../components/input/InputSearch';
import TableComponent from '../../../components/table';
import { UNIT_PARTNER_MANAGEMENT } from '../../../configs/path';
import { useFetch } from '../../../hooks';
import './styles.scss';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { deleteUnitsPartner, getListUnitsPartner } from '../../../service/unitsPartner';
import { MutationFunction } from '@tanstack/react-query';
import { Partner } from '../../../types/unitsPartner/unitsPartner';
import ConfirmActionModal from '../../../components/modal/specials/ConfirmActionModal';
import CreatePartnerModal from '../components/CreatePartnerModal/CreatePartnerModal';

const { Text } = Typography;

const columns: TableColumnsType<Partner> = [
  {
    title: 'Tên đơn vị DTHT',
    key: 'partnershipName',
    dataIndex: 'partnershipName',
    width: 310,
    render: (_: string, record: Partner) => {
      return <Text>{record?.partnershipName || ''}</Text>;
    },
  },

  {
    title: 'Mã số thuế',
    dataIndex: 'taxCode',
    key: 'taxCode',
    width: 170,
    render: (_: string, record: Partner) => {
      return <Text style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{`MST: ${record?.taxCode}` || ''}</Text>;
    },
  },
  {
    title: 'Mã đơn vị ĐTHT',
    dataIndex: 'partnershipCode',
    width: 180,
    key: 'code',
    render: (_: string, record: Partner) => {
      return <Text>{record?.partnershipCode || ''}</Text>;
    },
  },
  {
    title: 'Người quản lý',
    dataIndex: 'lineManager',
    width: 135,
    key: 'lineManager',
    render: (_: string, record: Partner) => {
      return <Text>{record?.lineManager?.name || ''}</Text>;
    },
  },

  {
    title: 'Trạng thái',
    dataIndex: 'active',
    align: 'center',
    width: 170,
    key: 'active',
    render: (value: boolean) =>
      value ? <Text type="success">Đang hoạt động</Text> : <Text type="danger">Vô hiệu hoá</Text>,
  },
];

const ListUnitsPartner = () => {
  const navigate = useNavigate();

  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [isOpenCreateModal, setIsOpenCreateModal] = useState<boolean>(false);
  const [currentPartner, setCurrentPartner] = useState<Partner>();

  const {
    data: unitPartners,
    isPlaceholderData,
    isLoading,
  } = useFetch<Partner[]>({
    queryKeyArrWithFilter: ['units-partner'],
    api: getListUnitsPartner,
  });

  const columnActions: TableColumnsType<Partner> = useMemo(() => {
    return [
      ...columns,
      {
        key: 'action',
        align: 'center',
        width: 80,
        render: (_, record) => {
          const handleDeleteUnitPartner = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentPartner(record);
          };
          return (
            <ActionsColumns
              handleViewDetail={() => {
                navigate(`${UNIT_PARTNER_MANAGEMENT}/${record.id}`);
              }}
              handleDelete={handleDeleteUnitPartner}
            />
          );
        },
      },
    ];
  }, [isOpenModalDelete, navigate]);

  return (
    <div className="wrapper-listOfUnitsPartner">
      <BreadCrumbComponent />
      <div className="header-content">
        <InputSearch keySearch="search" />
        <Button
          type="primary"
          onClick={() => {
            setIsOpenCreateModal(true);
          }}
        >
          Thêm mới
        </Button>
      </div>
      <TableComponent
        className="table-unit"
        queryKeyArr={['units-partner']}
        columns={columnActions}
        loading={isLoading || isPlaceholderData}
        dataSource={unitPartners?.data?.data?.rows}
      />

      <ConfirmActionModal
        open={isOpenModalDelete}
        apiQuery={deleteUnitsPartner as MutationFunction<unknown, unknown>}
        keyOfListQuery={['units-partner']}
        onCancel={() => setIsOpenModalDelete(false)}
        title="Xóa đối tác hợp tác"
        description="Vui lòng nhập lý do muốn xóa đối tác hợp tác này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Xác nhận"
        fieldNameReason="reasonDelete"
        maxLength={255}
        disable={true}
        payload={{ id: currentPartner?.id }}
        showReasonField={true}
      />

      <CreatePartnerModal
        open={isOpenCreateModal}
        onCancel={() => setIsOpenCreateModal(false)}
        onSuccess={() => {
          // Refresh data if needed
        }}
      />
    </div>
  );
};

export default ListUnitsPartner;
