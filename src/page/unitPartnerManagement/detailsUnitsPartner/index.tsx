import { memo, useEffect } from 'react';
import { Button, Form, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import AddFieldTab from './components/addFieldTab';
import './styles.scss';
import BreadCrumbComponent from '../../../components/breadCrumb';
import DetailsInfoTab from './components/detailsInfoTab';
import PersonnelManagementTab from './components/personnelManagementTab';
import LegalInfoTab from './components/legalInfoTab';
import PartnerManagementTab from './components/partnerManagementTab';
import { useParams } from 'react-router-dom';
import { useFetch, useUpdateField } from '../../../hooks';
import { UNIT_PARTNER_MANAGEMENT } from '../../../configs/path';
import { getDetailUnitsPartner, updateUnitsPartner } from '../../../service/unitsPartner';
import { Partner } from '../../../types/unitsPartner/unitsPartner';
import dayjs from 'dayjs';

function DetailsUnitsPartner() {
  const { id } = useParams();
  const [form] = Form.useForm();

  const { data: unitPartnerData, isRefetching } = useFetch<Partner>({
    api: () => id && getDetailUnitsPartner(id),
    queryKeyArr: ['units-partner', id],
    enabled: !!id,
    cacheTime: 10,
  });

  const update = useUpdateField({
    keyOfListQuery: ['units-partner'],
    label: 'Đối tác hợp tác',
    apiQuery: updateUnitsPartner,
    path: UNIT_PARTNER_MANAGEMENT,
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chi tiết',
      children: <DetailsInfoTab form={form} />,
    },
    {
      key: '2',
      label: 'Thông tin pháp lý',
      children: <LegalInfoTab form={form} />,
    },
    {
      key: '3',
      label: 'Danh sách đơn vị bán hàng đang hợp tác',
      children: <PartnerManagementTab />,
    },
    {
      key: '4',
      label: 'Quản lý nhân viên',
      children: <PersonnelManagementTab form={form} />,
    },
    {
      key: '5',
      label: 'Thêm thông tin trường đơn',
      children: <AddFieldTab form={form} />,
    },
  ];
  const onChange = (key: string) => {
    console.log(key);
  };

  const onUpdateUnitPartner = (values: Partner) => {
    console.log(values);

    // Convert dayjs object back to ISO string for API
    const formattedValues = {
      ...values,
      issueDate: values.issueDate ? dayjs(values.issueDate).toISOString() : '',
    };

    if (id) {
      update.mutate({ ...formattedValues, id: id });
    }
  };

  useEffect(() => {
    if (unitPartnerData?.data?.data && id) {
      const data = unitPartnerData?.data?.data;

      // Convert issueDate from ISO string to dayjs object
      const formattedData = {
        ...data,
        issueDate: data.issueDate ? dayjs(data.issueDate) : dayjs(),
      };

      form.setFieldsValue(formattedData);
    }
  }, [unitPartnerData, id, form]);

  const _detailUnitPartnerData = unitPartnerData?.data?.data || {
    partnershipLevel: '1',
    identityType: 'MST',
  };
  return (
    <div className={'box-detail-units'}>
      <Form
        initialValues={_detailUnitPartnerData}
        form={form}
        name="dynamic_form_nest_item"
        layout="vertical"
        onFinish={values => onUpdateUnitPartner(values)}
      >
        <div className={'box-main-wrap'}>
          <BreadCrumbComponent titleBread={'Gamuda'} />
          <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
        </div>
        <div className="create-footer">
          <div className="button-create">
            <Button type="default">Hủy</Button>
            <Button type="primary" htmlType="submit" loading={isRefetching} style={{ marginLeft: 12 }}>
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
}

export default memo(DetailsUnitsPartner);
