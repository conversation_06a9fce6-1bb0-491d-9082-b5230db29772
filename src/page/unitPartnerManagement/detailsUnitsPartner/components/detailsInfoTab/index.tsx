import { Col, Form, FormInstance, Input, Row, Typography, Select, DatePicker } from 'antd';
import './index.scss';
import dayjs from 'dayjs';
import { useFetch } from '../../../../../hooks';
import { AddressType } from '../../../../../components/selectAddress';
import { getIssuePlaces } from '../../../../../service/address';
import { getListUnits } from '../../../../../service/units';
import { Units } from '../../../../../types/units/units';
import { useEffect } from 'react';

const { Title, Text } = Typography;

interface CommonFormProps {
  form: FormInstance; // Type của Ant Design Form instance
  showTitle?: boolean; // Tùy chọn hiển thị title
}

const DetailsInfoTab: React.FC<CommonFormProps> = ({ form, showTitle = true }) => {
  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-issue-places'],
    api: getIssuePlaces,
  });
  const provinces = dataProvinces?.data?.data;

  const { data } = useFetch<Units[]>({
    queryKeyArrWithFilter: ['get-units-management'],
    api: getListUnits,
  });

  const listUnit = data?.data?.data?.rows || [];

  // Convert issueDate from ISO string to dayjs object when form values change
  useEffect(() => {
    const currentValues = form.getFieldsValue();
    if (currentValues.issueDate && typeof currentValues.issueDate === 'string') {
      form.setFieldsValue({
        ...currentValues,
        issueDate: dayjs(currentValues.issueDate),
      });
    }
  }, [form]);

  return (
    <>
      {showTitle && (
        <Row gutter={[12, 12]}>
          <Col span={24}>
            <Title style={{ marginBottom: '20px' }} level={5}>
              Thông tin quản lý đơn vị
            </Title>
          </Col>
        </Row>
      )}
      <Row gutter={48}>
        <Col span={16}>
          <Row gutter={[32, 8]}>
            <Col span={24}>
              <Form.Item label="Loại đơn vị" name="partnershipLevel">
                <Select options={[{ value: '1', label: 'Đối tác hợp tác ' }]} placeholder="Chọn loại đơn vị" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Tên đơn vị"
                name="partnershipName"
                rules={[{ required: true, message: 'Vui lòng nhập tên đơn vị' }]}
              >
                <Input placeholder="Nhập tên đơn vị" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Tên ngắn" name="short" rules={[{ required: true, message: 'Vui lòng nhập tên ngắn' }]}>
                <Input placeholder="Nhập tên ngắn" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Text>
                Giấy tờ xác minh <span style={{ color: 'red', fontSize: '14px' }}>*</span>
              </Text>
              <Row
                gutter={[8, 8]}
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: '16px 16px 4px 16px',
                  margin: 0,
                }}
              >
                <Col span={6}>
                  <Form.Item
                    label="Loại giấy tờ"
                    name="identityType"
                    rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                  >
                    <Select placeholder="Chọn loại giấy tờ">
                      <Select.Option value="CCCD">Căn cước công dân</Select.Option>
                      <Select.Option value="CMND">CMT</Select.Option>
                      <Select.Option value="MST">Mã số thuế</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Số giấy tờ"
                    name="taxCode"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số giấy tờ',
                      },
                    ]}
                  >
                    <Input placeholder="Nhập số giấy tờ" maxLength={60} />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Ngày cấp"
                    name="issueDate"
                    rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                  >
                    <DatePicker
                      placeholder="Chọn ngày cấp"
                      format="DD/MM/YYYY"
                      disabledDate={current => current && current > dayjs().endOf('day')}
                    />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
                  >
                    <Select
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      allowClear
                      options={provinces?.map(item => ({
                        value: item.code,
                        label: item.nameVN,
                      }))}
                      labelInValue
                      showSearch
                      placeholder="Chọn nơi cấp"
                      onChange={value => {
                        form.setFieldsValue({
                          issueLocation: value || undefined,
                        });
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={24}>
              <Form.Item label="Hợp tác với đơn vị bán hàng" name="partnerCode">
                <Select
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  allowClear
                  options={listUnit?.map(item => ({
                    value: item.code,
                    label: item.nameVN,
                  }))}
                  labelInValue
                  showSearch
                  placeholder="Chọn đơn vị bán hàng"
                  onChange={value => {
                    form.setFieldsValue({
                      partnerCode: value || undefined,
                    });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Col>
        {/* <Col span={8}>
          <Row>
            <Col span={24}>
              <Form.Item name="logo" label="Ảnh đại diện/Logo ĐVBH">
                <Upload
                  action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                  listType="picture-card"
                  fileList={fileList}
                  onPreview={handlePreview}
                  onChange={handleChange}
                >
                  {uploadButton}
                </Upload>
                <Text type="secondary">Định dạng .jpeg, .jpg, .png</Text>
              </Form.Item>
            </Col>
          </Row>
        </Col> */}
      </Row>
    </>
  );
};

export default DetailsInfoTab;
