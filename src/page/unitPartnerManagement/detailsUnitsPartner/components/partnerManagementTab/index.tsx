import { Col, Row, TableColumnsType, Typography } from 'antd';
import './styles.scss';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { useFetch } from '../../../../../hooks';
import { ListCooperativeSalesUnits, PartnerCode } from '../../../../../types/unitsPartner/unitsPartner';
import { getListOfCooperativeSalesUnits } from '../../../../../service/unitsPartner';
const { Text } = Typography;

interface CommonFormProps {
  partnerCode?: PartnerCode;
}

const PartnerManagementTab: React.FC<CommonFormProps> = partnerCode => {
  const currentPartnerCode = partnerCode?.partnerCode;
  const {
    data: listCooperativeSalesUnits,
    isPlaceholderData,
    isLoading,
  } = useFetch<ListCooperativeSalesUnits[]>({
    queryKeyArrWithFilter: ['cooperative-sales-units'],
    api: () => getListOfCooperativeSalesUnits({ partnerCode: currentPartnerCode }),
  });

  const columns: TableColumnsType<ListCooperativeSalesUnits> = [
    {
      title: 'Tên ĐVBH',
      key: 'partnershipName',
      dataIndex: 'partnershipName',
      width: 310,
      render: (_, record) => {
        return <Text>{record?.partnershipName || ''}</Text>;
      },
    },

    {
      title: 'Mã đơn vị',
      width: 180,
      dataIndex: 'partnershipCode',
      key: 'partnershipCode',
      render: (_, record) => {
        return <Text>{record?.partnershipCode || ''}</Text>;
      },
    },

    {
      title: 'Mã số thuế',
      dataIndex: 'taxCode',
      key: 'taxCode',
      width: 210,
      render: (_, record) => {
        return <Text>{record?.taxCode || ''}</Text>;
      },
    },
  ];

  return (
    <>
      <div className="wrapper-listOfUnitsPartner">
        <div className="header-content">
          <InputSearch keySearch="search" />
        </div>
        <Row gutter={24}>
          <Col span={16}>
            <TableComponent
              className="table-unit-partner"
              queryKeyArr={['cooperative-sales-units']}
              loading={isLoading || isPlaceholderData}
              dataSource={listCooperativeSalesUnits?.data?.data}
              columns={columns}
            />
          </Col>
        </Row>
      </div>
    </>
  );
};

export default PartnerManagementTab;
