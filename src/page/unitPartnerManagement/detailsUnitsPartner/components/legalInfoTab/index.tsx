import { Col, Form, FormInstance, Input, Row, Typography, Select, DatePicker, Button } from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import './index.scss';

import { useFetch } from '../../../../../hooks';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import { getIssuePlaces } from '../../../../../service/address';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import Checkbox, { CheckboxChangeEvent } from 'antd/es/checkbox';

const { Title, Text } = Typography;

interface CommonFormProps {
  form: FormInstance;
}

const LegalInfoTab: React.FC<CommonFormProps> = ({ form }) => {
  const [isModified, setIsModified] = useState(false);

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-issue-places'],
    api: getIssuePlaces,
  });
  const provinces = dataProvinces?.data?.data;

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <div className="legal-info-section">
      {/* Thông tin công ty */}
      <Row gutter={32}>
        <Col span={16}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Tên pháp lý"
                name={['partnerCode', 'branchName']}
                rules={[{ required: true, message: 'Vui lòng nhập tên pháp lý' }]}
              >
                <Input placeholder="Nhập tên pháp lý" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Fax" name={['partnerCode', 'contactFax']}>
                <Input placeholder="Nhập số fax" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Giấy CNĐKDN số"
                name={['partnerCode', 'certNumber']}
                rules={[{ required: true, message: 'Vui lòng nhập giấy CNĐKDN số' }]}
              >
                <Input placeholder="Nhập giấy CNĐKDN số" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Số điện thoại" name={['partnerCode', 'contactPhone']}>
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Địa chỉ công ty" name={['partnerCode', 'contactAddress']}>
                <Input placeholder="Nhập địa chỉ công ty" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Địa chỉ email công ty" name={['partnerCode', 'companyEmail']}>
                <Input placeholder="Nhập địa chỉ email công ty" />
              </Form.Item>
            </Col>

            {/* Thông tin thanh toán */}
            <Col span={24}>
              <Title level={5}>Thông tin thanh toán</Title>
            </Col>
            <Col span={24}>
              <Form.Item label="Tài khoản giao dịch">
                <div
                  style={{
                    backgroundColor: 'rgba(0, 0, 0, 0.02)',
                    border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                    padding: 20,
                    margin: 0,
                    marginBottom: 16,
                  }}
                >
                  <Form.List name="bankAccount">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, ...restField }) => (
                          <Row align="middle" gutter={[8, 8]} key={key}>
                            <Col span={10}>
                              <Form.Item
                                {...restField}
                                name={[name, 'bankName']}
                                label="Ngân hàng"
                                rules={[{ required: false }]}
                              >
                                <Select
                                  placeholder="Chọn ngân hàng"
                                  allowClear
                                  filterOption={(input, option) =>
                                    typeof option?.label === 'string'
                                      ? option.label.toLowerCase().includes(input.toLowerCase())
                                      : false
                                  }
                                  showSearch
                                  // loading={isLoadingBanks}
                                  // options={banks.map(item => ({
                                  //   value: item?.bankCode,
                                  //   label: item?.bankName,
                                  // }))}
                                  onChange={(value, option) => {
                                    form.setFieldsValue({
                                      bankAccount: {
                                        [name]: {
                                          bankCode: value,
                                          bankName: Array.isArray(option) ? '' : option?.label || '',
                                          accountNumber:
                                            form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                          beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                        },
                                      },
                                    });
                                  }}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={6}>
                              <Form.Item
                                {...restField}
                                name={[name, 'accountNumber']}
                                label="Số tài khoản"
                                rules={[
                                  {
                                    message: 'Vui lòng nhập số tài khoản',
                                    validator: (_, value) => {
                                      const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                      if (bankCode && !value?.trim()) {
                                        return Promise.reject('Vui lòng nhập số tài khoản');
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <Input placeholder="Nhập số tài khoản" maxLength={20} />
                              </Form.Item>
                            </Col>
                            <Col span={7}>
                              <Form.Item
                                {...restField}
                                name={[name, 'beneciary']}
                                label="Tên người thụ hưởng"
                                rules={[
                                  {
                                    message: 'Vui lòng nhập tên người thụ hưởng',
                                    validator: (_, value) => {
                                      const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                      if (bankCode && !value?.trim()) {
                                        return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                              </Form.Item>
                            </Col>
                            <Col span={1}>
                              <CloseOutlined
                                style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                                onClick={() => {
                                  remove(name);
                                  // handleRemoveBankAccount(name);
                                }}
                              />
                            </Col>
                          </Row>
                        ))}

                        {fields.length < 10 ? (
                          <Col span={23}>
                            <Button
                              type="dashed"
                              onClick={() => {
                                add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                              }}
                              style={{ padding: 0 }}
                              block
                              icon={<PlusOutlined />}
                            >
                              Thêm tài khoản giao dịch
                            </Button>
                          </Col>
                        ) : null}
                      </>
                    )}
                  </Form.List>
                </div>
              </Form.Item>
            </Col>

            {/* Thông tin người đại diện */}
            <Col span={24}>
              <Title level={5}>Thông tin người đại diện</Title>
            </Col>

            <Col span={24}>
              <Form.Item
                label="Đại diện bởi"
                name={['partnerCode', 'representBy']}
                rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
              >
                <Input placeholder="Nhập tên người đại diện" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Số điện thoại" name={['partnerCode', 'representPhone']}>
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Email" name={['partnerCode', 'representEmail']}>
                <Input placeholder="Nhập địa chỉ email" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Text>
                Giấy tờ xác minh <span style={{ color: 'red', fontSize: '14px' }}>*</span>
              </Text>
              <Row
                gutter={[8, 8]}
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: '16px 16px 4px',
                  marginBottom: 16,
                  margin: '10px 0px 16px',
                }}
              >
                <Col span={6}>
                  <Form.Item
                    label="Loại giấy tờ"
                    name="identityType"
                    rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                  >
                    <Select placeholder="Chọn loại giấy tờ">
                      <Select.Option value="CCCD">Căn cước công dân</Select.Option>
                      <Select.Option value="CMND">CMT</Select.Option>
                      <Select.Option value="MST">Mã số thuế</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Số giấy tờ"
                    name="taxCode"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số giấy tờ',
                      },
                    ]}
                  >
                    <Input placeholder="Nhập số giấy tờ" maxLength={60} />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Ngày cấp"
                    name="issueDate"
                    rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                  >
                    <DatePicker
                      placeholder="Chọn ngày cấp"
                      format="DD/MM/YYYY"
                      disabledDate={current => current && current > dayjs().endOf('day')}
                    />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
                  >
                    <Select
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      allowClear
                      options={provinces?.map(item => ({
                        value: item.code,
                        label: item.nameVN,
                      }))}
                      labelInValue
                      showSearch
                      placeholder="Chọn nơi cấp"
                      onChange={value => {
                        form.setFieldsValue({
                          issueLocation: value || undefined,
                        });
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>

            <Col span={24}>
              <Form.Item label="Địa chỉ thường trú người đại diện" name={['address']} className="input-address">
                <SelectAddress
                  parentName={'address'}
                  address={form.getFieldValue('address')}
                  handleAddressChange={validateForm}
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                />
              </Form.Item>
            </Col>
            <Col span={24} className="address">
              <Form.Item name={['address', 'address']}>
                <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Địa chỉ liên lạc người đại diện" name={['rootAddress']} className="input-address">
                <SelectAddress
                  address={form.getFieldValue('rootAddress')}
                  parentName="rootAddress"
                  handleAddressChange={validateForm}
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                />
              </Form.Item>
            </Col>
            <Col span={24} className="address">
              <Form.Item name={['rootAddress', 'address']}>
                <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default LegalInfoTab;
