import { Form, Button } from 'antd';
import { useState } from 'react';
import { useCreateField } from '../../../../hooks';
import { createUnitsPartner } from '../../../../service/unitsPartner';
import { Partner } from '../../../../types/unitsPartner/unitsPartner';
import DetailsInfoTab from '../../detailsUnitsPartner/components/detailsInfoTab';
import dayjs from 'dayjs';
import './styles.scss';
import ModalComponent from '../../../../components/modal';

interface CreatePartnerModalProps {
  open: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

const CreatePartnerModal: React.FC<CreatePartnerModalProps> = ({ open, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const create = useCreateField({
    keyOfListQuery: ['units-partner'],
    label: '<PERSON><PERSON><PERSON> tác hợp tác',
    apiQuery: createUnitsPartner,
  });

  const handleSubmit = async (values: Partner) => {
    setLoading(true);
    try {
      // Convert dayjs object back to ISO string for API
      const createUnitPartnerData = {
        ...values,
        partnershipName: values.partnershipName,
        taxCode: values.taxCode,
        partnershipCode: values.partnershipCode,
        partnershipLevel: '1',
        partnerCode: values.partnerCode,
        logo: '',
        issueDate: values.issueDate ? dayjs(values.issueDate).toISOString() : '',
      };

      create.mutate(createUnitPartnerData);

      // Reset form and close modal on success
      form.resetFields();
      onSuccess?.();
      onCancel();
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const defaultValues = {
    partnershipLevel: '1',
    identityType: 'MST',
    issueDate: dayjs(), // Default to current date
  };

  return (
    <ModalComponent
      className="create-partner-modal"
      title="Tạo mới đối tác hợp tác"
      open={open}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit} initialValues={defaultValues}>
        <DetailsInfoTab form={form} showTitle={false} />

        <div className="create-footer">
          <div className="button-create">
            <Button type="default" onClick={handleCancel}>
              Hủy
            </Button>
            <Button type="primary" htmlType="submit" loading={loading} style={{ marginLeft: 12 }}>
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </ModalComponent>
  );
};

export default CreatePartnerModal;
