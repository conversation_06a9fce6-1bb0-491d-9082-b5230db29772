import { Button, Modal, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import dayjs from 'dayjs';
import { useDeleteField, useFetch } from '../../../hooks';
import { STATUS_LIQUIDATION } from '../../../constants/common';
import { deleteLiquidationContract, getListLiquidation } from '../../../service/liquidation';
import { ILiquidation } from '../../../types/liquidation';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LIQUIDATION_CONTRACT_MANAGEMENT } from '../../../configs/path';
import useFilter from '../../../hooks/filter';
import LiquidationContractModal from './createLiquidation';
import FilterLiquidationContract from './components/filterSearch';

const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YYYY');
  const formattedTime = dayjs(date).format('HH:mm');
  return (
    <>
      <div>{formattedDate}</div>
      <div className="text-time">{formattedTime}</div>
    </>
  );
};

const LiquidationContractManagement = () => {
  const navigate = useNavigate();
  const [isModalCreateLiquidation, setModalCreateLiquidation] = useState(false);
  const [filterParams, setFilterParams] = useState<Record<string, unknown>>({});
  const [filter] = useFilter();

  const combinedFilter = useMemo(
    () => ({
      ...filterParams,
      page: filter.page || '1',
      pageSize: filter.pageSize || '10',
    }),
    [filterParams, filter.page, filter.pageSize],
  );

  const {
    data: liquidation,
    isLoading,
    isPlaceholderData,
    isFetching,
    refetch,
  } = useFetch<ILiquidation[]>({
    queryKeyArrWithFilter: ['get-list-liquidation', combinedFilter],
    defaultFilter: combinedFilter,
    api: getListLiquidation,
    moreParams: combinedFilter,
  });

  const { mutateAsync: remove } = useDeleteField({
    apiQuery: deleteLiquidationContract,
    keyOfDetailQuery: ['get-list-liquidation', combinedFilter],
    label: 'hợp đồng thanh lý',
  });

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: 'Xóa thanh lý hợp đồng',
      content: 'Bạn có chắc muốn xóa thanh lý hợp đồng này không?',
      cancelText: 'Hủy',
      onOk: async () => {
        await remove(id);
        refetch();
      },
      okButtonProps: {
        type: 'default',
      },
      cancelButtonProps: {
        type: 'primary',
      },
    });
  };

  const handleFilterChange = (newFilterParams: Record<string, unknown>) => {
    setFilterParams(newFilterParams);
  };

  const dataLiquidation = liquidation?.data?.data?.rows || [];

  const columns: TableColumnsType<ILiquidation> = [
    {
      title: 'Tên hợp đồng thanh lý',
      dataIndex: 'name',
      key: 'name',
      render: (_, record: ILiquidation) => (
        <a
          onClick={() => navigate(`${LIQUIDATION_CONTRACT_MANAGEMENT}/${record.id}`)}
          style={{ cursor: 'pointer', color: '#1890ff' }}
        >
          {record.name}
        </a>
      ),
    },
    {
      title: 'Loại hợp đồng',
      dataIndex: ['contract', 'type'],
      key: 'type',
      render: (type: string) => {
        switch (type) {
          case 'rent':
            return 'HĐ mua bán-thuê';
          case 'deposit':
            return 'HĐ đặt cọc';
        }
      },
    },
    {
      title: 'Loại thanh lý',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        switch (type) {
          case 'termination':
            return 'Thanh lý chấm dứt';
          case 'transfer':
            return 'Thanh lý chuyển nhượng';
        }
      },
    },
    {
      title: 'Tên hợp đồng',
      dataIndex: ['contract', 'name'],
      key: 'name',
    },
    {
      title: 'Họ tên khách hàng',
      dataIndex: ['customer', 'personalInfo', 'name'],
      key: 'personalInfoName',
    },
    { title: 'Ngày tạo', dataIndex: 'createdDate', key: 'createdDate', render: formatDate, width: 150 },
    {
      title: 'Trạng thái HĐ',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: string) => (
        <span>{STATUS_LIQUIDATION.find(item => item.value === status)?.label || status}</span>
      ),
    },
    {
      width: 100,
      render: (_, record: ILiquidation) =>
        record.status === 'init' && (
          <Button
            style={{ color: '#FF3B30' }}
            type="text"
            onClick={() => {
              handleDelete(record.id);
            }}
          >
            Xóa
          </Button>
        ),
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterLiquidationContract onFilterChange={handleFilterChange} />
        <Button type="default" onClick={() => setModalCreateLiquidation(true)}>
          Tạo mới thanh lý hợp đồng
        </Button>
      </div>
      <TableComponent
        columns={columns}
        dataSource={dataLiquidation}
        rowKey="id"
        queryKeyArr={['get-list-liquidation', combinedFilter]}
        loading={isFetching || isPlaceholderData || isLoading}
        defaultFilter={combinedFilter}
      />
      <LiquidationContractModal
        visible={isModalCreateLiquidation}
        onClose={() => {
          setModalCreateLiquidation(false);
        }}
      />
    </>
  );
};

export default LiquidationContractManagement;
