import { DefaultOptionType } from 'antd/es/select';

export interface AccountTabProps {
  id?: string;
}
export type DataRole = {
  code: string;
  name: string;
  type: string;
  source?: string;
  isDelete?: boolean;
  nameRole?: string;
  children?: DataRole[];
  dataRoles?: DataRole[];
  isAdmSource?: boolean;
};
export type Role = {
  key?: string;
  name?: string;
  code?: string;
  nameRole?: string;
  dataRoles?: DataRole[];
  children?: Role[] | GroupedDataRole[];
  isDelete?: boolean;
  isAdmSource?: boolean;
};

export type Account = {
  id: string;
  accountName?: string;
  isActive?: boolean;
  roles: Role[];
};
export type GroupedDataRole = {
  type: string;
  source?: string;
  key?: string;
  parentCode: string;
  isDelete?: boolean;
  isChildren?: boolean;
  area: Area[];
};
export type Area = {
  labelDataRole: string;
  name: string;
  code: string;
  type: string;
  isDelete?: boolean;
};
export type TypeTree = { code: string; name: string; parentId?: number | string | null };

export type selectObject = {
  type: DefaultOptionType | string;
  listCheck: DataRole[];
};

export type TypeListInternalOrgChart = {
  code: string;
  id: string;
  level: number;
  nameVN: string;
  parentCode: string;
  key: string;
  parentName: string;
  managerName: string;
  children: TypeListInternalOrgChart[];
  partnershipName: string;
  partnershipCode: string;
};

export type DataRoleItem = {
  chartNodeId?: string | null;
  dataType: string;
  active?: boolean;
  softDelete?: boolean;
  _id?: string;
  id?: string;
  account: string;
  companyCode: string;
  companyName: string;
  createdDate?: string;
  modifiedDate?: string;
  __v?: number;
};
