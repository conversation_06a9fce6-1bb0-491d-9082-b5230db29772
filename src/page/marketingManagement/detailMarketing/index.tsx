import { Col, DatePicker, Form, FormProps, Input, Row, Select, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import {
  FORMAT_DATE_API,
  FORMAT_DATE_TIME,
  OPTIONS_MARKETING_STATUS,
  OPTIONS_MARKETING_TYPE,
} from '../../../constants/common';
import { useFetch, useUpdateField } from '../../../hooks';
import { handleErrors } from '../../../service/error/errorsService';

import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import {
  getDetailMarketing,
  getListCostCenter,
  getListCostItem,
  getListProject,
  updateMarketing,
  uploadFile,
} from '../../../service/marketing';
import { IMarketing, TCostCenter, TCostItem } from '../../../types/marketing';
import dayjs from 'dayjs';

import { v4 as uuidv4 } from 'uuid';
import { TProjectDropdown } from '../../../types/common/common';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import UploadAttachments from '../component/uploadAttachments';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const DetailMarketing = () => {
  const { id } = useParams();
  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = useState<IMarketing>();
  const [isModified, setIsModified] = useState(false);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: updateMarketing,
    keyOfListQuery: ['get-marketing'],
    keyOfDetailQuery: ['get-detail-marketing', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const { data: dataProjects } = useFetch<TProjectDropdown[]>({
    queryKeyArrWithFilter: ['get-projects'],
    api: getListProject,
  });

  const { data: dataCostCenters } = useFetch<TCostCenter[]>({
    queryKeyArrWithFilter: ['get-cost-centers'],
    api: getListCostCenter,
  });

  const { data: dataCostItems } = useFetch<TCostItem[]>({
    queryKeyArrWithFilter: ['get-cost-items'],
    api: getListCostItem,
  });

  const { data: dataDetail } = useFetch<IMarketing>({
    api: () => id && getDetailMarketing(id),
    queryKeyArr: ['get-detail-marketing', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const data = dataDetail?.data?.data;

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (data) {
      const initFiles = data?.attachments?.map(item => {
        const link = `${import.meta.env.VITE_S3_IMAGE_URL}/${item}`;
        return {
          name: item,
          url: link,
          uid: uuidv4(),
        };
      });
      const initialData = {
        ...data,
        amount: data?.amount?.toString(),
        project: data?.projectCode,
        costCenter: data?.costCenterCode,
        expense: data?.expenseCode,
        attachments: initFiles,
        startDate: data?.startDate ? dayjs(data?.startDate) : undefined,
        endDate: data?.endDate ? dayjs(data?.endDate) : undefined,
      };
      setInitialValues(initialData as IMarketing);
    }
  }, [data, form]);

  const onFinish: FormProps['onFinish'] = async (values: IMarketing) => {
    const project = dataProjects?.data?.data?.rows?.find(item => item?.code === values?.project);
    const costCenter = dataCostCenters?.data?.data?.rows?.find(item => item?.code === values?.costCenter);
    const costItem = dataCostItems?.data?.data?.rows?.find(item => item?.budgetCode === values?.expense);

    const startDate = form?.getFieldValue('startDate');
    const endDate = form?.getFieldValue('endDate');

    const newData = {
      ...values,
      id: data?.id,
      projectCode: project?.code,
      projectName: project?.name,
      costCenterCode: costCenter?.code,
      costCenterName: costCenter?.fullName,
      expenseCode: costItem?.budgetCode,
      expenseName: costItem?.budgetCostName,
      expense: undefined,
      project: undefined,
      costCenter: undefined,
      amount: Number(values?.amount),
      attachments: values?.attachments?.map(item => item?.fileUrl as string),
      startDate: dayjs(startDate)?.format(FORMAT_DATE_API),
      endDate: dayjs(endDate)?.format(FORMAT_DATE_API),
    };
    const response = await mutateAsync({ ...newData });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <div className="wrapper-detail-personal-proposal">
      <BreadCrumbComponent titleBread={'Xem chi tiết'} />

      {initialValues ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={12}>
                  <Item
                    label="Mã kế hoạch"
                    name="marketingCode"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên kế hoạch'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên kế hoạch" maxLength={255} disabled />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Tên kế hoạch"
                    name="marketingName"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên kế hoạch'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên kế hoạch" maxLength={255} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Mã chủ trương" name="eapMarketingCode">
                    <a href={initialValues?.eapUrl} target="_blank" rel="noopener noreferrer">
                      <Input
                        placeholder="Mã chủ trương"
                        readOnly
                        value={initialValues?.eapMarketingCode}
                        style={{ cursor: 'pointer', color: '#1890ff', backgroundColor: 'rgba(0, 0, 0, 0.04)' }}
                      />
                    </a>
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Loại chủ trương"
                    name="marketingType"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại chủ trương' }]}
                  >
                    <Select placeholder="Chọn loại chủ trương" allowClear options={OPTIONS_MARKETING_TYPE} />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Trạng thái kế hoạch"
                    name="marketingStatus"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại tờ trình' }]}
                  >
                    <Select placeholder="Chọn loại chủ trương" allowClear options={OPTIONS_MARKETING_STATUS} disabled />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã dự án"
                    name="project"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại tờ trình' }]}
                  >
                    <Select
                      placeholder="Chọn mã dự án"
                      allowClear
                      options={dataProjects?.data?.data?.rows?.map((item: TProjectDropdown) => ({
                        key: item?.code,
                        label: item?.name,
                        value: item?.code,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Số tiền đề xuất dự kiến"
                    name="amount"
                    required
                    rules={[{ required: true, message: 'Vui lòng nhập số tiền', whitespace: true }]}
                  >
                    <Input
                      onKeyDown={handleKeyDownEnterNumber}
                      placeholder="Nhập số tiền"
                      maxLength={255}
                      suffix={'VNĐ'}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Số tiền sau khi phê duyệt"
                    name="amount"
                    required
                    rules={[{ required: true, message: 'Vui lòng nhập số tiền', whitespace: true }]}
                  >
                    <Input
                      onKeyDown={handleKeyDownEnterNumber}
                      placeholder="Nhập số tiền"
                      maxLength={255}
                      suffix={'VNĐ'}
                      disabled
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã ngân sách"
                    name="costCenter"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn mã ngân sách' }]}
                  >
                    <Select
                      placeholder="Chọn mã ngân sách"
                      allowClear
                      options={dataCostCenters?.data?.data?.rows?.map((item: TProjectDropdown) => ({
                        key: item?.code,
                        label: item?.name,
                        value: item?.code,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã chi phí"
                    name="expense"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn mã chi phí' }]}
                  >
                    <Select
                      placeholder="Chọn mã chi phí"
                      allowClear
                      options={dataCostItems?.data?.data?.rows?.map((item: TCostItem) => ({
                        key: item?.id,
                        label: item?.name,
                        value: item?.budgetCode,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Form.Item label="Thời gian">
                    <RangePicker
                      value={[form.getFieldValue('startDate'), form.getFieldValue('endDate')]}
                      format="DD/MM/YYYY"
                      onChange={dates => {
                        form.setFieldsValue({
                          startDate: dates?.[0],
                          endDate: dates?.[1],
                        });
                        setIsModified(true);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Nội dung kế hoạch"
                    name="content"
                    required
                    rules={[{ required: true, message: 'Vui lòng nhập nội dung' }]}
                  >
                    <TextArea placeholder="Nhập nội dung" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <div className="info">
                    <Row gutter={24}>
                      <Col lg={6} xs={8}>
                        <Text>Ngày cập nhật: </Text>
                      </Col>
                      <Col lg={18} xs={16}>
                        <Text>
                          {dayjs(data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                          {`${data?.updatedBy?.userName || ''} - ${data?.updatedBy?.fullName || ''}`}
                        </Text>
                      </Col>
                    </Row>
                    <Row gutter={24}>
                      <Col lg={6} xs={8}>
                        <Text>Ngày tạo: </Text>
                      </Col>
                      <Col lg={18} xs={16}>
                        <Text>
                          {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                          {`${data?.createdBy?.userName || ''} - ${data?.createdBy?.fullName || ''}`}
                        </Text>
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5}>File đính kèm</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={24}>
                  <UploadAttachments apiUpload={uploadFile} fieldAttachments="attachments" initValue={data} />
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      ) : (
        <></>
      )}
      <ButtonOfPageDetail
        handleSubmit={isModified ? () => form.submit() : undefined}
        handleCancel={handleCancel}
        loadingSubmit={isPending}
      />
    </div>
  );
};

export default DetailMarketing;
