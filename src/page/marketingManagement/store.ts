import { create } from 'zustand';

interface IMarketingListStore {
  filters: Record<string, unknown>;
  setFilter: (filter: Record<string, unknown>) => void;
  getCurrentFilter: () => Record<string, string>;
}

export const useStoreMarketing = create<IMarketingListStore>((set, get) => ({
  filters: {},
  setFilter: filter =>
    set(state => ({
      filters: {
        ...state.filters,
        ...filter,
      },
    })),
  getCurrentFilter: () => {
    const state = get();
    const filter = state.filters || {};
    const rawFilter = typeof filter === 'object' ? { ...filter } : {};
    return Object.entries(rawFilter)
      .filter(([, value]) => Boolean(value))
      .reduce(
        (acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        },
        {} as Record<string, string>,
      );
  },
}));
