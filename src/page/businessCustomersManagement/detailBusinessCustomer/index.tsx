import './styles.scss';
import { Tabs, TabsProps } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useLocation, useParams, Location, useSearchParams } from 'react-router-dom';
import { useFetch } from '../../../hooks';
import { useState } from 'react';
import GeneralInformation from './components/generalInformation';
import { ICustomers, IDocumentCustomer } from '../../../types/customers';
import {
  addCustomerDocument,
  addItemsCustomerDocument,
  deleteCustomerDocument,
  deleteItemCustomerDocument,
  getCustomerDocument,
  getDetailCustomer,
  getDocumentCustomerItems,
  updateCustomerDocument,
} from '../../../service/customers';
import DocumentComponent from '../../../components/document';

const DetailBusinessCustomer = () => {
  const { id } = useParams();
  const location = useLocation() as Location & { state: { activeTab?: string } };

  const { data: dataDetailBusinessCustomers } = useFetch<ICustomers>({
    api: () => id && getDetailCustomer(id),
    queryKeyArr: ['get-detail-customer-business', id],
    enabled: !!id,
  });

  const dataBusinessCustomer = dataDetailBusinessCustomers?.data?.data;

  const [searchParams, setSearchParams] = useSearchParams();

  const [activeTab, setActiveTab] = useState(location.state?.activeTab || searchParams.get('tab') || '1');
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSearchParams({ tab: key });
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chung',
      children: <GeneralInformation data={dataBusinessCustomer} />,
    },
    {
      key: '2',
      label: 'Tài liệu',
      children: (
        <DocumentComponent<IDocumentCustomer>
          // modelId={id}
          keyQueryList={'getDocument'}
          keyQueryItems={'getDocumentItems'}
          modelIdFieldName={'customer'}
          getDocument={getCustomerDocument}
          getDocumentItems={getDocumentCustomerItems}
          addDocument={addCustomerDocument}
          updateDocument={updateCustomerDocument}
          addItemsDocument={addItemsCustomerDocument}
          deleteDocument={deleteCustomerDocument}
          deleteItemDocument={deleteItemCustomerDocument}
        />
      ),
    },
  ];

  return (
    <div className="wrapper-detail-personal-customer">
      <BreadCrumbComponent titleBread={dataBusinessCustomer?.company?.name} />
      <div className="project_detail-page">
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} tabPosition="top" />
      </div>
    </div>
  );
};

export default DetailBusinessCustomer;
