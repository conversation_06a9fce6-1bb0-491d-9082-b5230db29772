import { Checkbox, Col, DatePicker, Form, FormInstance, Input, Row, Select, SelectProps, Typography } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { FormProps } from 'antd/lib';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import CurrencyInput from '../../../../../components/input/CurrencyInput';
import { OptionType } from '../../../../../components/select/selectEmployees';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import {
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_STATUS_FILTER,
  OPTIONS_STATUS_KHTN_FILTER,
  REGEX_PHONE_VN,
} from '../../../../../constants/common';
import { PERMISSION_DEMAND_CUSTOMER } from '../../../../../constants/permissions/demand';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../../../hooks';
import { getProvinces } from '../../../../../service/address';
import { putUpdateCustomer } from '../../../../../service/customers';
import { handleErrors } from '../../../../../service/error/errorsService';
import { ICustomers, TBankInfo, TDataDuplicate } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import BankInfo from '../../../../personalCustomersManagement/detailPersonalCustomer/components/bankInfo';
import ModalCheckDuplicate from '../../../listBusinessCustomer/components/modalCheckDuplicate';
import '../../styles.scss';
import SkeletonDetail from '../skeletonDetail';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface CommonFormProps {
  data?: ICustomers;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const OPTIONS_IDENTITIES: SelectProps['options'] = [
  {
    label: 'Giấy phép kinh doanh',
    value: 'GPKD',
  },
];

const GeneralInformation: React.FC<CommonFormProps> = ({ data }) => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);

  const [initialValues, setInitialValues] = useState<ICustomers>();
  const [yearOnly, setYearOnly] = useState(false);
  const [Provinces, setProvinces] = useState<SelectProps['options']>([]);
  const [isModified, setIsModified] = useState(false);
  const [valuesShareEmail, setValuesShareEmail] = useState<OptionType[]>();
  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = useState(false);
  const [dataDuplicate, setDataDuplicate] = useState<TDataDuplicate[]>();
  const [dataSubmit, setDataSubmit] = useState<ICustomers>();
  const { customerChangeStatus } = useCheckPermissions(PERMISSION_DEMAND_CUSTOMER);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putUpdateCustomer,
    keyOfListQuery: ['get-customers-business'],
    keyOfDetailQuery: ['get-detail-customer-business', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-province'],
    api: getProvinces,
  });

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (data) {
      const initialData = {
        ...data,
        bankInfo: (data?.bankInfo ?? []).reduce((acc: unknown[], item) => {
          if (item?.code && item?.name && item?.branchCode) {
            acc.push({
              ...item,
              bank: {
                label: item.name,
                value: item.code,
                branchCode: item.branchCode,
                code: item.code,
              },
            });
          }
          return acc;
        }, []),
        info: {
          ...data.info,
          birthday: data?.info?.birthday ? dayjs(data.info.birthday, FORMAT_DATE) : null,
          birthdayYear: data?.info?.birthdayYear ? dayjs(data.info.birthdayYear, 'YYYY') : null,
          onlyYear: data?.info?.birthdayYear ? true : false,
        },
        personalInfo: {
          ...data?.personalInfo,
          income: data?.personalInfo?.income ? data?.personalInfo?.income?.toString() : undefined,
        },
        identities: {
          ...data?.identities,
          type: 'GPKD',
          date: data?.identities?.date ? dayjs(data?.identities.date, FORMAT_DATE) : null,
        },
        company: {
          ...data?.company,
          issueDate: data?.company?.issueDate ? dayjs(data?.company?.issueDate, FORMAT_DATE) : null,
        },
        address: {
          ...data?.info?.address,
        },
        rootAddress: {
          ...data?.info?.rootAddress,
        },
        addressCompany: {
          ...data?.company?.address,
        },
      };
      setInitialValues(initialData as ICustomers);
      setIsModified(false);
      form.setFieldsValue(initialData);
      setYearOnly(!!data.info?.birthdayYear);
      setValuesShareEmail(data?.share?.emails?.map(item => ({ label: item, value: item })) || []);
    }
  }, [data, form]);

  // Lấy danh sách tỉnh thành
  useEffect(() => {
    if (dataProvinces?.data?.data) {
      const provinces = dataProvinces?.data?.data.map(province => ({
        label: province.nameVN,
        value: province.code,
      }));
      setProvinces(provinces);
    }
  }, [dataProvinces?.data?.data]);

  const onFinish: FormProps['onFinish'] = async (values: ICustomers) => {
    const checkChangePhone = form.isFieldTouched(['personalInfo', 'phone']);
    const newData = {
      id: id,
      bankInfo: (values?.bankInfo ?? []).reduce((acc: TBankInfo[], item) => {
        if (item?.bank?.code && item?.bank?.label && item?.bank?.branchCode) {
          acc.push({
            ...item,
            name: item?.bank?.label,
            branchCode: item?.bank?.branchCode,
            code: item?.bank?.code,
            bank: undefined,
          });
        }
        return acc;
      }, []),
      identities: {
        type: values?.identities?.type,
        value: values?.identities?.value,
        date: values?.identities?.date ? dayjs(values?.identities?.date).format(FORMAT_DATE) : null,
        place: values?.identities?.place,
      },
      personalInfo: {
        ...values?.personalInfo,
        income:
          typeof values?.personalInfo?.income === 'string'
            ? parseInt(values?.personalInfo?.income)
            : values?.personalInfo?.income,
        identities: values?.personalInfo?.identities?.map(item => ({
          ...item,
          type: typeof item?.type !== 'string' ? item?.type?.value : item?.type,
          date: typeof item?.date !== 'string' ? item?.date?.format(FORMAT_DATE) : null,
        })),
      },
      info: {
        ...values?.info,
        birthdayYear:
          typeof values?.info?.birthdayYear !== 'string' && values?.info?.birthdayYear
            ? values?.info?.birthdayYear?.format('YYYY')
            : null,
        birthday:
          typeof values?.info?.birthday !== 'string' && !values?.info?.birthdayYear
            ? values?.info?.birthday?.format(FORMAT_DATE)
            : null,
        address: values?.address,
        rootAddress: values?.rootAddress,
      },
      company: {
        ...values?.company,
        issueDate: values?.company?.issueDate ? dayjs(values?.company?.issueDate).format(FORMAT_DATE) : null,
        address: values?.addressCompany,
      },
      share: { emails: valuesShareEmail?.map(item => item?.value) || [] },
      takeNote: values?.takeNote,
      taxCode: values?.taxCode,
      type: 'business',
      isActive: values?.isActive,
    };
    const continueUpdate = !(checkChangePhone && data?.personalInfo?.phone !== values?.personalInfo?.phone);
    const response = await mutateAsync({ ...newData, continueUpdate });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        case 'DCUSE0001':
          setIsOpenCheckDuplicate(true);
          setDataDuplicate(response?.data?.data as TDataDuplicate[]);
          setDataSubmit(newData);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  // const handleChangeListShare = (val: OptionType[]) => {
  //   setValuesShareEmail(val);
  //   setIsModified(true);
  // };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <div className="wrapper-detail-personal-customer">
      {!initialValues ? (
        <SkeletonDetail />
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}></Col>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Item label="Mã số khách hàng" name="code" required>
                <Input placeholder="Mã số khách hàng" disabled />
              </Item>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Form.Item label="Trạng thái hoạt động" name="isActive">
                    <Select
                      placeholder="Chọn trạng thái hoạt động"
                      options={OPTIONS_STATUS_FILTER}
                      disabled={!customerChangeStatus ? true : false}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item label="Trạng thái KHTN" name="status">
                    <Select
                      placeholder="Chọn trạng thái KHTN"
                      allowClear
                      options={OPTIONS_STATUS_KHTN_FILTER}
                      disabled
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    name={['company', 'name']}
                    label="Tên công ty"
                    rules={[{ required: true, message: 'Vui lòng nhập tên công ty', whitespace: true }]}
                  >
                    <Input
                      maxLength={120}
                      placeholder="Nhập tên công ty"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    label="Tên ngắn"
                    name={['company', 'shortName']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên ngắn', whitespace: true }]}
                  >
                    <Input maxLength={25} placeholder="Nhập tên ngắn" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Mã số thuế" name={'taxCode'}>
                    <Input maxLength={15} placeholder="Nhập mã số thuế" />
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-issueDate">
                  <Item label="Ngày cấp mã số thuế" name={['company', 'issueDate']}>
                    <DatePicker format={FORMAT_DATE} placeholder="Nhập ngày cấp mã số thuế" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nơi cấp mã số thuế" name={['company', 'issueLocation']}>
                    <Input maxLength={25} placeholder="Nhập nơi cấp mã số thuế" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    label="Người Đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập người Đại diện', whitespace: true }]}
                  >
                    <Input
                      maxLength={120}
                      placeholder="Nhập người Đại diện"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Chức vụ" name={['personalInfo', 'position']}>
                    <Input maxLength={25} placeholder="Nhập chức vụ" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'phone']}
                    label="Số điện thoại"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số điện thoại',
                      },
                      {
                        pattern: REGEX_PHONE_VN,
                        message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                      },
                    ]}
                  >
                    <Input maxLength={15} onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số điện thoại" />
                  </Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['info', 'onlyYear']}
                    label="Ngày sinh"
                    layout="horizontal"
                    labelCol={{ span: 10 }}
                    labelAlign="left"
                    valuePropName="checked"
                  >
                    <Checkbox
                      checked={yearOnly}
                      style={{ marginLeft: '4px' }}
                      onChange={e => setYearOnly(e.target.checked)}
                    >
                      Chỉ năm sinh
                    </Checkbox>
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-birthday">
                  {yearOnly ? (
                    <Item name={['info', 'birthdayYear']}>
                      <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                    </Item>
                  ) : (
                    <Item name={['info', 'birthday']}>
                      <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                    </Item>
                  )}
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={24}>
                  <Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng', whitespace: true }]}
                  >
                    <Input maxLength={25} placeholder="Nhập địa chỉ email" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Loại giấy tờ" name={['identities', 'type']}>
                    <Select placeholder="Chọn loại giấy tờ" options={OPTIONS_IDENTITIES} />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Số giấy tờ" name={['identities', 'value']}>
                    <Input maxLength={60} placeholder="Nhập số giấy tờ" />
                  </Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Item label="Ngày cấp giấy tờ" name={['identities', 'date']}>
                    <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nơi cấp giấy tờ" name={['identities', 'place']}>
                    <Select
                      placeholder="Chọn nơi cấp"
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label as string).toLowerCase().includes(input.toLowerCase())
                      }
                      options={Provinces}
                    />
                  </Item>
                </Col>
              </Row>

              <Title level={5}>Thông tin tài khoản</Title>
              <Row gutter={24}>
                <BankInfo />
                <Col xs={24} sm={12}>
                  <Item label="Ngành nghề" name={['personalInfo', 'job']}>
                    <Input maxLength={50} placeholder="Nhập ngành nghề" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nguồn thu nhập" name={['personalInfo', 'incomeSource']}>
                    <Input placeholder="VD: công việc hành chính" maxLength={255} />
                  </Item>
                </Col>
                <Col span={24}>
                  <Item label="Thu nhập / tháng (VNĐ)" name={['personalInfo', 'income']}>
                    <CurrencyInput placeholder="Nhập khoảng thu nhập" />
                  </Item>
                </Col>
              </Row>
              {/* <Item name={['share', 'email']} label="Chia sẻ thông tin với người khác">
                <MultiSelectLazy
                  enabled={!!id}
                  queryKey={['getAllDataEmployees']}
                  apiQuery={getListEmployeeInternal}
                  keysLabel={['name', 'email']}
                  keysTag={'email'}
                  placeholder="Chọn nhân viên"
                  handleListSelect={handleChangeListShare}
                />
              </Item> */}

              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
              <Item label="Địa chỉ" name="rootAddress">
                <SelectAddress
                  isDisable={cloneAddress}
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'rootAddress'}
                  address={rootAddress}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['rootAddress', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" disabled={cloneAddress} />
              </Item>

              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
              <Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
              </Item>
              <Item label="Địa chỉ" name={'address'}>
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'address'}
                  address={initialValues?.address}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['address', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5} style={{ marginBottom: '35px' }}>
                Địa chỉ công ty
              </Title>
              <Item label="Địa chỉ" name={'addressCompany'}>
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'addressCompany'}
                  address={initialValues?.addressCompany}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['addressCompany', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>
              <Title level={5}>Ghi chú</Title>
              <Item name="takeNote">
                <TextArea rows={5} maxLength={500} placeholder="Nhập ghi chú nhanh" />
              </Item>
              <div className="info">
                <Row gutter={24} style={{ marginBottom: '10px' }}>
                  <Col lg={6} xs={8}>
                    <Text>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text>
                      {dayjs(data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                      {`${data?.updatedByObj?.username || ''} - ${data?.updatedByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text>
                      {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                      {`${data?.createdByObj?.username || ''} - ${data?.createdByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Form>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
      <ModalCheckDuplicate
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as ICustomers}
        removeDataSubmit={removeDataSubmit}
        type="update"
        setIsModified={setIsModified}
      />
    </div>
  );
};

export default GeneralInformation;
