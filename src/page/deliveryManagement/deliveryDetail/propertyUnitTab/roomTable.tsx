import React, { useState } from 'react';
import 'antd/dist/reset.css';
import './styled.scss';
import { Modal, Tabs, TabsProps } from 'antd';
import { IPropertyUnit } from '../../../../types/delivery';
import { useFetch } from '../../../../hooks';
import { getDetailBlock } from '../../../../service/delivery';
import GeneralInformationTab from './component/detailPropertyUnitTabs/generalInformationTab';
import HistoryTab from './component/detailPropertyUnitTabs/historyTab';
import PropertyTable from '../../component/propertyTable';
interface RoomTableProps {
  listBlocks: {
    block: string;
    floors: string[];
    rooms: string[];
  }[];
  isPendingCreate?: boolean;
  listPropertyUnit: IPropertyUnit[];
  isLoadingDataTable?: boolean;
}

const CustomTable: React.FC<RoomTableProps> = ({
  listBlocks,
  listPropertyUnit,
  isPendingCreate,
  isLoadingDataTable,
}) => {
  const [selectedProduct, setSelectedProduct] = useState<IPropertyUnit | null>(null);

  const { data: dataDetailBlock } = useFetch<IPropertyUnit>({
    queryKeyArr: ['detail-block'],
    api: () => getDetailBlock(selectedProduct?.id),
    withFilter: false,
    enabled: !!selectedProduct?.id,
    cacheTime: 10,
  });

  const dataBlock = dataDetailBlock?.data?.data;

  const [tab, setTab] = useState('general-info');

  const handleCellClick = (floor: string, room: string, block: string) => {
    const propertyUnit = listPropertyUnit.find(
      item => item.floor === floor && item.shortCode === room && item.block === block,
    );
    if (propertyUnit) {
      setSelectedProduct(propertyUnit);
    }
  };

  const handleChangeTab = (activeKey: string) => {
    setTab(activeKey);
  };

  const items: TabsProps['items'] = [
    {
      label: 'Thông tin chung',
      key: 'general-info',
      children: <GeneralInformationTab selectedProduct={dataBlock as IPropertyUnit} />,
    },
    { label: 'Lịch sử', key: 'history', children: <HistoryTab selectedProduct={dataBlock as IPropertyUnit} /> },
  ];

  return (
    <>
      <PropertyTable
        listBlocks={listBlocks}
        listPropertyUnit={listPropertyUnit}
        onCellClick={handleCellClick}
        isPendingCreate={isPendingCreate}
        isLoadingDataTable={isLoadingDataTable}
      />
      <Modal
        className="product-detail-modal"
        open={!!selectedProduct}
        title={`Sản phẩm ${selectedProduct?.code}`}
        onCancel={() => setSelectedProduct(null)}
        footer={null}
        width={636}
      >
        {selectedProduct && <Tabs items={items} activeKey={tab} onChange={handleChangeTab} />}
      </Modal>
    </>
  );
};

export default CustomTable;
