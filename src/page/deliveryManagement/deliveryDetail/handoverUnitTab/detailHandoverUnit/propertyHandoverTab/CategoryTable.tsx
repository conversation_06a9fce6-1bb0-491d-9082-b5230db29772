import { Button, Checkbox, Col, Form, notification, Row, Table, TableProps, Upload } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import { TAttachment, TDeliveryItem } from '../../../../../../types/delivery';
import { RcFile } from 'antd/lib/upload';
import { ALLOWED_ATTACHMENT_EXTENSIONS } from '../../../../../../constants/common';
import { uploadFile } from '../../../../../../service/delivery';
import { AxiosError } from 'axios';
import { FormInstance } from 'antd/lib';

import { v4 as uuidv4 } from 'uuid';

interface CategoryTableProps {
  dataSource?: TDeliveryItem[];
  form?: FormInstance;
  fieldParent: string;
}

const CategoryTable = (props: CategoryTableProps) => {
  const { dataSource, form, fieldParent } = props;

  const [files, setFiles] = useState<TAttachment[]>();

  const [fileList, setFileList] = useState<TAttachment[]>([]); //Use to check Error and LoadingTable

  const [isLoadingUpload, setIsLoadingUpload] = useState<boolean>(false);

  const columnActions: TableProps['columns'] = useMemo(
    () => [
      {
        title: 'Hạng mục',
        dataIndex: 'name',
        width: '25%',
        key: 'name',
        render: value => value ?? value,
      },
      {
        title: 'Trạng thái hoàn thành',
        width: '35%',
        dataIndex: 'title',
        key: 'title',
        render: (value: string, record: TDeliveryItem, index: number) => {
          if (record?.list) {
            return null;
          }

          return (
            <Form.Item
              valuePropName="checked"
              name={[fieldParent, record?.parentIndex as string, 'list', index, 'isPass']}
            >
              <Checkbox>{value ?? ''}</Checkbox>
            </Form.Item>
          );
        },
      },
    ],
    [fieldParent],
  );

  useEffect(() => {
    form?.setFieldsValue({ files: files });
  }, [files, form]); // Cập nhật Form khi files thay đổi

  const handleBeforeUpload = async (file: RcFile, fileList: RcFile[]) => {
    if (fileList[0] === file) {
      setIsLoadingUpload(true);
    }

    const fileName = file.name.toLowerCase();
    const isAllowedType = ALLOWED_ATTACHMENT_EXTENSIONS.some(ext => fileName.endsWith(ext));

    // Tổng số lượng file sau khi upload
    const totalFiles = (files?.length || 0) + fileList?.length;

    if (totalFiles > 10) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tải lên vượt quá 10 file!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check tổng dung lượng tất cả file
    const currentTotalSize =
      (files?.reduce((acc, attachment) => acc + (attachment?.file?.size || 0), 0) || 0) +
      fileList.reduce((acc, file) => acc + (file?.size || 0), 0);

    const totalSizeMB = currentTotalSize / (1024 * 1024); // Byte -> MB
    if (totalSizeMB > 1024) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tổng dung lượng vượt quá 1GB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check dung lượng từng file
    const fileSizeMB = file?.size / (1024 * 1024); // Byte -> MB

    if (fileSizeMB > 100) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Kích thước file "${file?.name}" vượt quá 100MB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (!isAllowedType) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message:
            'File không đúng định dạng. Vui lòng sử dụng file .png, .jpg, .jpeg, .mp4, .avi, .mov, .wmv, .xls, .xlsx, .doc, .docx, .pdf, .ppt, .pptx, .jfif, .rar, .zip, .msg hoặc .txt',
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (fileList[fileList.length - 1] === file) {
      setFileList(fileList);
    }

    return true;
  };

  return (
    <>
      <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
        <Col xs={24} md={24} style={{ marginBottom: '24px' }}>
          <Table
            className="handover-table"
            columns={columnActions}
            dataSource={dataSource}
            pagination={false}
            rowKey="id"
            expandable={{
              expandedRowKeys: dataSource?.map(item => item?.id),
              expandIcon: () => null,
            }}
          />
        </Col>
        <Col xs={24} md={24}>
          <Form.Item
            name="files"
            label="Tài liệu liên quan"
            valuePropName="fileList"
            getValueFromEvent={e => {
              return Array.isArray(e) ? e : e?.fileList;
            }}
          >
            <Upload
              beforeUpload={handleBeforeUpload}
              maxCount={10}
              multiple
              customRequest={async ({ file, onSuccess, onError }) => {
                try {
                  const fileData = file as RcFile;
                  const resp = await uploadFile([fileData]);
                  const data = resp.data.data;

                  if (data) {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const newFiles = {
                      name: data?.originalname,
                      url: data?.Location,
                      id: data?.uid || uuidv4(),
                      uid: data?.uid || uuidv4(),
                      key: data?.key,
                    };

                    setFiles(prev => {
                      const updatedAttachments = [...(prev || []), newFiles];
                      form?.setFieldValue('files', updatedAttachments);
                      return updatedAttachments;
                    });
                    // form?.setFieldValue('files', files ? [...files, newFiles] : [newFiles]);
                  }
                  const lastFile = fileList[0];

                  if (fileData.uid === lastFile.uid) {
                    setIsLoadingUpload(false);
                  }
                  // setIsModified(true);

                  onSuccess?.('ok');
                } catch (error: unknown) {
                  setIsLoadingUpload(false);
                  onError?.(error as AxiosError);
                }
              }}
              onRemove={file => {
                const newList = files?.filter(f => f.uid !== file.uid);
                setFiles(newList);
                form?.setFieldValue('files', newList);
                return true;
              }}
              fileList={files?.map(o => {
                const newFile = { ...o, uid: o?.uid ?? '1' };
                return newFile as RcFile;
              })} // gán với fileList
              name="file-upload"
            >
              <Button icon={<UploadOutlined />} disabled={isLoadingUpload}>
                Upload
              </Button>
            </Upload>
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default CategoryTable;
