import React from 'react';
import { Spin, Typography } from 'antd';
import { VariableSizeGrid as Grid } from 'react-window';
import 'antd/dist/reset.css';
import './styles.scss';
import { IPropertyUnit } from '../../../../types/delivery';
const { Title } = Typography;

interface RoomTableProps {
  listBlocks: {
    block: string;
    floors: string[];
    rooms: string[];
  }[];
  isPendingCreate?: boolean;
  listPropertyUnit: IPropertyUnit[];
  isLoadingDataTable?: boolean;
  onCellClick?: (floor: string, room: string, block: string, propertyUnit?: IPropertyUnit) => void;
}

const getStatusColorClass = (status: string): string => {
  const statusClasses: { [key: string]: string } = {
    XD06: 'status-xd06',
    XD04: 'status-xd04',
    XD08: 'status-xd08',
    XD09: 'status-xd09',
    XD10: 'status-xd10',
    XD11: 'status-xd11',
  };

  return statusClasses[status] || 'status-default';
};

const PropertyTable: React.FC<RoomTableProps> = ({
  listBlocks,
  listPropertyUnit,
  isPendingCreate,
  isLoadingDataTable,
  onCellClick,
}) => {
  const handleCellClick = (floor: string, room: string, block: string) => {
    const propertyUnit = listPropertyUnit.find(
      item => item.floor === floor && item.shortCode === room && item.block === block,
    );

    if (onCellClick) {
      onCellClick(floor, room, block, propertyUnit);
    }
  };

  return (
    <div className="custom-table-delivery-room">
      {listBlocks.length > 0 &&
        listBlocks.map(({ block, floors, rooms }) => {
          const rowCount = floors.length + 1;
          const columnCount = rooms.length + 1;
          const blockWidth = Math.min(columnCount * 45 + 8, 1200);

          return (
            <Spin key={block} spinning={isLoadingDataTable || isPendingCreate}>
              <div style={{ marginBottom: 20, overflowX: 'auto' }}>
                <Title level={5} className="name-block">
                  Block {block}
                </Title>
                {React.createElement(Grid as never, {
                  className: 'virtual-table',
                  columnCount,
                  columnWidth: () => 45,
                  height: 600,
                  rowCount,
                  rowHeight: () => 50,
                  width: blockWidth,
                  children: ({
                    columnIndex,
                    rowIndex,
                    style,
                  }: {
                    columnIndex: number;
                    rowIndex: number;
                    style: React.CSSProperties;
                  }) => {
                    if (rowIndex === 0 && columnIndex === 0) {
                      return <div style={style} className="virtual-table-cell header" />;
                    }
                    if (rowIndex === 0) {
                      return (
                        <div style={style} className="virtual-table-cell header">
                          {rooms[columnIndex - 1]}
                        </div>
                      );
                    }
                    if (columnIndex === 0) {
                      return (
                        <div style={style} className="virtual-table-cell header">
                          {floors[rowIndex - 1]}
                        </div>
                      );
                    }

                    const floor = floors[rowIndex - 1];
                    const room = rooms[columnIndex - 1];
                    const propertyUnit = listPropertyUnit.find(
                      item => item.floor === floor && item.shortCode === room && item.block === block,
                    );
                    const statusClass = propertyUnit ? getStatusColorClass(propertyUnit?.colorSyncErp as string) : '';

                    return (
                      <div
                        style={{ ...style, cursor: 'pointer' }}
                        className={`virtual-table-cell ${statusClass}`}
                        onClick={() => handleCellClick(floor, room, block)}
                      >
                        {propertyUnit?.shortCode || ''}
                      </div>
                    );
                  },
                })}
              </div>
            </Spin>
          );
        })}
    </div>
  );
};

export default PropertyTable;
