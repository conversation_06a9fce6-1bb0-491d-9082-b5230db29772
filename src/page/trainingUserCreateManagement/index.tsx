import { Col, Form, Input, Row, Select, Typography } from 'antd';
import BreadCrumbComponent from '../../components/breadCrumb';
import { useCreateField, useFetch } from '../../hooks';
import React, { Key } from 'react';
import { ARRAY_FIELD_CREATE_TRAINING_USER, columnCreate, columnsTrainingUser } from './component/columns';
import { TableRowSelection } from 'antd/lib/table/interface';
import {
  createTrainingUser,
  getEvents,
  getListCustomer,
  getRegisterEventCurrentUser,
} from '../../service/traingingUserCreate';
import { TCustomer, TEvent } from '../../types/trainingUserCreate';
import ListCreateTrainingUser from './component/ListCreateTrainingUser';
import { v4 as uuid } from 'uuid';
import ButtonOfPageDetail from './component/buttonOfPageDetail';
import TableComponent from '../../components/table';

const { Item } = Form;
const { Title } = Typography;

const TrainingUserCreate = () => {
  const [form] = Form.useForm();

  const [changeEvent, setChangeEvent] = React.useState<boolean>(false);

  const [eventSelected, setEventSelected] = React.useState<TEvent>();
  const [searchText, setSearchText] = React.useState('');

  const [additionalList, setAdditionalList] = React.useState<TCustomer[]>([]);
  const [userRegistedList, setUserRegistedList] = React.useState<TCustomer[]>([]);

  const [selectedRows, setSelectedRows] = React.useState<TCustomer[]>([]);

  const [selectedRowKeys, setSelectedRowKeys] = React.useState<Key[]>([]);

  const { data: dataEvent } = useFetch<TEvent[]>({
    queryKeyArrWithFilter: ['get-event'],
    api: getEvents,
  });

  const { data: dataUserRegistedList } = useFetch<TCustomer[]>({
    queryKeyArrWithFilter: ['get-user-registed', eventSelected?.id],
    api: getRegisterEventCurrentUser,
    moreParams: { idEvent: eventSelected?.id },
    enabled: !!eventSelected?.id,
  });

  const { data: dataCustomerList } = useFetch<TCustomer[]>({
    queryKeyArrWithFilter: ['get-customer'],
    api: getListCustomer,
  });

  const { mutateAsync: createTrainingUserList } = useCreateField<TCustomer[]>({
    apiQuery: createTrainingUser,
    keyOfListQuery: ['get-training-user', eventSelected?.id],
    checkDuplicate: false,
  });
  const userList = dataCustomerList?.data?.data?.rows?.map(item => ({ ...item, key: item?.id || '' }));
  const userRegisted = React.useMemo(
    () => dataUserRegistedList?.data?.data?.map(item => ({ ...item, key: item?.id || '' })) || [],
    [dataUserRegistedList?.data?.data],
  );

  React.useEffect(() => {
    const mergedList = [...additionalList, ...selectedRows, ...userRegistedList].filter(
      (item, index, array) => array.findIndex(row => row.id === item.id) === index,
    );

    form.setFieldValue('customers', mergedList);
  }, [additionalList, form, selectedRows, userRegisted, userRegistedList]);
  // // Runs whenever either list updates

  React.useEffect(() => {
    if (dataUserRegistedList && changeEvent) {
      setUserRegistedList(userRegisted);
      form?.setFieldValue('customers', userRegisted);
      setChangeEvent(false);
    }
  }, [additionalList, changeEvent, dataUserRegistedList, form, selectedRows, userRegisted]);

  const listEvent = dataEvent?.data?.data;

  const filteredData = React.useMemo(() => {
    const normalize = (str: string = '') => str.toLowerCase().replace(/\s+/g, ' ').trim(); // chuẩn hóa: viết thường + loại bỏ khoảng trắng thừa

    const normalizedSearch = normalize(searchText);
    return userList
      ? userList.filter(item => {
          const code = normalize(item?.code);
          const name = normalize(item?.name);

          return code.includes(normalizedSearch) || name.includes(normalizedSearch);
        })
      : [];
  }, [userList, searchText]);

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: TCustomer[]) => {
    setSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<TCustomer> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: TCustomer[]) => onSelectChange(selectedRowKeys, selectedRows),
    onSelectAll: (selected, selectedRows) => {
      if (selected) {
        // Handle Select All Logic
        setSelectedRows(selectedRows);
        setSelectedRowKeys(selectedRows.map(row => row.key));
      } else {
        // Handle Deselect All Logic
        setSelectedRowKeys([]);
        setSelectedRows([]);
      }
    },
  };

  const handleChangeSelectEvent = React.useCallback(
    (value: string) => {
      const event = listEvent?.find(o => o?.id === value);
      setEventSelected(event);
      setUserRegistedList([]);
      setChangeEvent(true);
    },
    [listEvent],
  );

  const handleSubmit = async () => {
    await form.validateFields();
    const values = form.getFieldsValue(true);
    const customers = values?.customers;
    const newData = customers?.map((o: TCustomer) => ({ ...o, idEvent: eventSelected?.id }) as unknown as TCustomer);
    const res = await createTrainingUserList(newData as TCustomer[]);
    if (Number(res?.data?.statusCode) === 0) {
      console.log('');
    }
  };

  const handleAddList = () => {
    const newData = {
      ...ARRAY_FIELD_CREATE_TRAINING_USER.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      id: uuid(),
      isNew: true,
    } as unknown as TCustomer;
    setAdditionalList(prev => [newData, ...prev]);
  };

  const handleSaveList = (row: TCustomer) => {
    setSelectedRows(prev => prev.map(item => (item.key === row.key ? { ...item, ...row } : item)));

    setAdditionalList(prev => prev.map(item => (item.key === row.key ? { ...item, ...row } : item)));

    setUserRegistedList(prev => prev.map(item => (item.key === row.key ? { ...item, ...row } : item)));

    const mergedData = [...selectedRows, ...additionalList, ...userRegistedList].map(item =>
      item.key === row.key ? { ...item, ...row } : item,
    );

    form.setFieldsValue({ customers: mergedData });
  };

  const handleDeleteList = (key: Key) => {
    setSelectedRows(prev => prev.filter(item => item.key !== key));
    setSelectedRowKeys(prev => prev.filter(item => item !== key));
    setAdditionalList(prev => prev.filter(item => item.key !== key));
    setUserRegistedList(prev => prev.filter(item => item.key !== key));

    const filteredData = [...selectedRows, ...additionalList, ...userRegistedList].filter(item => item.key !== key);

    form.setFieldsValue({ customers: filteredData });
  };

  return (
    <div className="wrapper-detail-personal-proposal">
      <BreadCrumbComponent />
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        // initialValues={{ custormers: currentSelectedRows }}
        className="space-y-6"
      >
        <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
          <Col span={24}></Col>
          <Col xs={24} md={12}>
            <Title level={5}>Tên sự kiện</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24}>
                <Item
                  label="Tên sự kiện"
                  name="idEvent"
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn sự kiện' }]}
                >
                  <Select
                    placeholder={'Chọn sự kiện'}
                    onChange={handleChangeSelectEvent}
                    options={listEvent?.map(o => ({ ...o, value: o?.id, label: o?.name }))}
                  />
                </Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách mời</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24}>
                <Input.Search
                  placeholder="Tìm kiếm thông tin khách mời"
                  allowClear
                  onSearch={value => setSearchText(value)}
                />
              </Col>
              {/* </Row> */}
              <Col xs={24} md={24} style={{ marginTop: '8px' }}>
                <TableComponent
                  queryKeyArr={['get-customer']}
                  rowSelection={rowSelection}
                  columns={columnsTrainingUser}
                  dataSource={filteredData}
                  rowKey={'id'}
                  key={'id'}
                />
              </Col>
            </Row>
          </Col>
          <Col xs={24} md={12}>
            <Title level={5}>Tạo mới khách mời</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24} md={24}>
                <ListCreateTrainingUser<TCustomer>
                  labelAdd={'Thêm khách hàng'}
                  nameField="customers"
                  columns={columnCreate}
                  dataSource={[...additionalList, ...selectedRows, ...userRegistedList]?.map(o => ({
                    ...o,
                    event: eventSelected,
                  }))}
                  arrayFieldTable={ARRAY_FIELD_CREATE_TRAINING_USER}
                  setDataSource={undefined}
                  handleAdd={handleAddList}
                  handleSave={handleSaveList}
                  handleDelete={handleDeleteList}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
      {true && (
        <ButtonOfPageDetail
          handleSubmit={() => form?.submit()}
          // loadingSubmit={isPending}
        />
      )}
    </div>
  );
};
export default TrainingUserCreate;
