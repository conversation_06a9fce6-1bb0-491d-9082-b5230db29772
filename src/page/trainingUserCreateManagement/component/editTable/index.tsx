import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, InputProps, SelectProps, Table } from 'antd';
import { Rule } from 'antd/es/form';
import { FormInstance, TableProps } from 'antd/lib';
import { TableComponents } from 'rc-table/lib/interface';
import './styles.scss';
import { useTrainingUserCreateStore } from '../../store';

export interface EditableRecord {
  key: string;
  id?: string;
  isNew?: boolean;
}

export type ColumnTypes<T> = Exclude<TableProps<T>['columns'], undefined>;

interface EditableProps<T> {
  components: TableComponents<T>;
  dataSource: T[];
  columns: (ColumnTypes<T>[number] & EditColumns)[];
  handleAdd: () => void;
  handleDelete: (key: React.Key) => void;
  handleSave: (record: T) => void;
  labelAdd?: string;
  collectFormRow: (form: FormInstance, index: number) => void;
  isLoading: boolean;
}

export interface EditColumns {
  editable?: boolean;
  dataIndex: string;
  inputType?: 'text' | 'select' | 'custom' | 'number';
  optionsSelect?: SelectProps['options'];
  rules?: Rule[] | ((record: unknown) => Rule[]);
  renderEditComponent?: (props: {
    value: unknown;
    onChange: (value: unknown) => void;
    onPressEnter?: () => void;
    onBlur?: () => void;
    save: () => void;
    disabled?: boolean;
  }) => React.ReactNode;
  inputProps?: Omit<InputProps, 'onPressEnter' | 'onBlur'>;
  selectProps?: Omit<SelectProps, 'onChange' | 'onBlur' | 'options'>;
}

export const Editable = <T extends EditableRecord>(props: EditableProps<T>) => {
  const { components, columns, dataSource, handleAdd, handleDelete, handleSave, labelAdd, collectFormRow, isLoading } =
    props;
  const { disabled, setIsModified } = useTrainingUserCreateStore();

  const actionsColumns: (ColumnTypes<T>[number] & EditColumns)[] = [
    ...columns,
    {
      dataIndex: 'action',
      align: 'center',
      width: '10%',
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Button
            type="link"
            danger
            onClick={() => {
              handleDelete(record?.key);
              setIsModified(true);
            }}
            disabled={disabled}
          >
            <DeleteOutlined />
          </Button>
        ) : null,
    },
  ];

  // Map columns to include inputType for EditableCell
  const columnsEdit = actionsColumns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: T) => ({
        record,
        editable: col?.editable,
        dataIndex: col?.dataIndex,
        title: col?.title,
        handleSave,
        inputType: col?.inputType,
        isNewRow: record?.isNew,
        optionsSelect: col?.optionsSelect,
        renderEditComponent: col?.renderEditComponent,
        rules: typeof col?.rules === 'function' ? col?.rules(record) : col.rules,
        inputProps: col?.inputProps,
        selectProps: col?.selectProps,
      }),
    };
  });

  return (
    <div>
      <Table
        onRow={(_record, index) => {
          return {
            index, // custom prop truyền vào EditableRow
            collectForm: collectFormRow,
          } as React.HTMLAttributes<HTMLTableRowElement> & { index?: string };
        }}
        components={components}
        className={`editable ${dataSource.length === 0 ? 'editable-empty' : ''}`}
        dataSource={dataSource}
        columns={columnsEdit as ColumnTypes<T>}
        sticky={{ offsetHeader: 0 }}
        summary={() => (
          <Table.Summary fixed="top">
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={columns.length}>
                <Button type="default" icon={<PlusOutlined />} onClick={handleAdd} disabled={disabled}>
                  {labelAdd}
                </Button>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
        locale={{ emptyText: null }}
        pagination={false}
        footer={() => <strong>Khách hàng được thêm: {dataSource.length}</strong>}
        loading={isLoading}
      />
    </div>
  );
};
