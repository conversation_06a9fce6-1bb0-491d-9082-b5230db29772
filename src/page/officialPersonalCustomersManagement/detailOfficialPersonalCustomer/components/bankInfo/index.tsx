import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, Input, Row, Select } from 'antd';
import isArray from 'lodash/isArray';
import { IBank, OptionTypeSelect } from '../../../../../types/common/common';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import { useFetch } from '../../../../../hooks';
import { getBanks } from '../../../../../service/bank';
import { useMemo } from 'react';
const { Item, List } = Form;

const BankInfo = () => {
  const form = Form.useFormInstance();
  const banks = Form.useWatch('bankInfo', form);

  const { data: dataBanks } = useFetch<IBank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });

  const bankOptions = useMemo(() => {
    const options: OptionTypeSelect[] = [];
    dataBanks?.data?.data?.forEach(bank => {
      options.push({
        value: bank.id,
        label: bank.bankName,
        branchCode: bank?.bankCode,
        code: bank?.bankCode,
      });
    });
    return options;
  }, [dataBanks?.data?.data]);

  const handleChangeBank = (option: OptionTypeSelect | OptionTypeSelect[], name: number) => {
    const selectedOption = isArray(option) ? option[0] : option;
    form.setFieldValue(['bankInfo', name, 'bank'], {
      label: selectedOption?.label,
      code: selectedOption?.code,
      branchCode: selectedOption?.branchCode,
      value: selectedOption?.value,
    });
  };

  return (
    <Col span={24} style={{ marginBottom: '16px' }}>
      <p style={{ marginBottom: 8 }}> Tài khoản giao dịch</p>
      <Card size="small" style={{ backgroundColor: '#********' }}>
        <List name="bankInfo">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Row key={key} gutter={8} align={'middle'}>
                  <Col span={7}>
                    <Item {...restField} label="Ngân hàng" name={[name, 'bank']} style={{ flex: 1 }}>
                      <Select
                        placeholder="Chọn ngân hàng"
                        allowClear
                        filterOption={(input, option) =>
                          (option?.label as string).toLowerCase().includes(input.toLowerCase())
                        }
                        options={bankOptions}
                        labelInValue
                        showSearch
                        style={{ width: '100%' }}
                        onChange={(_: string, option: OptionTypeSelect | OptionTypeSelect[]) => {
                          handleChangeBank(option, name);
                        }}
                      />
                    </Item>
                  </Col>
                  <Col span={7}>
                    <Item {...restField} label="Số tài khoản" name={[name, 'accountNumber']}>
                      <Input maxLength={20} placeholder="Nhập số tài khoản" onKeyDown={handleKeyDownEnterNumber} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      {...restField}
                      label="Tên người thụ hưởng"
                      name={[name, 'beneficiary']}
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const accountNumber = getFieldValue(['bankInfo', name, 'accountNumber']);
                            if (!accountNumber || value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error('Vui lòng nhập tên người thụ hưởng'));
                          },
                        }),
                      ]}
                    >
                      <Input maxLength={255} placeholder="Nhập tên người thụ hưởng ngân hàng" />
                    </Item>
                  </Col>
                  <Col span={2}>
                    <CloseOutlined onClick={() => remove(name)} style={{ marginTop: '14px' }} />
                  </Col>
                </Row>
              ))}
              {banks?.length < 10 ? (
                <Col span={22} style={{ padding: 0 }}>
                  <Button type="dashed" onClick={() => add()} style={{ padding: 0 }} block icon={<PlusOutlined />}>
                    Thêm tài khoản giao dịch
                  </Button>
                </Col>
              ) : null}
            </>
          )}
        </List>
      </Card>
    </Col>
  );
};

export default BankInfo;
