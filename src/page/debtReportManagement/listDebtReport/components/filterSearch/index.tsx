import { Form, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import {
  FORMAT_DATE_API,
  OPTIONS_STATUS_DEBT_REMINDER,
  OPTIONS_TYPE_PRODUCT_DEBT_REPORT,
} from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';

import './styles.scss';
import DatePickerFilter from './DatePickerFilter';
import { TProject } from '../../../../../types/debtReport';
import { getListOfProject } from '../../../../../service/debtReport';

type TFilter = {
  isActive?: number | null;
  dueFrom?: string | Dayjs | null;
  dueTo?: string | Dayjs | null;
  paidFrom?: string | Dayjs | null;
  paidTo?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  type?: string;
  projectIds?: string[];
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  useEffect(() => {
    const formatData = {
      status: params.get('status') || undefined,
      isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      dueFrom: values?.dueFrom ? dayjs(values?.dueFrom).format(FORMAT_DATE_API) : null,
      dueTo: values?.dueTo ? dayjs(values?.dueTo).format(FORMAT_DATE_API) : null,
      paidFrom: values?.paidFrom ? dayjs(values?.paidFrom).format(FORMAT_DATE_API) : null,
      paidTo: values?.paidTo ? dayjs(values?.paidTo).format(FORMAT_DATE_API) : null,
      projectIds: values?.projectIds,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectProjects = (values: TProject[]) => {
    console.log('values :', values);
  };

  const handleChangeSearch = (e: unknown) => {
    const searchTerm = typeof e === 'string' ? e : '';
    setFilter({ ...filter, page: '1', search: searchTerm });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setIsOpenFilter(false);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        defaultValueSearch={params.get('search') || ''}
        keyInputSearch={`search`}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleChangeSearch}
        extraFormItems={
          <>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListOfProject}
                queryKey={['project-dropdown']}
                keysLabel={['code', 'name']}
                handleListSelect={handleSelectProjects}
                placeholder="Chọn dự án"
                keysTag={'code'}
              />
            </Form.Item>
            <Form.Item label="Loại sản phẩm" name="propertyTypes">
              <Select
                mode="multiple"
                placeholder="Chọn loại sản phẩm"
                allowClear
                options={OPTIONS_TYPE_PRODUCT_DEBT_REPORT}
              />
            </Form.Item>
            <Form.Item label="Trạng thái nhắc nợ" name="debtReminderStatus">
              <Select mode="multiple" placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_DEBT_REMINDER} />
            </Form.Item>

            <Form.Item label="Ngày dự kiến thanh toán">
              <DatePickerFilter startDate="dueFrom" endDate="dueTo" />
            </Form.Item>

            <Form.Item label="Ngày thanh toán">
              <DatePickerFilter startDate="paidFrom" endDate="paidTo" />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
