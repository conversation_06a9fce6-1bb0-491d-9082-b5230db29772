import { Form, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { DEFAULT_PARAMS, FORMAT_DATE_API, OPTIONS_HISTORY_EVOUCHER_STATUS } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';
import { useStoreEVoucherHistory } from '../../../store';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { TPos } from '../../../../../types/eVoucher';
import { getAllProvider } from '../../../../../service/eVoucherHistory';

type TFilter = {
  type?: string | null;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  merchant?: string[] | null;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const [, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { setFilter: setFilterParam, getCurrentFilter } = useStoreEVoucherHistory();

  useEffect(() => {
    setInitialValues({
      startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
      endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
    });
  }, [params]);

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      type: values?.type ? values?.type : null,
      merchant: values?.merchant ? values?.merchant : null,
    };
    setFilterParam({ ...getCurrentFilter(), ...newFilter } as Record<string, unknown>);
    setFilter({ ...DEFAULT_PARAMS });
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...DEFAULT_PARAMS });
    setFilterParam({ ...getCurrentFilter(), search } as Record<string, unknown>);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilterParam({ search: getCurrentFilter()?.search ?? null });
    setFilter({ ...DEFAULT_PARAMS });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectOrgChart = (values: TPos[]) => {
    form.setFieldsValue({ merchant: values.map(item => item.id).join(',') });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={handleSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Đối tác" name="merchant">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getAllProvider}
                queryKey={['org-chart-dropdown']}
                keysLabel={['name', 'code']}
                handleListSelect={handleSelectOrgChart}
                placeholder="Chọn đối tác"
                keysTag={'name'}
                // defaultValues={defaultEmployee}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="type">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_HISTORY_EVOUCHER_STATUS} />
            </Form.Item>
            <DatePickerFilter startDate="startCreatedDate" endDate="endCreatedDate" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
