import { Button, Col, Row, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../components/breadCrumb';
import InputSearch from '../../components/input/InputSearch';
import TableComponent from '../../components/table';
import { useFetch } from '../../hooks';
import './styles.scss';

import { useNavigate } from 'react-router-dom';
import { EMPLOYEE_EXTERNAL_MANAGEMENT } from '../../configs/path';
import dayjs from 'dayjs';
import { EmployeeExternal } from '../../types/employeeExternal/employeeExternal';
import { getListEmployeeExternal } from '../../service/employeeExternal';
import { DownOutlined } from '@ant-design/icons';
import FormModalEmployeeExternal from './employeeExternalCreate/ModalEmployeeExternalCreate';
import { useState } from 'react';

const statusColors = {
  active: '#389E0D',
  inactive: '#CF1322',
};

const getStatusEmployeeExternal = (status: number) => (
  <p style={{ color: status === 1 ? statusColors.active : statusColors.inactive }}>
    {status === 1 ? 'Đang hiện diện' : 'Đã nghỉ việc'}
  </p>
);
const getLineManagerEmployeeExternal = (isLineManager: number) => {
  if (isLineManager === 1) {
    return <span>Quản lý</span>;
  }
  if (isLineManager === 2) {
    return <span>Nhân viên</span>;
  }
  return null;
};

const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YY');
  const formattedTime = dayjs(date).format('HH:mm:ss');
  return (
    <>
      <div>{formattedDate}</div>
      <div className="text-time">{formattedTime}</div>
    </>
  );
};

const ListEmployeeExternal = () => {
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };
  const handleCloseModal = () => {
    setIsModalVisible(false);
  };
  const { data, isLoading } = useFetch<EmployeeExternal[]>({
    queryKeyArrWithFilter: ['employee-external'],
    api: getListEmployeeExternal,
  });
  const dataEmployeeExternal =
    (data?.data?.data as unknown as { rows: EmployeeExternal[] })?.rows?.map(user => ({
      ...user,
      key: user.id,
    })) ?? [];

  const columns: TableColumnsType<EmployeeExternal> = [
    { title: 'Mã nhân viên', dataIndex: 'code', key: 'code' },
    {
      title: 'Nhân viên',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <>
          <div>{record?.name}</div>
          <div className="text-email">{record?.email}</div>
        </>
      ),
    },
    { title: 'Vị trí', dataIndex: 'isLineManager', key: 'isLineManager', render: getLineManagerEmployeeExternal },
    { title: 'Số điện thoại', dataIndex: 'phone', key: 'phone' },
    { title: 'Công ty', dataIndex: ['partnership', 'name'] },
    { title: 'Ngày tạo', dataIndex: 'createdDate', key: 'createdDate', className: 'text-time-td', render: formatDate },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'modifiedDate',
      key: 'modifiedDate',
      className: 'text-time-td',
      width: '12%',
      render: formatDate,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      align: 'center',
      width: '10%',
      render: getStatusEmployeeExternal,
    },
    {
      title: 'Hành động',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <a onClick={() => navigate(`${EMPLOYEE_EXTERNAL_MANAGEMENT}/${record.id}`)}>Xem chi tiết</a>
      ),
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content-employee-external">
        <div className="header-content">
          <InputSearch
            className="input-search"
            placeholder="Tìm kiếm theo tên, email, mã nhân viên, sđt"
            keySearch="search"
          />
          <Row gutter={[16, 8]}>
            <Col>
              <Button type="default">Xuất dữ liệu</Button>
            </Col>
            <Col>
              <Button type="default">
                Tải / nhập dữ liệu <DownOutlined />
              </Button>
            </Col>
            <Col>
              <Button type="primary" onClick={showModal}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </div>
      </div>
      <TableComponent
        className="table-employee-external"
        columns={columns}
        dataSource={dataEmployeeExternal}
        queryKeyArr={['employee-external']}
        loading={isLoading}
      />
      {isModalVisible && <FormModalEmployeeExternal visible={isModalVisible} onClose={handleCloseModal} />}
    </>
  );
};

export default ListEmployeeExternal;
