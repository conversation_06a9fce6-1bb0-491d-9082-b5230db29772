import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Flex, Form, Typography } from 'antd';
import { Space } from 'antd/lib';
import React, { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyInput from '../../../components/input/CurrencyInput';
import PercentInput from '../../../components/input/PercentInput';
import { TItemBonusByMargin } from '../../../types/salesPolicy';
import { useSalesPolicyStore } from '../store';
const { Title, Text } = Typography;

export interface TBonusByProductMarginProps {
  markError: (rowKey: string, fieldKey: string) => void;
  clearError: (rowKey: string, fieldKey: string) => void;
}

const BonusByProductMargin = (props: TBonusByProductMarginProps) => {
  const { clearError, markError } = props;

  const form = Form.useFormInstance();
  const [dataProductMargin, setDataProductMargin] = useState<TItemBonusByMargin[]>([]);
  const [disabledAdd, setDisabledAdd] = useState(false);
  const { initialValue, setIsModified, disabled, isModified } = useSalesPolicyStore();
  const defaultListBonus = initialValue?.listBonus;

  const columnsPercentage: (ColumnTypesCustom<TItemBonusByMargin>[number] & EditTableColumns)[] = [
    {
      title: 'Từ',
      dataIndex: 'minSalesPercentage',
      key: 'minSalesPercentage',
      editable: true,
      alwaysEditable: true,
      align: 'center',
      width: '15%',
      disabled,
      inputType: 'custom',
      dependencies: ['maxSalesPercentage'],
      renderEditComponent:
        (_, index) =>
        ({ onChange, save, disabled }) => {
          const prevMaxSalesPercentage = dataProductMargin[index - 1]?.maxSalesPercentage || '';
          index !== 0 && prevMaxSalesPercentage ? onChange(Number(prevMaxSalesPercentage) + 1) : '';
          return (
            <PercentInput
              placeholder="Nhập giá trị từ"
              onPressEnter={save}
              onBlur={save}
              onChange={val => onChange(val)}
              suffix="%"
              disabled={disabled || index !== 0}
            />
          );
        },
    },
    {
      title: 'Đến',
      dataIndex: 'maxSalesPercentage',
      key: 'maxSalesPercentage',
      editable: true,
      alwaysEditable: true,
      align: 'center',
      width: '15%',
      disabled,
      inputType: 'custom',
      dependencies: ['minSalesPercentage'],
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <PercentInput
            placeholder="Nhập giá trị đến"
            onPressEnter={save}
            onBlur={save}
            onChange={val => {
              onChange(val);
              setDisabledAdd(val >= '999' ? true : false);
            }}
            suffix="%"
            disabled={disabled}
          />
        ),
      rules: (_, index) => [
        ({ getFieldValue }) => ({
          validator: (_, value) => {
            const minSalesPercentage = getFieldValue('minSalesPercentage');
            const nextMaxSalesPercentage = dataProductMargin[index + 1]?.maxSalesPercentage || '';
            if (minSalesPercentage && Number(value) <= Number(minSalesPercentage)) {
              markError(dataProductMargin[index].key, 'maxSalesPercentage');
              return Promise.reject('Giá trị "Đến" phải lớn hơn giá trị "Từ"');
            } else if (nextMaxSalesPercentage && Number(value) > Number(nextMaxSalesPercentage)) {
              markError(dataProductMargin[index].key, 'maxSalesPercentage');
              return Promise.reject('Giá trị "Đến" phải nhỏ hơn giá trị "Đến" của dòng tiếp theo.');
            }
            clearError(dataProductMargin[index].key, 'maxSalesPercentage');
            return Promise.resolve();
          },
          validateTrigger: ['onChange', 'onBlur'],
        }),
      ],
    },
    {
      title: 'Tỉ lệ ghi nhận',
      dataIndex: 'bonusPercentageOnRate',
      key: 'bonusPercentageOnRate',
      alwaysEditable: true,
      editable: true,
      width: '15%',
      disabled,
      inputType: 'custom',
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <PercentInput
            placeholder="Nhập tỉ lệ giao dịch"
            onPressEnter={save}
            onBlur={save}
            onChange={val => onChange(val)}
            suffix="%"
            disabled={disabled}
          />
        ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'bonusAmountOnRate',
      key: 'bonusAmountOnRate',
      alwaysEditable: true,
      editable: true,
      width: '15%',
      disabled,
      inputType: 'custom',
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <CurrencyInput
            onChange={val => onChange(val || '')}
            onBlur={save}
            onPressEnter={save}
            placeholder={'Nhập số tiền phí'}
            suffix="VNĐ"
            disabled={disabled}
          />
        ),
    },
    {
      title: 'Ghi chú',
      dataIndex: 'noteSalesPercentage',
      key: 'noteSalesPercentage',
      width: '30%',
      alwaysEditable: true,
      editable: true,
      disabled,
      inputType: 'text',
      inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
    },
    {
      dataIndex: 'action',
      align: 'center',
      width: '10%',
      render: (_, record) => (
        <Button
          type="link"
          danger
          onClick={() => {
            handleDelete(record.key);
            setIsModified(true);
          }}
          disabled={disabled}
        >
          Xóa
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (defaultListBonus && !isModified) {
      const fieldBonus = defaultListBonus?.byMargin;
      if (Array.isArray(fieldBonus)) {
        setDataProductMargin(fieldBonus);
      }
    }
  }, [defaultListBonus, isModified]);

  const handleDelete = (key: React.Key) => {
    const newData = dataProductMargin.filter(item => item.key !== key);
    setDataProductMargin(newData);
    form.setFieldsValue({ listBonus: { byMargin: newData } });
  };

  const handleAdd = () => {
    const prevItem = dataProductMargin[dataProductMargin.length - 1];
    const newMaxSalesPercentage = prevItem?.maxSalesPercentage ? Number(prevItem.maxSalesPercentage) + 1 : '';
    const newData = {
      key: uuid(),
      minSalesPercentage: newMaxSalesPercentage,
      maxSalesPercentage: '',
      bonusPercentageOnRate: '',
      bonusAmountOnRate: '',
      noteSalesPercentage: '',
    };
    setDataProductMargin([...dataProductMargin, newData]);
  };

  const handleSave = (row: TItemBonusByMargin) => {
    const newData = [...dataProductMargin];
    const index = newData.findIndex((item: TItemBonusByMargin) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataProductMargin(newData);
    form.setFieldsValue({
      listBonus: {
        byMargin: newData,
      },
    });
    setIsModified(true);
  };

  return (
    <Space direction="vertical" style={{ marginBottom: '48px' }}>
      <Title level={5} style={{ margin: 0 }}>
        Thưởng vượt chỉ tiêu
      </Title>
      <Flex align="baseline" justify="space-between">
        <Space align="baseline" size={'large'}>
          <Text>Thưởng theo tỷ lệ bán được/ số sản phẩm ký quỹ </Text>
          <Form.Item name={['listBonus', 'isProgressiveByMargin']} valuePropName="checked">
            <Checkbox disabled={disabled}>Tính lũy tiến</Checkbox>
          </Form.Item>
        </Space>
        <Button type="default" icon={<PlusOutlined />} disabled={disabledAdd || disabled} onClick={handleAdd}>
          Thêm thưởng SLKQ
        </Button>
      </Flex>

      <EditTable<TItemBonusByMargin>
        columns={columnsPercentage}
        dataSource={dataProductMargin}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
      />
    </Space>
  );
};

export default BonusByProductMargin;
