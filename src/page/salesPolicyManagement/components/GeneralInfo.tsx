import { Col, Form, Input, Row, Select, Typography } from 'antd';
import React from 'react';
import { OPTIONS_APPROVAL_STATUS, OPTIONS_POLICY_TYPE, OPTIONS_STATUS_FILTER } from '../../../constants/common';
import { useSalesPolicyStore } from '../store';
import { useCheckPermissions } from '../../../hooks';
import { PERMISSION_SALES_POLICY } from '../../../constants/permissions/commission';

const { Item } = Form;
const { Title } = Typography;

const GeneralInfo = () => {
  const { disabled, actionModal } = useSalesPolicyStore();
  const { update: permissionUpdate } = useCheckPermissions(PERMISSION_SALES_POLICY);
  const eappUrl = Form.useWatch('eappUrl');

  return (
    <div>
      <Title level={4}>Thông tin chung</Title>
      <Row gutter={24}>
        <Col md={12} xs={24}>
          <Item
            label="Mã chính sách"
            name="code"
            required
            rules={[
              { required: true, message: 'Vui lòng nhập mã chính sách', whitespace: true },
              { max: 60, message: 'Mã chính sách vượt quá ký tự cho phép' },
            ]}
          >
            <Input
              placeholder="Nhập mã chính sách"
              maxLength={60}
              onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
              }}
              disabled={disabled || !actionModal?.type}
            />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item
            label="Tên chính sách"
            name="name"
            required
            rules={[{ required: true, message: 'Vui lòng nhập tên chính sách', whitespace: true }]}
          >
            <Input
              placeholder="Nhập tên chính sách"
              maxLength={60}
              disabled={!permissionUpdate}
              onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
              }}
            />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Loại chính sách" name="policyType">
            <Select placeholder="Chọn loại chính sách" options={OPTIONS_POLICY_TYPE} disabled={disabled} />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Trạng thái" name="isActive">
            <Select placeholder="Chọn trạng thái" options={OPTIONS_STATUS_FILTER} disabled={disabled} />
          </Item>
        </Col>
        {!actionModal?.type && (
          <>
            <Col md={12} xs={24}>
              <Item label="Số E-approve" name="eappNumber">
                <Input
                  readOnly
                  onClick={() => {
                    if (eappUrl) {
                      window.open(eappUrl, '_blank', 'noopener,noreferrer');
                    }
                  }}
                  style={{
                    cursor: eappUrl ? 'pointer' : 'default',
                    color: eappUrl ? '#1890ff' : 'inherit',
                  }}
                />
              </Item>
            </Col>
            <Col md={12} xs={24}>
              <Item label="Trạng thái phê duyệt" name="status">
                <Select placeholder="Chọn trạng thái" options={OPTIONS_APPROVAL_STATUS} disabled={true} />
              </Item>
            </Col>
          </>
        )}
      </Row>
    </div>
  );
};

export default GeneralInfo;
