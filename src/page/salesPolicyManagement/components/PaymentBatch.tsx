import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import PercentInput from '../../../components/input/PercentInput';
import { OPTIONS_STATUS_TRANSACTIONS } from '../../../constants/common';
import { TPaymentBatch } from '../../../types/salesPolicy';
import { handleBeforeInput } from '../../../utilities/regex';
import { useSalesPolicyStore } from '../store';

const { Title } = Typography;

const PaymentBatch = () => {
  const form = Form.useFormInstance();
  const [dataSource, setDataSource] = useState<TPaymentBatch[]>([]);
  const { initialValue, setIsModified, disabled } = useSalesPolicyStore();
  const defaultListPayment = initialValue?.listPayment;

  useEffect(() => {
    if (Array.isArray(defaultListPayment) && defaultListPayment?.length > 0) {
      setDataSource(defaultListPayment);
    }
  }, [defaultListPayment, initialValue?.listPayment]);

  const columnPayment: (ColumnTypesCustom<TPaymentBatch>[number] & EditTableColumns)[] = [
    {
      title: 'Đợt',
      dataIndex: 'phase',
      key: 'phase',
      alwaysEditable: true,
      editable: true,
      align: 'center',
      width: '20%',
      inputType: 'text',
      disabled,
      inputProps: { maxLength: 13, onBeforeInput: handleBeforeInput, inputMode: 'numeric', placeholder: 'Đợt' },
    },
    {
      title: 'Tỉ lệ giao dịch',
      dataIndex: 'transactionRate',
      key: 'transactionRate',
      alwaysEditable: true,
      editable: true,
      width: '20%',
      disabled,
      inputType: 'custom',
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <PercentInput
            placeholder="Nhập tỉ lệ giao dịch"
            onPressEnter={save}
            onBlur={save}
            onChange={val => onChange(val)}
            suffix="%"
            disabled={disabled}
          />
        ),
    },
    {
      title: 'Tình trạng giao dịch',
      dataIndex: 'status',
      key: 'status',
      alwaysEditable: true,
      editable: true,
      width: '20%',
      inputType: 'select',
      disabled,
      optionsSelect: OPTIONS_STATUS_TRANSACTIONS,
      selectProps: { placeholder: 'Chọn tình trang giao dịch' },
      rules: (record: unknown) => {
        const typedRecord = record as TPaymentBatch;
        return [
          {
            validator: (_, value) => {
              const selectedStatusList = dataSource.reduce<string[]>((acc, item) => {
                if (item.key !== typedRecord.key && item.status) acc.push(item.status);
                return acc;
              }, []);
              const isDuplicate = selectedStatusList.includes(value);
              if (isDuplicate) {
                return Promise.reject(new Error('Trạng thái đã được chọn'));
              }
              return Promise.resolve();
            },
          },
        ];
      },
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      alwaysEditable: true,
      width: '30%',
      editable: true,
      disabled,
      inputType: 'text',
      inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
    },
    {
      dataIndex: 'action',
      align: 'center',
      width: '10%',
      render: (_, record) => (
        <Button
          type="link"
          danger
          disabled={disabled}
          onClick={() => {
            handleDelete(record.key);
            setIsModified(true);
          }}
        >
          Xóa
        </Button>
      ),
    },
  ];

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    form.setFieldsValue({ listPayment: newData });
  };

  const handleAdd = () => {
    const newData: TPaymentBatch = {
      key: uuid(),
      phase: '',
      transactionRate: '',
      status: undefined,
      note: '',
      isNew: true,
    };
    setDataSource([...dataSource, newData]);
  };

  const handleSave = (row: TPaymentBatch) => {
    const newData = [...dataSource];
    const index = newData.findIndex(item => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataSource(newData);
    form.setFieldsValue({ listPayment: newData });
    setIsModified(true);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={5}>Đợt thanh toán</Title>
      <Button type="default" icon={<PlusOutlined />} disabled={disabled} style={{ float: 'right' }} onClick={handleAdd}>
        Thêm đợt TT
      </Button>
      <EditTable<TPaymentBatch>
        columns={columnPayment}
        dataSource={dataSource}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
      />
    </Space>
  );
};

export default PaymentBatch;
