import { Col, Form, Row, Space, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { PERMISSION_SALES_POLICY } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useRowFieldErrors, useUpdateField } from '../../../hooks';
import { getDetailOfSalesPolicy, updateSalesPolicy } from '../../../service/salesPolicy';
import { TSalesPolicy } from '../../../types/salesPolicy';
import { formatRangePikerToSubmit } from '../../../utilities/shareFunc';
import AnalyticalInfo from '../components/AnalyticalInfo';
import BonusByProductMargin from '../components/BonusByProductMargin';
import BonusByProductQuantity from '../components/BonusByProductQuantity';
import DetailedInfo from '../components/DetailedInfo';
import GeneralInfo from '../components/GeneralInfo';
import PaymentBatch from '../components/PaymentBatch';
import { useSalesPolicyStore } from '../store';
import {
  formatBonusListForDisplay,
  formatBonusListForSubmit,
  formatListPaymentForDisplay,
  formatListPaymentForSubmit,
} from '../utils';
import './styles.scss';

const { Text } = Typography;

const DetailOfSalesPolicy = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const { update: permissionUpdate } = useCheckPermissions(PERMISSION_SALES_POLICY);
  const { setDisabled, setInitialValue, isModified, setIsModified, initialValue } = useSalesPolicyStore();
  const { clearError, hasAnyError, markError, resetErrors } = useRowFieldErrors();
  const { data, isFetching } = useFetch<TSalesPolicy>({
    api: getDetailOfSalesPolicy,
    queryKeyArr: ['detail-of-sales-policy', id],
    withFilter: false,
    moreParams: { id },
  });
  const dataSalesPolicy = data?.data?.data;

  useEffect(() => {
    if (dataSalesPolicy) {
      const formatListBonus = formatBonusListForDisplay(dataSalesPolicy?.listBonus);
      const formatListPayment = formatListPaymentForDisplay(dataSalesPolicy?.listPayment);

      const formatData = {
        ...dataSalesPolicy,
        active: Number(dataSalesPolicy?.active),
        revenueRate: dataSalesPolicy?.revenueRate,
        amount: dataSalesPolicy?.amount?.toString(),
        comamount: dataSalesPolicy?.comamount?.toString(),
        comrate: dataSalesPolicy?.comrate?.toString(),
        date: [dayjs(dataSalesPolicy.startDate || undefined), dayjs(dataSalesPolicy.endDate || undefined)],
        listPayment: formatListPayment,
        listBonus: { ...dataSalesPolicy?.listBonus, ...formatListBonus },
        periodObj: {
          periodFrom: dataSalesPolicy?.periodFrom,
          periodTo: dataSalesPolicy?.periodTo,
          periodName: dataSalesPolicy?.periodName,
        },
      };
      const statusPolicy = dataSalesPolicy?.status || null;
      setDisabled(statusPolicy === 'APPROVED' || statusPolicy === 'WAITING' ? true : false);
      form.setFieldsValue(formatData);
      setInitialValue(formatData as TSalesPolicy);
    }
  }, [dataSalesPolicy, form, setDisabled, setInitialValue]);

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const { mutateAsync: updateSalePolicy, isPending } = useUpdateField<TSalesPolicy>({
    apiQuery: updateSalesPolicy,
    keyOfDetailQuery: ['detail-of-sales-policy', id],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    if (hasAnyError()) {
      return;
    }
    const values = await form.validateFields();
    const listPaymentBatch = form.getFieldValue('listPayment');
    const listBonus = form.getFieldValue('listBonus');
    const periodObj = form.getFieldValue('periodObj');

    const formatListPaymentBatch = formatListPaymentForSubmit(listPaymentBatch);
    const formatListBonus = formatBonusListForSubmit(listBonus);

    const newData = {
      ...values,
      ...periodObj,
      id,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      analysisBonus: values?.analysisBonus?.trim() || '',
      analysisIndicator: values?.analysisIndicator?.trim() || '',
      analysisFees: values?.analysisFees?.trim() || '',
      active: values?.active,
      revenueRate: Number(values?.revenueRate),
      amount: Number(values?.amount),
      comamount: Number(values?.comamount),
      comrate: Number(values?.comrate),
      ...formatRangePikerToSubmit(values.date),
      date: undefined,
      listPayment: formatListPaymentBatch,
      listBonus: {
        isProgressiveByQuantity: values?.listBonus?.isProgressiveByQuantity ? true : false,
        isProgressiveByMargin: values?.listBonus?.isProgressiveByMargin ? true : false,
        ...formatListBonus,
      },
    };
    const res = await updateSalePolicy(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
      resetErrors();
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <div className="wrapper-detail-of-sales-policy">
      <Spin spinning={isFetching}>
        <BreadCrumbComponent titleBread={dataSalesPolicy && dataSalesPolicy.name ? dataSalesPolicy.name : ''} />
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={!permissionUpdate}
          onValuesChange={validateForm}
        >
          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <GeneralInfo />
              <DetailedInfo enabled />
              <AnalyticalInfo />
            </Col>
            <Col xs={24} md={12}>
              <Col span={24}>
                <div className="update-time">
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày cập nhật: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataSalesPolicy?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataSalesPolicy?.modifiedBy?.userName || ''} - ${dataSalesPolicy?.modifiedBy?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày tạo: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataSalesPolicy?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataSalesPolicy?.createdBy?.userName || ''} - ${dataSalesPolicy?.createdBy?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Col>
            <Col span={24}>
              <PaymentBatch />
            </Col>
            <Col span={24}>
              <div className="list-bonus">
                <Space direction="vertical" size={'middle'}>
                  <BonusByProductQuantity markError={markError} clearError={clearError} />
                  <BonusByProductMargin markError={markError} clearError={clearError} />
                </Space>
              </div>
            </Col>
          </Row>
        </Form>
        {isModified && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfSalesPolicy;
