import { But<PERSON>, Col, Form, Row, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import ModalComponent from '../../../components/modal';
import { useCreateField, useFetch, useRowFieldErrors } from '../../../hooks';
import { createSalesPolicy, getClonePolicy } from '../../../service/salesPolicy';
import { TSalesPolicy } from '../../../types/salesPolicy';
import { formatRangePikerToSubmit } from '../../../utilities/shareFunc';
import AnalyticalInfo from '../components/AnalyticalInfo';
import BonusByProductMargin from '../components/BonusByProductMargin';
import BonusByProductQuantity from '../components/BonusByProductQuantity';
import DetailedInfo from '../components/DetailedInfo';
import GeneralInfo from '../components/GeneralInfo';
import PaymentBatch from '../components/PaymentBatch';
import { useSalesPolicyStore } from '../store';
import {
  formatBonusListForDisplay,
  formatBonusListForSubmit,
  formatListPaymentForDisplay,
  formatListPaymentForSubmit,
} from '../utils';
import './styles.scss';

const CreateSalePolicy = () => {
  const [form] = Form.useForm();
  const { actionModal, setActionModal, setInitialValue } = useSalesPolicyStore();
  const { clearError, hasAnyError, markError, resetErrors } = useRowFieldErrors();

  const { data, isLoading } = useFetch<TSalesPolicy>({
    api: () => getClonePolicy({ id: actionModal?.id }),
    queryKeyArr: ['get-clone-policy', actionModal?.id],
    enabled: !!actionModal?.id,
  });
  const dataClonePolicy = data?.data?.data;

  const { mutateAsync: createSalePolicy, isPending } = useCreateField<TSalesPolicy>({
    apiQuery: createSalesPolicy,
    keyOfListQuery: ['list-of-sales-policy'],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataClonePolicy && actionModal?.type === 'clone') {
      const formatListBonus = formatBonusListForDisplay(dataClonePolicy?.listBonus);
      const formatListPayment = formatListPaymentForDisplay(dataClonePolicy?.listPayment);

      const formatData = {
        ...dataClonePolicy,
        periodObj: {
          periodFrom: dataClonePolicy?.periodFrom,
          periodName: dataClonePolicy?.periodName,
          periodTo: dataClonePolicy?.periodTo,
        },
        code: `${dataClonePolicy?.code}-COPY`,
        active: Number(dataClonePolicy?.active),
        revenueRate: dataClonePolicy?.revenueRate?.toString(),
        amount: dataClonePolicy?.amount?.toString(),
        comamount: dataClonePolicy?.comamount?.toString(),
        comrate: dataClonePolicy?.comrate?.toString(),
        date: [dayjs(dataClonePolicy.startDate || undefined), dayjs(dataClonePolicy.endDate || undefined)],
        listPayment: formatListPayment,
        listBonus: formatListBonus,
      };
      form.setFieldsValue(formatData);
      setInitialValue(formatData as TSalesPolicy);
    }
  }, [actionModal?.type, dataClonePolicy, form, setInitialValue]);

  const handleSubmit = async () => {
    if (hasAnyError()) {
      return;
    }
    const values = await form.validateFields();
    const listPaymentBatch = form.getFieldValue('listPayment');
    const listBonus = form.getFieldValue('listBonus');
    const periodObj = form.getFieldValue('periodObj');

    const formatListPaymentBatch = formatListPaymentForSubmit(listPaymentBatch);
    const formatListBonus = formatBonusListForSubmit(listBonus);

    const newData = {
      ...values,
      ...periodObj,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      analysisBonus: values?.analysisBonus?.trim() || '',
      analysisIndicator: values?.analysisIndicator?.trim() || '',
      analysisFees: values?.analysisFees?.trim() || '',
      active: values?.active,
      revenueRate: Number(values?.revenueRate),
      amount: Number(values?.amount),
      comamount: Number(values?.comamount),
      comrate: Number(values?.comrate),
      ...formatRangePikerToSubmit(values.date),
      date: undefined,
      listPayment: formatListPaymentBatch,
      listBonus: {
        isProgressiveByQuantity: values?.listBonus?.isProgressiveByQuantity ? true : false,
        isProgressiveByMargin: values?.listBonus?.isProgressiveByMargin ? true : false,
        ...formatListBonus,
      },
    };

    const res = await createSalePolicy(newData);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      setActionModal({ isOpen: false, type: undefined, id: undefined });
      setInitialValue(undefined);
      resetErrors();
    }
  };

  return (
    <Spin spinning={isLoading}>
      <ModalComponent
        rootClassName="wrapper-create-sale-policy"
        title="Tạo mới chính sách phí"
        open={actionModal?.isOpen}
        onCancel={() => {
          setActionModal({ isOpen: false, type: undefined, id: undefined });
          form.resetFields();
          setInitialValue(undefined);
        }}
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" onClick={handleSubmit} loading={!!isPending}>
            Lưu
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            active: 1,
          }}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <GeneralInfo />
              <DetailedInfo enabled={actionModal?.isOpen} />
              <AnalyticalInfo />
            </Col>
            <Col span={24}>
              <PaymentBatch />
            </Col>
            <Col span={24}>
              <div className="list-bonus">
                <Space direction="vertical" size={'middle'}>
                  <BonusByProductQuantity markError={markError} clearError={clearError} />
                  <BonusByProductMargin markError={markError} clearError={clearError} />
                </Space>
              </div>
            </Col>
          </Row>
        </Form>
      </ModalComponent>
    </Spin>
  );
};

export default CreateSalePolicy;
