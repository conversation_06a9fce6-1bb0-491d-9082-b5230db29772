import { useQueryClient } from '@tanstack/react-query';
import { Button, Form, Input, Space } from 'antd';
import React from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { STATUS_ADJUSTMENT_VERSION } from '../../../../constants/common';
import { PERMISSION_COMMISSION } from '../../../../constants/permissions/commission';
import { FetchResponse, TDataList, useCheckPermissions, useUpdateField } from '../../../../hooks';
import { updateAnNounced } from '../../../../service/commission';
import { ButtonConfig, GroupButtonProps, TCommission, TExpense } from '../../../../types/commission';
import UploadFileVersionAdjustment from './UploadFileVersionAdjustment';
import { checkStatusAdjustmentVersion, handleDownloadFileS3 } from '../../../../utilities/shareFunc';

export const GroupButton: React.FC<GroupButtonProps & { dataExpenseList?: TExpense }> = ({
  handleStatusUpdate,
  modal,
  notification,
  tab,
  versionAdjustment,
  loadingButton,
  isCreate,
  dataExpenseList,
}) => {
  const { id } = useParams();
  const [formCancelApprove] = Form.useForm();
  const [formCancelPublish] = Form.useForm();
  const { publish, create, update, getById, approve } = useCheckPermissions(PERMISSION_COMMISSION);

  const queryClient = useQueryClient();
  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['detail-of-commission', id]);
  const dataCommission = data?.data?.data;

  const adjustment = tab === 'adjustment' ? versionAdjustment : dataCommission?.adjustmentVersions?.[0];
  const itemStatus = dataCommission?.adjustmentVersions?.find(item => checkStatusAdjustmentVersion(item?.status));

  const mutateAnNounced = useUpdateField({
    apiQuery: updateAnNounced,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleDownloadOriginal = () => {
    const version = dataCommission?.adjustmentVersions?.[0];
    const nameFile = `${version?.fileName}.xlsx`;
    if (!version?.fileUrl) {
      notification.error({ message: 'Không tìm thấy đường dẫn tải xuống' });
      return;
    }
    handleDownloadFileS3(version.fileUrl, nameFile);
  };

  const isStatusWaiting = adjustment?.status === STATUS_ADJUSTMENT_VERSION.WAITING && approve;

  const isButtonSendApprove =
    (adjustment?.status === STATUS_ADJUSTMENT_VERSION.NEW ||
      adjustment?.status === STATUS_ADJUSTMENT_VERSION.REJECTED) &&
    !itemStatus;

  const isStatusApprove = adjustment?.status === STATUS_ADJUSTMENT_VERSION.APPROVED;

  const isStatusAnnounced = adjustment?.status === STATUS_ADJUSTMENT_VERSION.ANNOUNCED && publish;
  const checkPermissionButtonImport =
    dataExpenseList && dataExpenseList?.expenseList?.length > 0 && isCreate && !itemStatus;
  const isButtonSendSAP = adjustment?.status === STATUS_ADJUSTMENT_VERSION.ANNOUNCED && publish && getById && isCreate;

  // Danh sách các nút với điều kiện hiển thị
  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'approve',
      label: 'Duyệt',
      permission: isStatusWaiting,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Duyệt đợt tính phí',
          content: 'Bạn có muốn duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'APPROVED', successMessage: 'Duyệt thành công.' });
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'sendApprove',
      label: 'Gửi duyệt',
      permission: isButtonSendApprove && update,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Gửi duyệt đợt tính phí',
          content: 'Bạn có muốn gửi duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'WAITING', successMessage: 'Gửi duyệt thành công.' });
          },

          modal,
        });
      },

      type: 'primary',
    },

    {
      key: 'publish',
      label: 'Công bố',
      permission: isStatusApprove && publish,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận công bố',
          content: 'Bạn có muốn công bố đợt tính phí không?',
          handleConfirm: async () => {
            if (id) {
              const res = await mutateAnNounced.mutateAsync({
                id,
                status: versionAdjustment?.status || adjustment?.status || '',
                adjustmentVersionId: versionAdjustment?.id || adjustment?.id || '',
              });
              if (res?.data?.statusCode === '0') {
                notification.success({ message: 'Công bố thành công.' });
              }
            }
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'cancelApprove',
      label: 'Hủy phê duyệt',
      permission: isStatusApprove && (update || getById),
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          className: 'modal-cancel-approve',
          title: 'Hủy phê duyệt đợt tính phí',
          content: (
            <Form form={formCancelApprove}>
              <p>Bạn có muốn gửi duyệt đợt tính phí không?</p>
              <Form.Item
                name="cancelApprovalReason"
                rules={[{ required: true, message: 'Vui lòng nhập lý do.', whitespace: true }]}
              >
                <Input placeholder="Vui lòng nhập lý do muốn hủy phê duyệt đợt tính phí" maxLength={256} />
              </Form.Item>
            </Form>
          ),
          handleConfirm: async () => {
            await formCancelApprove.validateFields();
            const values = formCancelApprove.getFieldsValue();
            await handleStatusUpdate({
              status: 'WAITING',
              successMessage: 'Hủy duyệt thành công.',
              cancelPublishReason: values.cancelApprovalReason,
            });
            formCancelApprove.resetFields();
          },
          destroyOnClose: true,
          onCancel: () => formCancelApprove.resetFields(),
          modal,
        });
      },
    },
    {
      key: 'cancel-publish',
      label: 'Hủy công bố',
      permission: isStatusAnnounced,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          className: 'modal-cancel-approve',
          title: 'Hủy công bố đợt tính phí',
          content: (
            <Form form={formCancelPublish}>
              <p>Vui lòng nhập lý do muốn hủy công bố đợt tính phí không?</p>
              <Form.Item
                name="cancelApprovalReason"
                rules={[{ required: true, message: 'Vui lòng nhập lý do.', whitespace: true }]}
              >
                <Input placeholder="Vui lòng nhập lý do muốn hủy công bố đợt tính phí" maxLength={256} />
              </Form.Item>
            </Form>
          ),
          handleConfirm: async () => {
            await formCancelPublish.validateFields();
            const values = formCancelPublish.getFieldsValue();
            if (id) {
              const res = await mutateAnNounced.mutateAsync({
                id,
                status: versionAdjustment?.status || adjustment?.status || '',
                adjustmentVersionId: versionAdjustment?.id || adjustment?.id || '',
                cancelPublishReason: values?.cancelApprovalReason,
              });
              if (res?.data?.statusCode === '0') {
                formCancelPublish.resetFields();
                notification.success({ message: 'Hủy công bố thành công.' });
              }
            }
          },
          onCancel: () => formCancelPublish.resetFields(),
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'reject',
      label: 'Từ chối ',
      permission: isStatusWaiting,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          title: 'Từ chối đợt tính phí',
          content: 'Bạn có muốn từ chối đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'REJECTED', successMessage: 'Từ chối thành công' });
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'integrated',
      label: 'Tạo hợp đồng môi giới',
      permission: isButtonSendSAP,
      onClick: () => {
        modalConfirm({
          title: 'Tạo hợp đồng môi giới',
          content: (
            <>
              <div>Bạn có muốn tạo hợp đồng môi giới?</div>
              <div>Sau khi nhấn xác nhận, bạn sẽ không thể chỉnh sửa được thông tin bản ghi đợt tính phí nữa.</div>
            </>
          ),
          handleConfirm: () => {
            handleStatusUpdate({ status: 'INTERGRATED', successMessage: 'Tạo hợp đồng môi giới thành công' });
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: update && checkPermissionButtonImport,
      component: <UploadFileVersionAdjustment />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: (create || update || getById) && checkPermissionButtonImport,
      onClick: handleDownloadOriginal,
      type: 'default',
    },
  ];
  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
    </Space>
  );
};
