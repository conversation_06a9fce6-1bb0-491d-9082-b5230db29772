import React from 'react';
import { TableColumnsType, Typography, Spin } from 'antd';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import './styles.scss';
import {
  _IMPORT_HISTORY_STATUS_COLOR,
  FORMAT_DATE_TIME,
  IMPORT_HISTORY_STATUS,
  IMPORT_HISTORY_STATUS_NAME,
} from '../../constants/common';
import { useFetch } from '../../hooks';
import { getListImportHistory } from '../../service/uploadHistory';
import dayjs from 'dayjs';
import { FailedFile, ImportHistory } from '../../types/importHistoryProject/importHistoryProject';
import { LoadingOutlined } from '@ant-design/icons';
import { ImportProjectHistoryProvider } from './context/ImportProjectHistoryContext';
import FilterSearch from './components/FilterSearch';

const { Text } = Typography;

const ImportProjectHistoryManagement: React.FC = () => {
  const {
    data: importHistoryList,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ImportHistory[]>({
    queryKeyArrWithFilter: ['get-import-project-history'],
    api: getListImportHistory,
  });
  const importHistorys = importHistoryList?.data?.data?.rows || [];

  const columns: TableColumnsType<ImportHistory> = [
    {
      title: 'Tên dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 280,
      render: (value: string) => <p>{value}</p>,
    },
    {
      title: 'Ngày tải lên',
      dataIndex: 'importDate',
      key: 'importDate',
      width: 154,
      render: (value: string, record: ImportHistory) => (
        <div>
          <div>{dayjs(value).format(FORMAT_DATE_TIME)}</div>
          <div className="text-email">{record?.userName || '-'}</div>
        </div>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 176,
      align: 'center',
      render: (status: string) => (
        <Text style={{ color: _IMPORT_HISTORY_STATUS_COLOR[status] }}>{IMPORT_HISTORY_STATUS_NAME[status]}</Text>
      ),
    },
    {
      title: 'Trạng thái tích hợp SAP',
      dataIndex: 'sapResponse',
      key: 'sapResponse',
      width: 176,
      align: 'center',
      render: (_: unknown, record: ImportHistory) => (
        <Text>
          {record?.sapResponse?.length && record?.sapResponse[0]?.statusCode === '200' ? 'Thành công' : 'Thất bại'}
        </Text>
      ),
    },
    {
      title: 'Sản phẩm upload thành công',
      dataIndex: '',
      key: '',
      width: 232,
      align: 'center',
      render: (_: string, record: ImportHistory) => {
        const totalRecords = (record.success ?? 0) + (record.fail ?? 0);
        switch (record.status) {
          case 'PENDING':
            return <Text></Text>;
          case 'ENTIRE_ERROR':
            return <Text></Text>;
          case 'PROCESSING':
            return <Text>...</Text>;
          case 'SUCCESS':
            return (
              <Text>
                {record.success}/{totalRecords}
              </Text>
            );
          case 'PARTIAL_ERROR':
            return (
              <Text>
                {record.success}/{totalRecords}
              </Text>
            );
          default:
            return <Text>-</Text>;
        }
      },
    },
    {
      title: 'Log file',
      dataIndex: 'failedFile',
      key: 'failedFile',
      width: 310,
      render: (value: FailedFile, record: ImportHistory) => {
        switch (record.status) {
          case IMPORT_HISTORY_STATUS.pending:
            return <Text></Text>;
          case IMPORT_HISTORY_STATUS.processing:
            return <Spin indicator={<LoadingOutlined spin />} size="large" />;
          case IMPORT_HISTORY_STATUS.success:
            return value?.failedFileUrl ? (
              <a href={`${import.meta.env.VITE_S3_IMAGE_URL}/${value?.failedFileUrl}`} target="_blank" rel="noreferrer">
                {value?.failedFileName}
              </a>
            ) : (
              <Text>-</Text>
            );
          case IMPORT_HISTORY_STATUS.partial_error:
          case IMPORT_HISTORY_STATUS.entire_error:
            return value?.failedFileUrl ? (
              <a href={`${import.meta.env.VITE_S3_IMAGE_URL}/${value?.failedFileUrl}`} target="_blank" rel="noreferrer">
                {value?.failedFileName}
              </a>
            ) : (
              <Text>-</Text>
            );
          default:
            return <Text>-</Text>;
        }
      },
    },
  ];

  return (
    <ImportProjectHistoryProvider>
      <BreadCrumbComponent />

      <div className="header-content">
        <FilterSearch />
      </div>
      <TableComponent
        queryKeyArr={['get-import-project-history']}
        columns={columns}
        loading={isLoading || isPlaceholderData || isFetching}
        dataSource={importHistorys}
        rowKey="id"
      />
    </ImportProjectHistoryProvider>
  );
};

export default ImportProjectHistoryManagement;
