import { But<PERSON>, <PERSON>lex, <PERSON>, Popover, Spin, Typography } from 'antd';
import { useEffect, useState } from 'react';

import { CloseCircleOutlined, PushpinOutlined } from '@ant-design/icons';
import { useQueryClient } from '@tanstack/react-query';
import { MessageTypeEnum } from '../../../../../../constants/common';
import { useCreateField, useFetch, useSubscribeQuery, useUpdateField } from '../../../../../../hooks';
import {
  adminSendComment,
  getPinnedComment,
  putPinnedComment,
  sendComment,
} from '../../../../../../service/liveStream';
import { IComment, IExtraData, TSubmitComment } from '../../../../../../types/liveStream';
import { useStoreLiveStreamRoom } from '../../../../storeLiveStreamRoom';
import AvatarComponent from '../../../AvatarComponent';
import ModalRequestDemand from '../../../ModalRequestDemand';
import { CommentInput } from './components/CommentInput';
import CommentItem from './components/CommentItem';
import ThreadView from './components/ThreadView';
import './styles.scss';
import useStompClient from '../../../../../../hooks/notify';

const { Text } = Typography;

const CommentSection = () => {
  const queryClient = useQueryClient();
  const [comments, setComments] = useState<IComment[]>();
  const [isLoadingButtonSend, setIsLoadingButtonSend] = useState(false);
  const [openModalRequestDemand, setOpenModalRequestDemand] = useState(false);
  const activeConversation = useStoreLiveStreamRoom(state => state.activeConversation);
  const setActiveConversation = useStoreLiveStreamRoom(state => state.setActiveConversation);
  const dataAccount = useStoreLiveStreamRoom(state => state.dataAccount);
  const initialDataEvent = useStoreLiveStreamRoom(state => state.initialDataEvent);
  const isAdmin = useStoreLiveStreamRoom(state => state.isAdmin);

  const dataComments = useSubscribeQuery<IComment[]>(['get-list-comments', initialDataEvent?.id]);

  useStompClient({
    username: dataAccount?.id,
    onMessageReceived(message) {
      console.log('Message received:', message);
      const newComment = JSON.parse(message.body) as IComment;
      console.log('newComment :', newComment);
      // Update comments list in real-time
      // setComments(prev => [...(prev || []), newComment]);
    },
  });

  const { data: pinned, isLoading } = useFetch<IComment>({
    api: getPinnedComment,
    queryKeyArr: ['get-pinned', initialDataEvent?.id],
    withFilter: false,
    moreParams: {
      eventId: initialDataEvent?.id,
    },
    enabled: !!initialDataEvent?.id && !!dataAccount,
  });

  const dataPinned =
    pinned?.data?.data && typeof pinned?.data?.data !== 'boolean' ? (pinned?.data?.data as IComment) : undefined;

  const send = useCreateField({
    apiQuery: sendComment,
    isMessageSuccess: false,
    isMessageError: false,
    // keyOfDetailQuery: ['get-list-comments', initialDataEvent?.id],
  });
  const adminSend = useCreateField({
    apiQuery: adminSendComment,
    isMessageSuccess: false,
    isMessageError: false,
    // keyOfDetailQuery: ['get-list-comments', initialDataEvent?.id],
  });
  const putPinned = useUpdateField({
    apiQuery: putPinnedComment,
    isMessageSuccess: false,
    isMessageError: false,
    keyOfDetailQuery: ['get-pinned', initialDataEvent?.id],
  });

  useEffect(() => {
    if (dataComments?.data?.data) {
      setComments(dataComments.data.data);
    }
  }, [dataComments, pinned]);

  // Lưu ID của comment và chuyển sang ThreadView
  const handleViewThread = (comment: IComment) => {
    const commentToSet =
      comment?.type === 12
        ? {
            name: comment?.extraData?.replyName,
            content: comment?.extraData?.replyContent,
            date: comment?.extraData?.replyDate,
            id: comment?.extraData?.id,
          }
        : comment;

    setActiveConversation(commentToSet as IComment & IExtraData);
  };

  const handleBackFromThread = () => {
    setActiveConversation(null);
  };

  const handleNewComment = async (content: string, replyComment?: IExtraData) => {
    if (initialDataEvent?.id) {
      const newComment: TSubmitComment = {
        content,
        eventId: initialDataEvent?.id,
        type: replyComment ? MessageTypeEnum.Reply : MessageTypeEnum.Text,
        extraData: replyComment,
        userCheckinCode: !isAdmin ? dataAccount?.checkInCode : undefined,
        userEmail: !isAdmin ? dataAccount?.email : undefined,
      };
      const res = isAdmin ? await adminSend.mutateAsync(newComment) : await send.mutateAsync(newComment);
      setIsLoadingButtonSend(true);
      if (res?.data?.statusCode === '0') {
        setTimeout(async () => {
          await queryClient.invalidateQueries({
            queryKey: ['get-list-comments', initialDataEvent?.id],
            refetchType: 'all',
          });
          await queryClient.invalidateQueries({
            queryKey: ['get-list-reply-comments', replyComment?.id, initialDataEvent?.id],
            refetchType: 'all',
          });
          setIsLoadingButtonSend(false);
        }, 3000);
      } else {
        setIsLoadingButtonSend(false);
      }
    }
  };

  const handleUnPinned = async () => {
    await putPinned.mutateAsync(dataPinned?.id);
  };

  const content = (
    <Flex align="flex-start" gap={8}>
      <AvatarComponent src={dataPinned?.userImage} size={32} fullName={dataPinned?.name} />
      <div>
        <Text strong>{dataPinned?.name}</Text>
        <div>{dataPinned?.content}</div>
      </div>
    </Flex>
  );

  return (
    <div className="wrapper-comment">
      {!dataComments ? (
        <Flex justify="center" align="center" style={{ height: '80%' }}>
          <Spin tip="Đang tải bình luận..." />
        </Flex>
      ) : (
        <div className={`comments-container ${dataPinned ? `pinned` : ''}`}>
          {activeConversation && <ThreadView mainComment={activeConversation} onBack={handleBackFromThread} />}
          <List
            itemLayout="vertical"
            className="comments-list"
            style={activeConversation ? { display: `none` } : undefined}
            dataSource={[...(comments || [])].reverse()}
            renderItem={(item: IComment) => (
              <div data-comment-id={item?.id}>
                <CommentItem comment={item} onReplyClick={handleViewThread} />
              </div>
            )}
          />
        </div>
      )}
      <div className="comment-input-container">
        {dataPinned && (
          <div className="wrapper-pinned">
            <Flex
              align="center"
              justify="space-between"
              gap={8}
              style={{
                background: '#f0f2f5',
                borderRadius: 16,
                padding: '8px 12px',
              }}
            >
              <Flex align="center" gap={8} style={{ width: '95%' }}>
                <PushpinOutlined />
                <AvatarComponent src={dataPinned?.userImage} fullName={dataPinned?.name} size={24} />
                {!isLoading && (
                  <Popover content={content} placement="topRight">
                    <Text
                      style={{ maxWidth: '100%', textOverflow: 'ellipsis', whiteSpace: 'nowrap', overflow: 'hidden' }}
                    >
                      <Text strong>{dataPinned?.name}</Text>
                      {': '}
                      <Text>{dataPinned?.content}</Text>
                    </Text>
                  </Popover>
                )}
              </Flex>
              {isAdmin && (
                <CloseCircleOutlined style={{ cursor: 'pointer', float: 'right' }} onClick={handleUnPinned} />
              )}
            </Flex>
          </div>
        )}
        {!isAdmin && initialDataEvent?.allowRequestDemand && (
          <>
            <Button
              type="primary"
              style={{ padding: '0 9px', height: 24, float: 'right', margin: '8px 16px 0px ' }}
              onClick={() => setOpenModalRequestDemand(true)}
            >
              Tư vấn
            </Button>
            <ModalRequestDemand open={openModalRequestDemand} onCancel={() => setOpenModalRequestDemand(false)} />
          </>
        )}
        <CommentInput onSubmit={handleNewComment} loadingSend={isLoadingButtonSend} />
      </div>
    </div>
  );
};

export default CommentSection;
