import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterPeriod from '../../../../../components/dropdown/dropdownFilterPeriod';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { FORMAT_DATE, FORMAT_DATE_API, OPTIONS_STATUS_FILTER } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';

type TFilter = {
  isActive?: number | null;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
  year?: string | Dayjs | null;
  period?: string | null;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  //Ẩn người tạo
  // const { entities: defaultEmployee } = useEntitiesById({
  //   apiQuery: getEmployeeDropdownById,
  //   queryKey: ['employee-dropdown'],
  //   params: { id: filter?.createdBy },
  //   labelField: 'name',
  //   valueField: 'id',
  // });

  useEffect(() => {
    const formatData = {
      status: params.get('status') || undefined,
      isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
      endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
      period: params.get('period') || undefined,
      year: params.get('year') ? params.get('year') : undefined,
      createdBy: params.get('createdBy') || undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      status: values.status || null,
      isActive: values.isActive || null,
      year: values?.year ? Number(dayjs(values?.year).format('YYYY')) : null,
      period: values?.period || null,
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  // const handleSelectEmployee = (values: TSelectDropdown[]) => {
  //   form.setFieldsValue({ createdBy: values.map(item => item?.option?.accountId).join(',') });
  // };
  const handleClearFilters = () => {
    setInitialValues(undefined);
    setIsOpenFilter(false);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            {/* <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'email']}
                handleListSelect={handleSelectEmployee}
                defaultValues={defaultEmployee}
                placeholder="Chọn nhân viên"
                keysTag={['email']}
              />
            </Form.Item> */}
            <Form.Item label="Trạng thái hoạt động" name="isActive">
              <Select placeholder="Chọn trạng thái hoạt động" allowClear options={OPTIONS_STATUS_FILTER} />
            </Form.Item>
            {/* <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Kỳ tính phí/ hoa hồng" name="year">
                  <DatePicker format={'YYYY'} picker="year" />
                </Form.Item>
              </Col>
              <Col span={12} style={{ alignContent: 'end' }}>
                <Form.Item name="period">
                  <Select placeholder="Chọn kỳ tính phí" allowClear options={OPTIONS_PERIOD} />
                </Form.Item>
              </Col>
            </Row> */}
            <DropdownFilterPeriod
              label="Kỳ tính phí/ hoa hồng"
              fieldName="period"
              defaultValues={initialValues?.period as string}
            />

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Tạo từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => current && current > dayjs().startOf('day')}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => current && current > dayjs().startOf('day')}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
