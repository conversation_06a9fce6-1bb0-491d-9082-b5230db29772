import { Flex, Radio, Table, TableProps } from 'antd';
import { AnyObject } from 'antd/es/_util/type';
import { useEffect, useState } from 'react';
import GroupButtonActionDebt from './GroupButtonActionDebt';
import { IAdjustmentVersions } from '../../../../types/commissionDebt';

interface IAdjustmentDataProps<T extends AnyObject> {
  columns: TableProps<T>['columns'];
  initialValue?: T[];
}

const AdjustmentData = <T extends IAdjustmentVersions>(props: IAdjustmentDataProps<T>) => {
  const { columns, initialValue } = props;
  const [dataAdjustment, setDataAdjustment] = useState<T[]>([]);
  const [selectedAdjustment, setSelectedAdjustment] = useState<T | null>();
  const initialValueConfirmed = initialValue?.find(item => item?.status === 'CONFIRMED');

  useEffect(() => {
    if (initialValue) {
      setSelectedAdjustment(initialValueConfirmed);
      setDataAdjustment(initialValue);
    }
  }, [initialValue, initialValueConfirmed]);

  const RadioColumns: TableProps<T>['columns'] = [
    {
      title: '',
      dataIndex: 'radio',
      key: 'radio',
      align: 'center',
      width: 48,
      render: (_, record) => (
        <Radio
          checked={selectedAdjustment?.id === record.id}
          disabled={initialValueConfirmed && record.status !== 'CONFIRMED'}
          onClick={() => {
            if (selectedAdjustment?.id === record.id) {
              setSelectedAdjustment(null);
            } else {
              setSelectedAdjustment(record);
            }
          }}
        />
      ),
    },
    ...(columns || []),
  ];

  return (
    <div>
      <Flex justify="end" style={{ marginBottom: 16 }}>
        <GroupButtonActionDebt
          versionAdjustment={selectedAdjustment}
          checkButtonConfirm={!initialValueConfirmed}
          isDisabledButtonUpload={!(dataAdjustment.length === 0)}
        />
      </Flex>

      <Table dataSource={dataAdjustment} columns={RadioColumns} rowKey="id" pagination={false} />
    </div>
  );
};

export default AdjustmentData;
