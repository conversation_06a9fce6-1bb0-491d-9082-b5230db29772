import { Flex, Table } from 'antd';
import { AnyObject } from 'antd/es/_util/type';
import { TableProps } from 'antd/lib';
import { useEffect, useState } from 'react';
import InputSearch from '../../../../components/input/InputSearch';
import { filterData } from '../../../../utilities/shareFunc';
import GroupButtonActionDebt from './GroupButtonActionDebt';
import { useStoreCommissionDebt } from '../../storeCommissionDebt';
import { STATUS_DEBT_ADJUSTMENT_VERSION } from '../../../../constants/debtCommission';

interface IOriginalDataProps<T extends AnyObject> {
  columns: TableProps<T>['columns'];
  initialValueTransaction?: T[];
  filterFields: string[];
}

const OriginalData = <T extends AnyObject>(props: IOriginalDataProps<T>) => {
  const { columns, initialValueTransaction, filterFields } = props;
  const [dataTransaction, setDataTransaction] = useState<T[]>([]);

  const { initialValue } = useStoreCommissionDebt();

  const isConfirm = !!initialValue?.adjustmentVersions?.find(
    item => item?.status === STATUS_DEBT_ADJUSTMENT_VERSION.CONFIRMED,
  );
  useEffect(() => {
    if (initialValueTransaction) {
      setDataTransaction(initialValueTransaction || []);
    }
  }, [initialValueTransaction]);

  const handleSearch = (value: unknown) => {
    if (typeof value !== 'string') return;

    if (!value.trim() && initialValueTransaction) {
      setDataTransaction(initialValueTransaction);
    } else {
      const filteredData = filterData(initialValueTransaction || [], value, filterFields);
      setDataTransaction(filteredData);
    }
  };

  return (
    <div>
      <Flex justify="space-between" style={{ marginBottom: 16 }}>
        <InputSearch showParams={false} style={{ maxWidth: 350 }} onChange={handleSearch} />
        <GroupButtonActionDebt
          versionAdjustment={initialValue?.adjustmentVersions?.[0]}
          checkButtonConfirm={!isConfirm}
          isDisabledButtonUpload={!(initialValueTransaction?.length === 0)}
        />
      </Flex>
      <Table
        dataSource={dataTransaction || []}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        pagination={false}
      />
    </div>
  );
};

export default OriginalData;
