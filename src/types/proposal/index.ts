import { RcFile } from 'antd/es/upload';
import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';
import { TDepositContractForm } from '../contract/depositContract';

export interface IProposal {
  id?: string;
  code?: string;
  eappNumber?: string;
  type?: string;
  sheetName?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: TCreatedBy;
  updatedBy?: TCreatedBy;
  urlEapp?: string;
  url?: string;
  noneTemp?: boolean;
  ticketId?: number;
  sheetUrl?: string;
  files?: TAttachment[];
  name?: string; //tên file tờ trình trường hợp không tệp mẫu
  hasTemplate?: number;
  salePolicy?: TSalePolicy;
  commissionPolicy?: TSalePolicy;
}

export type TShare = {
  emails: string[];
};

export type TIdentities = {
  type?: string;
  value?: string;
  date?: string | Dayjs | null;
  place?: string;
};
export type TAttachment = {
  name?: string;
  file?: RcFile;
  url?: string;
  id?: string;
  uid?: string;
};

export interface IDetailProposal {
  data: IProposal;
}

export type TCreateProposal = {
  id?: string;
  type?: string;
  sheetName?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  urlEapp?: string;
  url?: string;
  ticketId?: number;
  sheetUrl?: string;
  files?: TAttachment[];
  contract?: TDepositContractForm;
  salePolicy?: TSalePolicy;
  commissionPolicy?: TSalePolicy;
};

export type TResponseCreateProposal = {
  account: string;
  proposal: IProposal;
  statusGetData?: string;
  statusUpdateDB?: string;
};

export type TResponsiveImport = {
  status: 'PENDING' | 'SUCCESS' | 'PARTIAL_ERROR' | 'ENTIRE_ERROR'; // Assuming possible statuses
  fileUrl: string;
  fileName: string;
  processBy: string;
  id: string;
  createdDate: string; // Consider using Date type if parsing is needed
  updatedDate: string;
};

export type TProposalType = {
  code: string;
  name: string;
  serviceId: string;
};

export type TStatusType = {
  code: string;
  name: string;
};

export type TEapData = {
  code: string;
  data: { requestCode: string; ticketId: string; url: string };
};

export type TSubmitNoneSampleProposal = {
  eapData: TEapData;
};

export type TSalePolicy = {
  code?: string;
  name?: string;
  id?: string;
};
