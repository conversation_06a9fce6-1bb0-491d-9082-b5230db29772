import { AddressObject } from '../project/project';
interface Deposit {
  id: string;
  code: string;
  name: string;
}
interface PolicyPayment {
  defaultApply: boolean;
  active: boolean;
  status: string;
  type: string;
  description: string;
  _id: string;
  name: string;
  code: string;
  project: {
    id: string;
    name: string;
  };
  schedule: {
    installments: Installment[];
    templates: any[];
    templateFiles: any[];
  };
  startDate: string;
  expiredDate: string;
  productQuantity: number;
  signedDate: string;
  files: any[];
  createdDate: string | null;
  modifiedDate: string;
  modifiedBy: string;
  createdBy: string;
  id: string;
  eventName: string;
  actionName: string;
  __v: number;
  reason: string;
}
interface Receipt {
  id: string;
  amount: number;
  code: string;
  status: string;
  type: string;
  receiptNum: string;
  receiptDate: string;
}
export interface Installment {
  type: string;
  typeRealEstate: string;
  expiredDateType: string;
  exactDays: string | null;
  name: string;
  value: number;
  expiredDays: string;
  isToContract: boolean;
  type2: string;
  value2: number;
  totalAmount: number;
  totalTransfered: number;
  receipts: Receipt[];
  paymentDueDate: string;
}
interface Attribute {
  id: string;
  attributeId: string;
  attributeName: string;
  textPrompt: string;
  controlType: string;
  displayOrder: number;
  isRequired: boolean;
  groupName: string;
  groupOrder: number;
  value: string;
  class: string;
  from: number | string;
  to: number | string;
  config: {
    step: number;
    floor?: number;
    ceil?: number;
    from?: string;
    to?: string;
    min?: string;
    max?: string;
  };
  list?: { key: string; value: string }[];
}
interface PropertyUnit {
  salesProgram: SalesProgram & {
    dwellTime: number;
    dwellTimeReBooking: number;
    dwellTimeBookingPriority: number;
    isWorkingTime: boolean;
    skipCountHasCustomer: boolean;
    workingTime: any[];
  };
  price: number;
  priceVat: number;
  priceAbove: number;
  priceAboveVat: number;
  housePriceVat: number;
  landPriceVat: number;
  housePrice: number;
  contractPrice: number;
  contractPriceForMaintenanceFee: number;
  landPrice: number;
  pos: {
    parentId: string;
    id: string;
    name: string;
    isRegister: boolean;
  };
  isHot: boolean;
  area: number;
  bedroom: string;
  toilet: number;
  title: string;
  description: string;
  active: boolean;
  modifiedBy: string;
  isPublic: boolean;
  isDisplay: boolean;
  lifeCycleStatus: string;
  processBy: string | null;
  lastAssignedDate: string | null;
  authenticationHistory: any[];
  proposalApproved: boolean;
  primaryStatus: string;
  stage: number;
  isRemove: boolean;
  extendable: boolean;
  publishPrice: boolean;
  extendPos: string | null;
  dwellTimeReBooking: number;
  image360: string;
  lstImage: any[];
  _id: string;
  code: string;
  shortCode: string;
  block: string;
  status: string;
  floor: string;
  direction: string;
  outsideArea: number;
  constructStatusXD: string;
  project: {
    id: string;
    name: string;
  };
  attributes: Attribute[];
  category: {
    id: string;
  };
  source: string;
  id: string;
  histories: {
    modifiedDate: string;
    prevPosId: string;
    posId: string;
    posName: string;
    modifiedBy: string;
    modifiedByName: string;
    reason: string;
    primaryStatus: string;
    actionName: string;
  }[];
  createdDate: string;
  processedDate: string;
  priorities: {
    posId: string;
    posName: string;
    employeeId: string;
    employeeName: string;
    priority: number;
  }[];
  historiesHandover: any[];
  modifiedDate: string;
  floorImages: any[];
  __v: number;
  registeredDate: string;
  registeredPos: {
    parentId: string;
    id: string;
    name: string;
  };
  returnDate: string;
}
interface MaintenanceFee {
  value: number;
  stagePayment: string;
  type: string;
}
interface CompanyInformation {
  haveValue: boolean;
  documentNo: string;
  authority: string;
  dateOfIssue: string;
  companyAddress: string;
  companyTel: string;
  companyEmail: string;
  companyAccountNumber: string;
  companyBank: string;
  representative: string;
  representativePosition: string;
}

interface Interest {
  title: string;
  customer: string;
  rate: number;
  needTransfer: number;
  delayFrom: string;
  delayTo: string;
  totalDelayDate: number;
  amount: number;
}
interface Contract {
  maintenanceFee: MaintenanceFee;
  status: string;
  policyDiscounts: any[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  isTransferred: boolean;
  currency: string;
  type: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  interestCalculations: any[];
  description: string;
  active: boolean;
  createdBy: string;
  modifiedBy: string;
  files: any[];
  filesDelivery: any[];
  paymentPercent: number;
  isSendDelivery: boolean;
  handoverStatus: string;
  handoverSchedule: string | null;
  tradeHistory: any[];
  pathDtt: any[];
  changeInstallment: boolean;
  _id: string;
  startDate: string | null;
  expiredDate: string | null;
  signedDate: string;
  transferType: string;
  companyInformation: CompanyInformation;
  releaseStartDate: string | null;
  releaseEndDate: string | null;
  createdDate: string;
  primaryTransaction: {
    project: Project;
    id: string;
    customer: Customer;
    customer2: {
      info: {
        onlyYear: boolean;
      };
      personalInfo: {
        identities: any[];
      };
      identities: any[];
    };
    bookingTicketCode: string | null;
    escrowTicketCode: string;
    propertyUnit: PropertyUnit;
  };
  policyPayment: PolicyPayment;
  code: string;
  name: string;
  modifiedDate: string;
  deliveryItems: any[];
  deliveryHistories: any[];
  __v: number;
  interest: Interest;
  deposit: Deposit;
  id: string;
}
export interface ContractLiquidation {
  status: string;
  type: string;
  description: string;
  createdBy: string;
  liquidationDate: string;
  contract: Contract;
  productCode: string;
  customer: Customer;
  code: string;
  name: string;
  modifiedBy: string;
  id: string;
  eventName: string;
  actionName: string;
  createdDate: string;
  modifiedDate: string;
  __v: number;
}

export interface ContractLiquidationOrderMoney {
  _id: string;
  interestCalculations: {
    interestAmount?: number;
    interestAmountTransferred?: number;
    interestReductionAmount?: number;
    remainingAmount?: number;
    startDate?: string;
    endDate?: string;
    dayOfLatePayment?: number;
    installmentName?: string;
    principalAmount?: number;
    id?: string;
    createdDate?: { $date?: string };
    code?: string;
    title?: string;
    interestRate?: number;
    status?: string;
    totalDelayDate?: number;
    debtage?: { name?: string };
    needTransfer?: number;
    latePaymentFee?: number;
    amount?: number;
    totalSettlementAmount?: number;
  }[];
  primaryTransaction: {
    project: {
      id: string;
      name: string;
      code: string;
      setting: {
        openingTimeSale: string;
        endTimeSale: string;
        bannerStartTime: string;
        bannerEndTime: string;
        timeRegister: string;
        amountRegistration: number;
        amountByTypes: any[];
        dateToIssue: string;
        dateBeforeFirstDebtRemind: number[];
        dateBeforeNextDebtRemind: number[];
        interestRate: number;
        daysPerYear: number;
        delayDay: number;
        onlyWorkday: boolean;
        recipientsMaximum: string;
        subscriptionsMaximum: string;
        isRetailCustomer: boolean;
        isManualReceipt: boolean;
        requireDocument: string[];
        rootPos: null;
        marketPos: {
          parentId: null;
          name: string;
          code: string;
          id: string;
        };
        salesUnit: {
          id: string;
          name: string;
          staffIds: null;
          code: string;
          additionalFields: {
            posInfo: {
              name: string;
              role: string;
              cty: string;
              sms: {
                bookingApprove: string;
                success: string;
                brandName: string;
              };
              allowOnlinePayment: string;
              sendMailSale: string;
            };
            devBusinessIds: any[];
          };
          parentId: string;
          permissionNum: number;
          erpAccount: string;
          unitType: string;
        }[];
        livestreamUrl: string;
        liveChatUrl: string;
        guideVideoUrl: string;
        smallBannerUrl: string;
        bigBannerUrl: string;
        spBannerUrl: string;
        eventName: string;
        eventCode: string;
        adviceType: null;
        paymentTypes: string[];
        paymentPolicies: any[];
        discountPolicies: any[];
        templateFiles: {
          name: string;
          url: string;
          absoluteUrl: string;
          uploadName: string;
        }[];
        templateFileYCDCO: {
          name: string;
          url: string;
          absoluteUrl: string;
          uploadName: string;
        }[];
        refundTemplateUrl: any[];
        receiptTemplateUrl: any[];
        unitTableTemplateUrl: any[];
        confirmTime: string;
        vendorCode: string;
        campaignErp: string;
        prefixReceipt: string;
        formatDescReceipt: string;
        templateFileHasStatus: {
          stage: number[];
          apartmentTypes: null;
          pos: null;
          fileId: string;
          file: {
            name: string;
            url: string;
            absoluteUrl: string;
            uploadName: string;
          };
          status: string[];
        }[];
        templateFileHasType: {
          stage: any[];
          apartmentTypes: any[];
          pos: any[];
          fileId: string;
          file: {
            name: string;
            url: string;
            absoluteUrl: string;
            uploadName: string;
          };
          status: null;
          type: string;
        }[];
        templateFileContract: any[];
        salesProgram: {
          id: string;
          name: string;
        }[];
        isSyncCRMInvestor: boolean;
        allowOnlinePayment: boolean;
        allowSubPayment: boolean;
        onlinePaymentMethods: string[];
      };
    };
    id: string;
    customer: {
      info: {
        onlyYear: boolean;
        gender: string;
        birthday: string;
        birthdayYear: null;
        address?: AddressObject;
        rootAddress?: AddressObject;
        taxCode: string;
        age: number;
      };
      personalInfo: {
        identities: {
          _id: string;
          value: string;
          date: string;
          place: string;
        }[];
        email: string;
        phone: string;
        name: string;
      };
      company: {
        address?: any;
        number: string;
        name: string;
        issueDate: string;
        issueLocation: string;
      };
      identities: {
        _id: string;
        value: string;
        date: string;
        place: string;
      }[];
      code: string;
      id: null;
      taxCode: string;
      bankInfo: {
        bank: string;
        value: string;
        branch: string;
        accountNumber: string;
        syntaxTransfer: string;
        CODE: string;
        NAME: string;
        LOGO: string;
        TYPE: string;
        INDEX: string;
      };
      position: string;
      type?: string;
      employee: {
        staffIds: string[];
        workingAt: string;
        managerAt: null;
        pos: {
          staffIds: string[];
          parentId: string;
          name: string;
          code: string;
          type: string;
          id: string;
          managerId: string;
        };
        id: string;
        code: string;
        email: string;
        erp: {
          code: string;
          url: string;
        };
        images: {
          list: any[];
          zones: any[];
          videos: { url: string }[];
        };
        name: string;
        phone: string;
      };
    };
    customer2: {
      info: { onlyYear: boolean };
      personalInfo: { identities: any[] };
      identities: any[];
    };
    bookingTicketCode: null;
    escrowTicketCode: string;
    propertyUnit: {
      salesProgram: {
        dwellTime: number;
        dwellTimeReBooking: number;
        dwellTimeBookingPriority: number;
        isWorkingTime: boolean;
        workingTime: any[];
        id: string;
        name: string;
      };
      price: number;
      priceVat: number;
      priceAbove: number;
      priceAboveVat: number;
      housePriceVat: number;
      landPriceVat: number;
      housePrice: number;
      contractPrice: number;
      contractPriceForMaintenanceFee: number;
      landPrice: number;
      pos: {
        parentId: string;
        id: string;
        name: string;
        isRegister: boolean;
      };
      isHot: boolean;
      area: number;
      bedroom: string;
      toilet: number;
      title: string;
      description: string;
      active: boolean;
      modifiedBy: string;
      isPublic: boolean;
      isDisplay: boolean;
      lifeCycleStatus: string;
      processBy: null;
      lastAssignedDate: null;
      authenticationHistory: any[];
      proposalApproved: boolean;
      primaryStatus: string;
      stage: number;
      isRemove: boolean;
      extendable: boolean;
      publishPrice: boolean;
      extendPos: null;
      dwellTimeReBooking: number;
      image360: string;
      lstImage: any[];
      _id: string;
      code: string;
      shortCode: string;
      block: string;
      status: string;
      floor: string;
      direction: string;
      outsideArea: number;
      constructStatusXD: string;
      syncErpData: { anal_ppt9: string };
      project: { id: string };
      attributes: {
        id: string;
        attributeId: string;
        attributeName: string;
        textPrompt: string;
        controlType: string;
        displayOrder: number;
        isRequired: boolean;
        groupName: string;
        groupOrder: number;
        value: string;
        class: string;
        from: number;
        to: number;
        config: {
          step: number;
          floor: number;
          ceil: number;
          from: string;
          to: string;
          min: string;
          max: string;
        };
      }[];
      category: { id: string };
      source: string;
      histories: {
        modifiedDate: string;
        posId: string;
        posName: string;
        modifiedBy: string;
        modifiedByName: string;
        reason: string;
        primaryStatus: string;
        actionName: string;
      }[];
      createdDate: string;
      processedDate: string;
      priorities: {
        posId: string;
        posName: string;
        employeeId: string;
        employeeName: string;
        priority: number;
      }[];
      historiesHandover: any[];
      modifiedDate: string;
      floorImages: any[];
      __v: number;
      extData: { chietkhau: string };
      employeeTakeCare: {
        id: string;
        email: string;
      };
      id: string;
      f1: null;
      reasonRevoke: string;
      registeredDate: string;
      registeredPos: {
        parentId: string;
        id: string;
        name: string;
      };
      registeredQueue: null;
      revokeDate: string;
      returnDate: string;
    };
  };
  policyPayment: {
    defaultApply: boolean;
    active: boolean;
    status: string;
    type: string;
    description: string;
    _id: string;
    name: string;
    code: string;
    project: {
      id: string;
      name: string;
    };
    schedule: {
      installments: {
        type: string;
        typeRealEstate: string;
        expiredDateType: string;
        exactDays: null;
        name: string;
        value: number;
        expiredDays: string;
        descriptionProgress: string;
        totalAmount: number;
        totalTransfered: number;
        receipts: {
          id: string;
          amount: number;
          code: string;
          status: string;
          type: string;
          receiptNum: string;
          receiptDate: string;
        }[];
        paymentDueDate: string;
      }[];
      templates: any[];
      templateFiles: any[];
    };
    startDate: string;
    expiredDate: string;
    productQuantity: number;
    signedDate: string;
    files: any[];
    createdDate: null;
    modifiedDate: string;
    modifiedBy: string;
    createdBy: string;
    id: string;
    eventName: string;
    actionName: string;
    __v: number;
    reason: string;
  };
  name: string;
  id: string;
}

export interface ICreateOfferRefundMoneyAccountancy {
  bankName?: string;
  bankNumber?: string;
  collectMoneyDate?: string;
  contentBank?: string;
  contractId?: string;
  description?: string;
  files?: {
    uid: string;
    name: string;
    url: string;
    absoluteUrl: string;
    uploadName: string;
  }[];
  money?: number;
  propertyTicket?: {
    id?: string;
  };
  state?: string;
  type?: string;
}

export interface ICreateOfferOrderMoneyAccountancy {
  bankName?: string;
  bankNumber?: string;
  collectMoneyDate?: string;
  contentBank?: string;
  contractId?: string;
  description?: string;
  money?: number;
  interestCalculations?: {};
  interestAmount?: number;
  isInterest?: boolean;
  totalMoney?: number;
  paymentBatch?: string;
  files?: {
    uid: string;
    name: string;
    url: string;
    absoluteUrl: string;
    uploadName: string;
  }[];
  propertyTicket?: {
    id?: string;
    customer?: {
      name?: string;
      address?: AddressObject;
    };
  };
  state?: string;
  type?: string;
}
export interface IOfferOrderMoneyAccountancy {
  _id?: string;
  status?: string;
  finishDate?: string;
  transferedMoney?: number;
  transferringMoney?: number;
  contractRequireMoney?: number;
  contractReceiptMoney?: number;
  propertyTickets?: string[];
  createdById?: string;
  posId?: string;
  pos?: {
    nameVN?: string;
    name?: string;
    code?: string;
    id?: string;
  };
  contractId?: string;
  files?: string[];
  description?: string;
  active?: boolean;
  modifiedBy?: string;
  change?: number;
  reason?: string;
  receiptNum?: null;
  totalAmountPrint?: 1;
  unCheckAmount?: boolean;
  isInterest?: boolean;
  isReCall?: boolean;
  f1?: boolean;
  sendEmailHistories?: string[];
  currency?: string;
  type?: string;
  state?: string;
  money?: number;
  date?: string;
  bankName?: string;
  bankNumber?: string;
  contentBank?: string;
  receiptDate?: string;
  propertyTicket?: PropertyTicket;
  code?: string;
  createdBy?: {
    email?: string;
    id?: string;
    name?: string;
  };
  id?: string;
  createdAt?: string;
  updatedAt?: string;
}

export type TabOfferOrderMoneyAccountancy = 'WAITING_TRANSFER' | 'TRANSFERED';

export interface IOfferRefundMoneyAccountancy {
  urleApp?: string;
  eappNumber?: string;
  ticketStatus?: string;
  receiptDate?: string;
  pos?: {
    name?: string;
  };
  reciept?: {
    code?: string;
    amount?: number;
  };
  customer?: {
    personalInfo?: {
      name?: string;
      phone?: string;
    };
    identities?: [
      {
        value?: string;
      },
    ];
  };
  _id?: string;
  status?: string;
  finishDate?: string;
  transferedMoney?: number;
  transferringMoney?: number;
  contractRequireMoney?: number;
  contractReceiptMoney?: number;
  propertyTickets?: string[];
  createdById?: string;
  posId?: string;
  files?: string[];
  description?: string;
  active?: boolean;
  modifiedBy?: string;
  change?: number;
  reason?: string;
  receiptNum?: null;
  totalAmountPrint?: number;
  unCheckAmount?: boolean;
  isInterest?: boolean;
  isReCall?: boolean;
  f1?: boolean;
  sendEmailHistories?: string[];
  currency?: string;
  type?: string;
  state?: string;
  money?: 1;
  date?: string;
  bankName?: string;
  bankNumber?: string;
  contentBank?: string;
  propertyTicket?: PropertyTicket;
  code?: string;
  createdBy?: {
    email?: string;
    id?: string;
    name?: string;
  };
  id?: string;
  createdAt?: string;
  updatedAt?: string;
  transaction?: {
    propertyTicket?: PropertyTicket;
  };
}

export type TabOfferRefundMoneyAccountancy = 'WAITING_TRANSFER' | 'TRANSFERED';

export interface DetailOfferMoneyAccountancy {
  receiptDate?: string;
  _id: string;
  status: string;
  finishDate: any;
  transferedMoney: number;
  transferringMoney: number;
  contractRequireMoney: number;
  contractReceiptMoney: number;
  propertyTickets: any[];
  createdById: string;
  posId: string;
  files: any[];
  description: string;
  active: boolean;
  modifiedBy: string;
  approvedBy: any;
  approved_details: any;
  change: number;
  reason: string;
  receiptNum: any;
  project?: Project;
  totalAmountPrint: number;
  unCheckAmount: boolean;
  isInterest: boolean;
  isReCall: boolean;
  f1: boolean;
  sendEmailHistories: any[];
  currency: string;
  type: string;
  state: string;
  money: number;
  date: string;
  bankName: string;
  bankNumber: string;
  contentBank: string;
  propertyTicket: PropertyTicket;
  transaction?: TransactionDetails;
  code: string;
  createdBy: CreatedBy;
  id: string;
  createdAt: string;
  updatedAt: string;
  history: any[];
  __v: number;
  relatedTransactions: RelatedTransaction[];
  totalPendingAmount: number;
  totalPaidAmount: number;
  totalRemainingAmount: number;
  note: string;
  transactionFee: number;
}

export interface PropertyTicket {
  project: Project;
  status: string;
  stage: number;
  priority: number;
  isPicked: boolean;
  emailConfirm: boolean;
  sentEmail: boolean;
  sentEmailDate: string;
  description: string;
  active: boolean;
  softDelete: boolean;
  surveys: any[];
  unCheckAmount: boolean;
  syncErpCancel: boolean;
  isRefund: boolean;
  _id: string;
  ticketType: string;
  salesProgramId: string;
  propertyUnitId: string;
  propertyUnit?: { code?: string };
  customer: Customer;
  customer2: Customer2;
  files: any[];
  amountRegistration: number;
  note: string | null;
  demandCategory: any;
  bookingTicketCode: string;
  employee: Employee;
  code: string;
  id: string;
  documents: any[];
  reciept: Reciept[];
  modifiedDate: string;
  createdDate: string;
  historyStatus: any[];
  __v: number;
  actionName: string;
  eventName: string;
  escrowTicketCode?: string;
  pos?: {
    name?: string;
    code?: string;
    id?: string;
  };
}

export interface Project {
  surveys: any[];
  f1Projects: any[];
  type: string;
  name: string;
  banks: Bank[];
  projectLeader: ProjectLeader;
  code: string;
  accountants: Accountant[];
  customerServices: CustomerService[];
  setting: Setting;
  f1Pos: any;
  id: string;
}

export interface Bank {
  code: string;
  name: string;
  accountNumber: string;
}

export interface ProjectLeader {
  id: string;
  code: string;
  email: string;
  phone: string;
  pos: Pos;
  name: string;
}

export interface Accountant {
  id: string;
  code: string;
  email: string;
  name: string;
  phone: string;
  pos: Pos;
}

export interface CustomerService {
  id: string;
  code: string;
  email: string;
  name: string;
  phone: string;
  pos: Pos;
  isDVTK: boolean;
}

export interface Setting {
  dateBeforeFirstDebtRemind: any[];
  dateBeforeNextDebtRemind: any[];
  onlyWorkday: boolean;
  isRetailCustomer: boolean;
  isManualReceipt: boolean;
  paymentTypes: any[];
  paymentPolicies: any[];
  discountPolicies: any[];
  templateFileContract: any[];
  salesProgram: SalesProgram[];
  onlinePaymentMethods: any[];
  requireDocument: any[];
  salesUnit: SalesUnit[];
  templateFileHasStatus: any[];
  templateFileHasType: any[];
  isSyncCRMInvestor: boolean;
  allowOnlinePayment: boolean;
  allowSubPayment: boolean;
  amountByTypes: any[];
  openingTimeSale: any;
  endTimeSale: any;
  bannerStartTime: any;
  bannerEndTime: any;
  timeRegister: any;
  amountRegistration: any;
  dateToIssue: any;
  interestRate: any;
  daysPerYear: any;
  delayDay: any;
  recipientsMaximum: any;
  subscriptionsMaximum: any;
  rootPos: any;
  marketPos: MarketPos;
  livestreamUrl: string;
  liveChatUrl: string;
  guideVideoUrl: string;
  smallBannerUrl: string;
  bigBannerUrl: string;
  spBannerUrl: string;
  eventName: string;
  eventCode: string;
  adviceType: any;
  templateFiles: any[];
  templateFileYCDCO: any[];
  refundTemplateUrl: any[];
  receiptTemplateUrl: any[];
  unitTableTemplateUrl: any[];
  confirmTime: any;
  vendorCode: string;
  prefixReceipt: string;
  formatDescReceipt: string;
}

export interface SalesProgram {
  id: string;
  name: string;
}

export interface SalesUnit {
  id: string;
  name: string;
  code: string;
  additionalFields: AdditionalFields;
  parentId: string;
  permissionNum: number;
  unitType: string;
}

export interface AdditionalFields {
  devBusinessIds: any[];
}

export interface MarketPos {
  parentId: string;
  name: string;
  code: string;
  id: string;
}

export interface Customer {
  id?: string;
  info?: Info;
  personalInfo?: PersonalInfo;
  identities?: Identity[];
  company?: Company;
  code?: string;
  bankInfo?: BankInfo;
  employee?: Employee;
  taxCode?: string;
  position?: string;
  type?: string;
  identityNumber?: string;
  name?: string;
  address?: AddressObject;
}

export interface Company {
  address?: AddressObject;
  number: string;
  name: string;
  issueDate: string;
  issueLocation: string;
}
export interface Info {
  onlyYear: boolean;
  gender: string;
  birthday: string;
  birthdayYear: string;
  address: AddressObject;
  rootAddress: AddressObject;
  taxCode: string;
  age: string;
}

export interface PersonalInfo {
  identities: Identity[];
  email: string;
  phone: string;
  name: string;
  code: string;
}

export interface Identity {
  _id: string;
  value: string;
  date: any;
  place: string;
}

export interface BankInfo {
  name?: string;
  accountNumber?: string;
  beneciary?: string;
  bankCode?: string;
  bankName?: string;
  code?: string;
  beneficiary?: string;
}

export interface Employee {
  staffIds: string[];
  workingAt: string;
  managerAt: any;
  pos: Pos;
  id: string;
  code: string;
  email: string;
  phone: string;
  erp: Erp;
  images: Images;
  name: string;
}

export interface Pos {
  staffIds: any[];
  parentId: any;
  code: string;
  id: string;
  managerId: any;
  name: string;
  type: string;
}

export interface Erp {
  code: string;
  url: string;
}

export interface Images {
  avatar: string;
  list: any[];
  zones: any[];
  videos: Video[];
}

export interface Video {
  url: string;
}

export interface Customer2 {
  info: Info;
  personalInfo: PersonalInfo;
  identities: Identity[];
  code: string;
  bankInfo: BankInfo;
  employee: Employee;
  taxCode: string;
  position: string;
  type: string;
}

export interface Reciept {
  id: string;
  amount: number;
  code: string;
  status: string;
  type: string;
  receiptNum: string;
  receiptDate: any;
}

export interface CreatedBy {
  managerAt: string;
  email: string;
  pos: Pos;
  code: string;
  id: string;
  name: string;
  level1: Level;
  level2: Level;
  level3: Level;
  level4: Level;
  level5: Level;
  level6: Level;
  acountId: string;
}

export interface Level {
  code: string;
  nameVN: string;
}

export interface RelatedTransaction {
  urleApp?: string;
  _id: string;
  status: string;
  finishDate: any;
  transferedMoney: number;
  transferringMoney: number;
  contractRequireMoney: number;
  contractReceiptMoney: number;
  propertyTickets: any[];
  createdById: string;
  posId: string;
  files: any[];
  description: string;
  active: boolean;
  modifiedBy?: string;
  approvedBy: any;
  approved_details: any;
  change: number;
  reason: string;
  receiptNum?: string;
  totalAmountPrint: number;
  unCheckAmount: boolean;
  isInterest: boolean;
  isReCall: boolean;
  f1: boolean;
  sendEmailHistories: any[];
  currency: string;
  type: string;
  state: string;
  money: number;
  date: string;
  bankName: string;
  bankNumber: string;
  contentBank: string;
  propertyTicket: PropertyTicket;
  code: string;
  createdBy: any;
  pos?: Pos;
  id: string;
  createdAt: string;
  updatedAt: string;
  history: any[];
  __v: number;
  bankInfo?: string;
  contractId: any;
  interestCalculations: any;
  payBy?: string;
  payer?: string;
  paymentBatch: any;
  receiptDate?: string;
  syncErp: any;
  transactionFee?: number;
}

export interface Other {
  type: string;
  mainKey: string;
  value?: string;
  arrayData?: ArrayDaum[];
  objectData?: ObjectDaum[];
}

export interface ArrayDaum {
  arrayValue: string;
}

export interface ObjectDaum {
  objectKey: string;
  objectValue: string;
}

export interface ContractDetails {
  maintenanceFee: {
    value: number;
    stagePayment: string;
    type: string;
  };
  status: string;
  policyDiscounts: {
    defaultApply: boolean;
    active: boolean;
    status: string;
    type: string;
    description: string;
    _id: string;
    name: string;
    code: string;
    project: {
      id: string;
      name: string;
    };
    discount: {
      value: string;
      description: string;
      typeRealEstate: string;
      type: string;
    };
    startDate: string;
    expiredDate: string;
    productQuantity: number;
    signedDate: string;
    files: any[];
    createdDate: string | null;
    createdBy: string;
    modifiedDate: string;
    modifiedBy: string;
    id: string;
    eventName: string;
    actionName: string;
    __v: number;
    reason: string;
    schedule: any | null;
  }[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  isTransferred: boolean;
  currency: string;
  type: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  interestCalculations: {
    interestAmount: number;
    interestAmountTransferred: number;
    interestReductionAmount: number;
    remainingAmount: number;
    startDate: string;
    endDate: string;
    dayOfLatePayment: number;
    installmentName: string;
    principalAmount: number;
    id: string;
    createdDate: { date: string } | string;
    code: string;
    title: string;
    interestRate: number;
    status: string;
    totalDelayDate: number;
    debtage: { name: string } | null;
    needTransfer: number;
    latePaymentFee: number;
    amount: number;
    totalSettlementAmount: number;
  }[];
  description: string;
  active: boolean;
  createdBy: string;
  modifiedBy: string;
  files: any[];
  filesDelivery: any[];
  paymentPercent: number;
  isSendDelivery: boolean;
  handoverStatus: string;
  handoverSchedule: string | null;
  tradeHistory: any[];
  pathDtt: any[];
  changeInstallment: boolean;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: number;
  staffsInvolved: any[];
  isInterestExemption: boolean;
  assignStatus: string;
  debtHistory: {
    histories: any[];
  };
  isAddCoOwnerShipInfo: boolean;
  isAddCompanyInfo: boolean;
  _id: string;
  startDate: string;
  expiredDate: string;
  signedDate: string;
  transferType: string;
  companyInformation: CompanyInformation;
  releaseStartDate: string;
  releaseEndDate: string;
  createdDate: string;
  primaryTransaction: {
    project: {
      id: string;
      name: string;
      code: string;
      setting: any;
    };
    id: string;
    customer: any;
    customer2: any;
    bookingTicketCode: string | null;
    escrowTicketCode: string;
    propertyUnit: any;
  };
  policyPayment: any;
  code: string;
  name: string;
  id: string;
  modifiedDate: string;
  deliveryItems: any[];
  deliveryHistories: any[];
  __v: number;
  assignHistories: any[];
  callRecords: any[];
  notes: any[];
  interest: Interest;
  projectType: string;
  transferHistories: any[];
}

export interface TransactionDetails {
  interestAmount: number;
  paymentBatch: string;
  _id: string;
  status: string;
  finishDate: string | null;
  transferedMoney: number;
  transferringMoney: number;
  contractRequireMoney: number;
  contractReceiptMoney: number;
  propertyTickets: any[];
  createdById: string;
  posId: string;
  files: {
    uid: string;
    name: string;
    url: string;
    absoluteUrl: string;
    uploadName: string;
  }[];
  description: string;
  active: boolean;
  modifiedBy: string;
  approvedBy: string | null;
  approved_details: string | null;
  change: number;
  reason: string;
  receiptNum: string | null;
  totalAmountPrint: number;
  unCheckAmount: boolean;
  isInterest: boolean;
  isReCall: boolean;
  f1: boolean;
  sendEmailHistories: any[];
  currency: string;
  bankName: string;
  bankNumber: string;
  collectMoneyDate: string;
  contentBank: string;
  contractId: string;
  money: number;
  propertyTicket: PropertyTicket;
  state: string;
  type: string;
  code: string;
  createdBy: CreatedBy;
  pos: Pos;
  date: string;
  primaryContract: PrimaryContract;
  name: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  history: any[];
  __v: number;
  relatedTransactions: RelatedTransaction[];
  totalPendingAmount: number;
  totalPaidAmount: number;
  totalRemainingAmount: number;
  interestCalculations?: InterestReceipt;
}

export interface InterestReceipt {
  principalAmount?: number;
  interestRate?: number;
  interestAmount?: number;
  interestAmountTransferred?: number;
  interestReductionAmount?: number;
  remainingAmount?: number;
  dayOfLatePayment?: number;
  id?: string;
  installmentName?: string;
  isStartCalcInterest?: boolean;
  createdDate?: string;
  code?: string;
  title?: string;
  debtage?: {
    name?: string;
    fromDay?: number;
    toDay?: number;
    interestRate?: number;
    isFinalRange?: boolean;
    isBadDebt?: boolean;
  };
  needTransfer?: number;
  delayFrom?: string;
  delayTo?: string;
  totalDelayDate?: number;
  latePaymentFee?: number;
  totalSettlementAmount?: number;
  status?: string;
}

interface PolicyDiscount {
  defaultApply: boolean;
  active: boolean;
  status: 'approved' | string;
  type: 'discount' | string;
  description: string;
  _id: string;
  name: string;
  code: string;
  project: { id: string; name: string };
  discount: {
    value: string;
    description: string;
    typeRealEstate: string;
    type: string;
  };
  startDate: string;
  expiredDate: string;
  signedDate: string;
  productQuantity: number;
  files: any[];
  createdDate: string | null;
  createdBy: string;
  modifiedDate: string;
  modifiedBy: string;
  id: string;
  eventName: string;
  actionName: string;
  __v: number;
  reason: string;
  schedule: any | null;
}

interface InterestCalculation {
  principalAmount: number;
  interestRate: number;
  interestAmount: number;
  interestAmountTransferred: number;
  interestReductionAmount: number;
  remainingAmount: number;
  dayOfLatePayment: number;
  id: string;
  installmentName: string;
  createdDate: string;
  code: string;
  title: string;
  debtage: any | null;
  needTransfer: number;
  delayFrom: string;
  delayTo: string;
  totalDelayDate: number;
  latePaymentFee: number;
  totalSettlementAmount: number;
  status: string;
}

interface PrimaryTransaction {
  project: any;
  id: string;
  customer: Customer;
  customer2: any;
  bookingTicketCode: string | null;
  escrowTicketCode: string;
  propertyUnit: any;
}
export interface PrimaryContract {
  maintenanceFee: MaintenanceFee;
  status: string;
  policyDiscounts: PolicyDiscount[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  isTransferred: boolean;
  currency: string;
  type: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  interestCalculations: InterestCalculation[];
  description: string;
  active: boolean;
  createdBy: string;
  modifiedBy: string;
  files: any[];
  filesDelivery: any[];
  paymentPercent: number;
  isSendDelivery: boolean;
  handoverStatus: string;
  handoverSchedule: any | null;
  tradeHistory: any[];
  pathDtt: any[];
  changeInstallment: boolean;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: number;
  staffsInvolved: any[];
  _id: string;
  startDate: string;
  expiredDate: string;
  signedDate: string;
  transferType: string;
  companyInformation: CompanyInformation;
  releaseStartDate: string;
  releaseEndDate: string;
  createdDate: string;
  primaryTransaction: PrimaryTransaction;
  policyPayment: PolicyPayment;
  code: string;
  name: string;
  id: string;
  modifiedDate: string;
  deliveryItems: any[];
  deliveryHistories: any[];
  __v: number;
  assignStatus: string;
  assignHistories: any[];
  callRecords: any[];
  debtHistory: { histories: any[] };
  isAddCoOwnerShipInfo: boolean;
  isAddCompanyInfo: boolean;
  isInterestExemption: boolean;
  notes: any[];
  interest: {
    title: string;
    customer: string;
    rate: number;
    needTransfer: number;
    delayFrom: string;
    delayTo: string;
    totalDelayDate: number;
    amount: number;
    name: string;
  };
}
