import { DefaultOptionType } from 'antd/lib/select';
import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';

export interface ICommissionPolicy {
  id?: string;
  code?: string;
  name?: string;
  startDate?: Dayjs;
  endDate?: Dayjs;
  policyType?: string | number;
  listPos?: TPos;
  isActive: number;
  isProgressive?: boolean;
  isVAT?: boolean;
  listRate?: TRate[];
  createdDate?: string;
  createdBy?: TCreatedBy;
  modifiedDate?: string;
  modifiedBy?: TCreatedBy;
  commissionRateManage?: TUser;
  commissionRateRevenue?: TUser;
  iscommissionListUsed?: boolean;
  status?: string;
  year?: number;
  commissionRateManageNVKD?: string;
  commissionRateManageTPBH?: string;
  commissionRateManageGDBH?: string;
  commissionRateManageGDKD?: string;
  commissionRateRevenueNVKD?: string;
  commissionRateRevenueTPBH?: string;
  commissionRateRevenueGDBH?: string;
  commissionRateRevenueGDKD?: string;
  periodFrom: string;
  periodTo: string;
  periodName: string;
}

export type TUser = {
  userNVTV: number;
  userNVKD: number;
  userTPBH: number;
  userGDBH: number;
  userGDKD: number;
  defaultRate: number;
};

export type TRateManage = {
  id: string;
  userNVTV?: number;
  userNVKD?: number;
  userTPBH?: number;
  userGDBH?: number;
  userGDKD?: number;
  manageUserNVTV?: number;
  manageUserNVKD?: number;
  manageUserTPBH?: number;
  manageUserGDBH?: number;
  manageUserGDKD?: number;
  manageDefaultRate?: number;
  manageBottomPrice?: string;
  isNew?: boolean;
};

export type TRate = {
  key: string;
  name?: string;
  commissionRateManage?: TUser;
  commissionRateRevenue?: TUser;
  bottomPrice?: string;
  calculateType?: string;
  unit?: number;
  topPrice?: string;
  listUser?: TUser;
  userNVTV?: number;
  userNVKD?: number;
  userTPBH?: number;
  userGDBH?: number;
  userGDKD?: number;
  policyType: string;
  isNew?: boolean;
  manageBottomPrice?: string;
};

export type TIdentities = {
  type?: string;
  value?: string;
  date?: string | Dayjs | null;
  place?: string;
  typeObject?: DefaultOptionType;
};

export type TCreateCommissionPolicy = {
  id?: string;
  code?: string;
  name?: string;
  startDate?: string;
  endDate?: string;
  policyType?: string;
  listPos?: TPos;
  isActive?: number;
  isProgressive?: boolean;
  isVAT?: boolean;
  listRate?: TRate[];
  createdDate?: string;
  createdBy?: string;
  updatedDate?: string;
  updatedBy?: string;
  commissionRateManage?: TUser;
  commissionRateRevenue?: TUser;
  year?: number;
  period?: string;
  periodFrom?: string;
  periodTo?: string;
  periodName?: string;
  commissionRateManageNVKD?: string;
  commissionRateManageTPBH?: string;
  commissionRateManageGDBH?: string;
  commissionRateManageGDKD?: string;
  commissionRateRevenueNVKD?: string;
  commissionRateRevenueTPBH?: string;
  commissionRateRevenueGDBH?: string;
  commissionRateRevenueGDKD?: string;
};

export interface IDetailProposal {
  data: ICommissionPolicy;
}

export type TCreateProposal = {
  id?: string;
  type?: string;
  sheetName?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  urlEapp?: string;
  url?: string;
  ticketId?: number;
  sheetUrl?: string;
  // files?: TAttachment[];
};

export type TResponsiveImport = {
  status: 'PENDING' | 'SUCCESS' | 'PARTIAL_ERROR' | 'ENTIRE_ERROR'; // Assuming possible statuses
  fileUrl: string;
  fileName: string;
  processBy: string;
  id: string;
  createdDate: string; // Consider using Date type if parsing is needed
  updatedDate: string;
};

export type TPos = {
  code: string;
  name: string;
  value: string;
  label: string;
  id: string;
};

export type TFilterHistoryCommissionImport = {
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  processBy?: string;
  type?: string;
};
