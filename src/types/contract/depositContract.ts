// import { Dayjs } from 'dayjs';
// import { TCreatedBy } from '../common/common';

import { UploadFile } from 'antd/es/upload/interface';
import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';
import { IPaymentPolicy } from '../paymentPolicy';

export type TListDepositContract = {
  _id: string;
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  sapCode?: string;
  startDate: string | Dayjs | null;
  expiredDate: string | Dayjs | null;
  signedDate: string | Dayjs | null;
  primaryTransaction: PrimaryTransaction;
  policyPayment: object;
  policyDiscount: object;
  salesPolicy: object;
  staffsInvolved: [];
  policyDiscounts: PolicyDiscounts[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    type: string;
    value: number;
    stagePayment: string;
  };

  oldContract: object;
  isTransferred: boolean;
  currency: string;
  transferType: string;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  interest: object;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  liquidation: object;

  interestCalculations: [];
  description: string;
  active: boolean;
  createdBy: string;
  createdDate: string | Dayjs | null;
  modifiedBy: string;
  modifiedDate: string | Dayjs | null;
  deposit: object;
  purchase: object;
  files: [];
  filesDelivery: [];
  paymentPercent: number;
  deliveryItems: [];
  deliveryDate: string | Dayjs | null;
  isSendDelivery: boolean;
  deliveryHistories: [];
  deliveryResult: object;
  handoverStatus: {
    type: string;
    default: string;
  };
  handoverSchedule: object;
  companyInformation: CompanyInformation;
  tradeHistory: object;
  pathDtt: [];
  syncErpData: object;
  changeInstallment: boolean;
  releaseStartDate: string | Dayjs | null;
  releaseEndDate: string | Dayjs | null;
  liquidate: string | Dayjs | null;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: boolean;
};

export type TFilterDepositContract = {
  page?: number;
  pageSize?: number;
  status?: string;
  projectId?: string[] | string | null;
  discountPolicyIds?: string[] | string | null;
  paymentPolicyIds?: string[] | string | null;
  type?: string;
  search?: string;
};

export type TPos = {
  id: string;
  code: string;
  name: string;
};

export type TDepositContract = {
  _id: string;
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  eapStatus?: string;
  startDate: string | Dayjs | null;
  expiredDate: string | Dayjs | null;
  signedDate: string | Dayjs | null;
  primaryTransaction: PrimaryTransaction;
  policyPayment: PolicyPayment;
  policyDiscount: object;
  salesPolicy: {
    id?: string;
    code?: string;
    name?: string;
  };
  staffsInvolved: [];
  policyDiscounts: PolicyDiscounts[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    type: string;
    value: number;
    stagePayment: string;
  };

  oldContract: object;
  isTransferred: boolean;
  currency: string;
  transferType: string;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  interest: object;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  liquidation: object;

  interestCalculations: [];
  description: string;
  active: boolean;
  createdBy: string;
  createdDate: string | Dayjs | null;
  modifiedBy: string;
  modifiedDate: string | Dayjs | null;
  deposit: object;
  purchase: object;
  files: ExtendedUploadFile[];
  filesDelivery: [];
  paymentPercent: number;
  deliveryItems: [];
  deliveryDate: string | Dayjs | null;
  isSendDelivery: boolean;
  deliveryHistories: [];
  deliveryResult: object;
  handoverStatus: {
    type: string;
    default: string;
  };
  handoverSchedule: object;
  companyInformation: CompanyInformation;
  tradeHistory: object;
  pathDtt: [];
  syncErpData: object;
  changeInstallment: boolean;
  releaseStartDate: string | Dayjs | null;
  releaseEndDate: string | Dayjs | null;
  liquidate: string | Dayjs | null;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: boolean;
  distributionChannel?: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory?: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo?: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
  businessArea?: {
    id?: string;
    name?: string;
    code?: string;
  };
};

export type TDepositContractPayload = {
  id?: string;
  primaryTransactionId: string;
  primaryTransaction?: PrimaryTransaction;
  policyPaymentId: string;
  policyDiscountIds: [];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    stagePayment: string;
    type: string;
    value: number;
  };
  type: string;
  personalInfo?: PersonalInfo;
  files: ExtendedUploadFile[];
  startDate: string | Dayjs | null;
  expiredDate?: string | Dayjs | null;
  signedDate?: string | Dayjs | null;
  transferType?: string;
  codeCustomer?: string;
  customer2?: Customer;
  isDebtRemind?: boolean;
  companyInformation?: CompanyInformation;
  changeInstallment?: boolean;
  releaseEndDate?: string;
  releaseStartDate?: string;
  salesPolicy: {
    id: string;
    name: string;
    code?: string;
  };
  staffsInvolved?: staffs[];
  contractDuration?: [string, string];
  releaseDate?: [string, string];
  priceType?: string;
  businessArea?: {
    id?: string;
    name?: string;
    code?: string;
  };
  distributionChannel?: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory?: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo?: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
};

export type TDepositContractForm = {
  primaryTransactionId: string;
  policyPayment: IPaymentPolicy;
  policyDiscountIds: [];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  bankInfo?: BankInfo;
  company?: Company;
  personalInfo?: PersonalInfo;
  info?: Info;
  codeCustomer?: string;
  stagePaymentSelect?: {
    value?: string;
    label?: string;
  };
  maintenanceFee: {
    stagePayment: string;
    type: string;
    value: number;
  };
  customerSelect: {
    id?: string;
    code?: string;
  };
  type: string;
  files: File[];
  address?: {
    addres?: string;
    district: addressObject;
    province: addressObject;
    ward: addressObject;
  };
  rootAddress?: {
    addres?: string;
    district: addressObject;
    province: addressObject;
    ward: addressObject;
  };
  startDate: string | Dayjs | null;
  expiredDate?: string | Dayjs | null;
  signedDate?: string | Dayjs | null;
  transferType?: string;
  isDebtRemind?: boolean;
  companyInformation?: CompanyInformation;
  changeInstallment?: boolean;
  releaseEndDate?: string | Dayjs | null;
  releaseStartDate?: string | Dayjs | null;
  salesPolicy: {
    id: string;
    code: string;
    name: string;
  };
  staffsInvolved?: staffs[];
  contractDuration?: [string, string];
  releaseDate?: [string, string];
  priceType?: string;
  maintenanceFeeValue?: number;
  maintenanceFeeType?: string;
  stagePayment?: string;
  businessArea: {
    id?: string;
    nameVN?: string;
    code?: string;
  };
  distributionChannel: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  loanBankCode?: string;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
};

export interface staffs {
  id: string;
  code: string;
  name: string;
  percent: number;
}

export interface PrimaryTransaction {
  id?: string;
  name?: string;
  bookingTicketCode?: string;

  project?: {
    id?: string;
    code?: string;
    name?: string;
    imageUrl?: string;
  };
  escrowTicketCode?: string;
  customer2?: Customer;
  customer?: {
    personalInfo?: {
      name?: string;
      id?: string;
    };
    company?: {
      name?: string;
    };
    type?: string;
  };
  propertyUnit?: {
    code?: string;
    price?: number;
    priceVat?: number;
    landPrice?: number;
    landPriceVat?: number;
    housePrice?: number;
    housePriceVat?: number;
    contractPriceForMaintenanceFee?: number;
    contractPrice?: number;
  };
  employee?: {
    id?: string;
    name?: string;
    code?: string;
    pos?: {
      id?: string;
      code?: string;
      name?: string;
    };
  };
}
export interface Employee {
  id: string;
  name: string;
  code: string;
}

// Interfaces
export interface Receipt {
  status: string;
  amount: number;
  code: string;
  receiptDate?: string | Dayjs | null;
}
export interface Installment {
  id?: string;
  receipts: Receipt[];
  isToContract: boolean;
  name: string;
  totalAmount: number;
  totalTransfered?: number;
  descriptionProgress?: number;
  paymentDueDate?: string | Dayjs | null;
  transactionSuccessful?: boolean;
  type: string;
  value?: string;
  type2?: string;
  value2?: string;
}
interface PolicyPayment {
  id?: string;
  name?: string;
  schedule?: { installments?: Installment[] };
}

export interface PolicyDiscounts {
  id?: string;
  name?: string;
}

export interface PaymentHistoryData {
  stt: string | number;
  isToContract: boolean;
  name: string;
  totalAmount: number;
  totalTransfered: number;
  totalRemaining: string | number;
  value: string;
  code: string;
  date: string | Dayjs | null;
  descriptionProgress?: number;
  paymentDueDate: string | Dayjs | null;
  transactionSuccessful?: boolean;
}

export interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

export interface Customer {
  id?: string;
  code?: string;
  name?: string;
  gender?: string;
  onlyYear?: boolean;
  birthday?: string;
  birthdayYear?: string;
  identityType?: string;
  phone?: string;
  email?: string;
  taxCode?: string;
  identityNumber?: string;
  identityDate?: string | Dayjs | null;
  identityPlace?: string;
  bankInfo?: BankInfo;
  company?: Company;
  personalInfo?: PersonalInfo;
  info?: Info;
}

export interface CompanyInformation {
  authority?: string;
  companyAccountNumber?: string;
  companyAddress?: string;
  taxCode?: string;
  companyBank?: string;
  companyEmail?: string;
  companyTel?: string;
  dateOfIssue?: Dayjs | null | string;
  documentNo?: string;
  representative?: string;
  representativePosition?: string;
}
interface BankInfo {
  accountNumber?: string;
  beneciary?: string;
  name?: string;
}
interface Company {
  issueDate?: string | Dayjs | null;
  issueLocation?: string;
  issueType?: string;
}
interface PersonalInfo {
  name?: string;
  phone?: string;
  email?: string;
  identities?: Identities[];
}
interface Info {
  gender?: string;
  onlyYear?: boolean;
  birthday?: string | Dayjs | null;
  birthdayYear?: string | Dayjs | null;
  cloneAddress?: boolean;
  address?: {
    addres?: string;
    district?: addressObject;
    province?: addressObject;
    ward?: addressObject;
  };
  rootAddress?: {
    addres?: string;
    district?: addressObject;
    province?: addressObject;
    ward?: addressObject;
  };
}

interface Identities {
  type?: string;
  date?: string | Dayjs | null;
  place?: string;
  value?: string;
  typeObject?: {
    label?: string;
    value?: string;
  };
  _id?: string;
}

interface addressObject {
  name: string;
  code: string;
  nameVN: string;
}

export interface StaffItem {
  key: string;
  id?: string;
  code?: string;
  name?: string;
  percent?: number;
}
export interface StaffFormValues {
  [key: string]: { employee: string; percent: number };
}

export type TInterestCalculation = {
  principalAmount: string;
  interestRate: string;
  interestAmount: string;
  interestAmountTransferred: string;
  interestReductionAmount: string;
  remainingAmount: string;
  dayOfLatePayment: string;
  id: string;
  installmentName: string;
  isStartCalcInterest?: boolean;
  code: string;
  title: string;
  debtage: string;
  debtType: string;
  needTransfer: string;
  delayFrom: string;
  delayTo: string;
  totalDelayDate: string;
  latePaymentFee: string;
  totalSettlementAmount: string;
  status: string;
  updatedDate: string;
  createdDate: string;
  updatedBy: TCreatedBy;
  createdBy: TCreatedBy;
};
export type TLoanBankInfo = {
  id?: string;
  bankCode?: string;
  bankName?: string;
  branchName?: string;
  houseBank?: string;
};
