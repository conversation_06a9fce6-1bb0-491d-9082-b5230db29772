import { Dayjs } from 'dayjs';
import { AddressType } from '../../components/selectAddress';
import { TUpdatedBy } from '../common/common';

export interface ICustomers {
  key?: string;
  id?: string;
  identitiesValue?: string;
  code?: string;
  taxCode?: string;
  lead?: {
    code?: string;
    name?: string;
  };
  isActive?: string | number;
  createdDate?: string;
  updatedDate?: string;
  createdBy?: string;
  updatedBy?: string;
  info?: TInfo;
  personalInfo?: TPersonalInfo;
  bankInfo?: TBankInfo[];
  share?: TShare;
  name?: string;
  phone?: string;
  email?: string;
  idValue?: string[];
  address?: TAddress;
  rootAddress?: TAddress;
  takeNote?: string;
  type?: string;
  continueUpdate?: boolean;
  company?: TCompany;
  companyName?: string;
  addressCompany?: TAddress;
  status?: string;
  identities?: TIdentities;
  updatedByObj?: TUpdatedBy;
  createdByObj?: TUpdatedBy;
  companyShortName?: string;
}

export type TShare = {
  emails: string[];
};

type TCompany = {
  name?: string;
  shortName?: string;
  issueDate?: string | Dayjs | null;
  issueLocation?: string;
  address?: TAddress;
};

type TPersonalInfo = {
  name?: string;
  phone?: string;
  email?: string;
  shortName?: string;
  position?: string;
  job?: string;
  income?: number;
  incomeSource?: string;
  relationshipStatus?: string;
  identities?: TIdentities[];
};

export type TIdentities = {
  type?: string | TSelectBank;
  value?: string;
  date?: string | Dayjs | null;
  place?: string;
};
export type TBankInfo = {
  name?: string | TSelectBank;
  accountNumber?: string;
  code?: string;
  branchCode?: string;
  beneficiary?: string;
  bank?: TSelectBank;
};

type TInfo = {
  gender?: boolean;
  onlyYear?: boolean;
  birthdayYear?: string | Dayjs | null;
  address?: TAddress;
  rootAddress?: TAddress;
  birthday?: string | Dayjs | null;
  cloneAddress?: boolean;
};

type TAddress = {
  address?: string;
  province: AddressType;
  district: AddressType;
  ward: AddressType;
};

export type TSelectBank = {
  label: string; // Nhãn hiển thị
  value: string; // Giá trị duy nhất
  key: string; // Khóa duy nhất (có thể trùng với value)
  code: string; // Mã ngân hàng
  branchCode: string; // Mã chi nhánh
};

export interface IModalCreateCustomer {
  isOpen: boolean;
  toggleModal?: (isVisible: boolean) => void;
  handleCancel: () => void;
}
export interface IModalCheckDuplicate {
  isOpen: boolean;
  handleCancelCheckDuplicate: () => void;
  dataDuplicate?: TDataDuplicate[];
  handleCancel?: () => void;
  dataSubmit: TCreateCustomer & ICustomers;
  removeDataSubmit: () => void;
  type: 'create' | 'update';
  setIsModified?: (value: boolean) => void;
}

export interface IDetailCustomer {
  data: ICustomers;
}

export type TDataDuplicate = {
  name: string;
  code: string;
  identities: string[];
  type?: string;
  email: string;
  id: string;
  taxCode: string;
};

export type TCreateCustomer = {
  type?: string;
  name?: string;
  phone?: string;
  companyName?: string;
  gender?: boolean;
  continueCreate?: boolean; // Mặc định là false
};

export type TEmployeeAll = {
  id?: string;
  name?: string;
  email?: string;
  accountId?: string;
  username?: string;
  cccd?: string;
  dvbh?: string;
  orgCode?: string;
  phoneNumber?: string;
};

export type TListHistoryImportDemandCustomer = {
  status: DemandCustomerStatus;
  fileUrl: string;
  fileName: string;
  processBy: string;
  type: 'DEMAND_CUSTOMER_INVIDIUAL' | 'DEMAND_CUSTOMER_BUSINESS';
  id: string;
  createdDate: string;
  updatedDate: string;
  fail: number;
  success: number;
  failedFileName: string;
  processByObj: TUpdatedBy;
};
type DemandCustomerStatus = 'ENTIRE_ERROR' | 'PARTIAL_SUCCESS' | 'SUCCESS' | 'PENDING' | 'PROCESSING';

export type TFilterHistoryCustomer = {
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  type?: string;
};

export type TResponsiveImport = {
  status: 'PENDING' | 'SUCCESS' | 'PARTIAL_ERROR' | 'ENTIRE_ERROR'; // Assuming possible statuses
  fileUrl: string;
  fileName: string;
  processBy: string;
  type: 'DEMAND_CUSTOMER_INVIDIUAL' | 'DEMAND_CUSTOMER_BUSINESS'; // If there are other types, use a union type
  id: string;
  createdDate: string; // Consider using Date type if parsing is needed
  updatedDate: string;
};

export type IFilterDocumentCustomer = {
  page?: number;
  pageSize?: number;
  search?: string;
  customerID?: string;
};

export type IFormDocumentCustomer = {
  name?: string;
  customerID?: string;
  id?: string;
  type?: string;
  path?: string;
  sizeFile?: number;
};
export type IItemDocumentCustomer = {
  id: string;
  name?: string;
  path?: string;
  sizeFile?: number;
  type?: string;
};
export type IDocumentCustomer = {
  id: string;
  projectId: string;
  type: string;
  name: string;
  path: string;
  sizeFile: number;
  items: IItemDocumentCustomer[];
  softDelete: boolean;
  createdDate: Date;
  updatedDate: Date;
};

export type TProject = {
  id: string;
  name: string;
};

export type TPropertyUnit = {
  code: string;
  id: string;
  _id?: string;
  name?: string;
};

export type TTransitionInformation = {
  code?: string;
  id?: string;
  key?: string;
  contractStatus?: string;
  project?: TProject;
  propertyUnit?: TPropertyUnit;
};

export type TTransitionHistory = {
  code?: string;
  id?: string;
  key?: string;
  contractStatus?: string;
  project?: TProject;
  propertyUnit?: TPropertyUnit;
  children?: TTransitionHistory[];
  ticketType?: string;
};

export type TTreeTransitionHistory = {
  code?: string;
  id?: string;
  key?: string;
  contractStatus?: string;
  nameProject?: string;
  project?: TProject;
  propertyUnit?: TPropertyUnit;
  children?: TTransitionHistory[];
};

export type TTreeTransitionInformation = {
  id?: string;
  key?: string;
  children?: TTransitionInformation[];
  project?: TProject;
  nameProject?: string;
};
