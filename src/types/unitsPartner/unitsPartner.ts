export interface OtherField {
  type: string;
  key: string;
  value?: string;
  arrayData?: Array<{ arrayValue: string }>;
  objectData?: Array<{ objectKey: string; objectValue: string }>;
}

export interface OrgchartPartner {
  id: string;
  nameVN: string;
  taxcode: string | null;
  code: string;
}

export interface Partner {
  _id: string;
  lineManager: {
    name: string;
    code: string;
    id: string;
  };
  partnerCode: string[];
  other: OtherField[];
  active: boolean;
  softDelete: boolean;
  identityType: string;
  partnershipName: string;
  partnershipLevel: string;
  taxCode: string;
  short: string;
  issueDate: string;
  issueLocation: string;
  partnershipCode: string;
  orgchartPartner: OrgchartPartner[];
  createdBy: string;
  modifiedBy: string;
  id: string;
  bankInfor: any[];
  lastUpdate: string;
  managerIds: string[];
  createdDate: string;
  __v: number;
  businessPartnerCode: string;
  logo: string;
}

export interface ListCooperativeSalesUnits {
  [key: string]: unknown;
  _id?: string;
  bank?: Bank;
  representIssuedDate?: string;
  other?: [];
  active?: boolean;
  softDelete?: boolean;
  partnershipName?: string;
  level?: string;
  taxCode?: string;
  partnershipCode?: string;
  partnershipLevel?: string;
  partnerCode?: PartnerCode;
  logo?: string;
  id?: string;
  lastUpdate?: string;
  managerIds?: Manager[];
  createdDate?: string;
  softDeleteReason?: string;
  lineManager?: string;
}

export interface Manager {
  id?: string;
}

export interface Bank {}

export interface PartnerCode {
  _id?: string;
  status?: number;
  parentCode?: string;
  parentName?: string;
  lineManager?: string;
  managerId?: string;
  taxCode?: string;
  representBy?: string;
  representPhone?: string;
  representEmail?: string;
  representIDValue?: string;
  representIssuedDate?: string;
  representIssuedPlace?: string;
  representAddressObject?: string;
  representContactAddress?: string;
  representTaxNumber?: string;
  contactAddress?: string;
  contactPhone?: string;
  contactFax?: string;
  bank?: Bank;
  bankInfo?: string;
  addressObject?: string;
  phone?: string;
  branchName?: string;
  certNumber?: string;
  source?: string;
  other?: [];
  lastUpdate?: string;
  businessArea?: string;
  costCenter?: string;
  orgLayer?: string;
  level?: number;
  shortName?: string;
  nameEN?: string;
  nameVN?: string;
  code?: string;
  createdBy?: string;
  modifiedBy?: string;
  id?: string;
  createdDate?: string;
  label?: string;
  value?: string;
}
