import { LoadingOutlined } from '@ant-design/icons';
import { App, Form, Typography, Upload, UploadProps } from 'antd';
import { RcFile } from 'antd/es/upload';
import { ColProps, UploadFile } from 'antd/lib';
import { AxiosError } from 'axios';
import { ReactNode, useEffect, useState } from 'react';
import IconUpload from '../../assets/images/IconImageUpload.svg';
import { modalConfirm } from '../modal/specials/ModalConfirm';
import './style.scss';
import { uploadMedia } from '../../service/upload';
import { IResponseUpload } from '../../types/common/common';

const { Text } = Typography;

interface IUpload extends UploadProps {
  fileSize?: number; // file size in MB
  areaImg?: { width: number; height: number };
  fieldName: string | (number | string)[];
  label: string | ReactNode;
  isValidate?: boolean;
  labelCol?: ColProps;
  defaultImage?: string;
  path: string;
  reset?: boolean;
  textType?: string;
  moreTypes?: string[];
  onSelectImage?: (files: IResponseUpload) => void;
  iconUpload?: ReactNode; // Icon to display in the upload button
}

const FormUploadImage = (props: IUpload) => {
  const {
    fileSize,
    areaImg,
    fieldName,
    label,
    isValidate,
    labelCol,
    reset,
    defaultImage,
    path,
    textType = 'Định dạng .jpeg, .jpg, .png',
    moreTypes,
    onSelectImage,
    iconUpload,
    ...restProps
  } = props;

  const form = Form.useFormInstance();
  const [loading, setLoading] = useState(false);
  const [currentImg, setCurrentImg] = useState<string | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { modal } = App.useApp();

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : iconUpload || <img src={IconUpload} alt="Upload Icon" />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  // Xử lý reset hoặc defaultImage
  useEffect(() => {
    if (reset) {
      setCurrentImg(null);
      setFileList([]);
      form.setFieldValue(fieldName, null);
    } else if (defaultImage) {
      const currentLogo = `${import.meta.env.VITE_S3_IMAGE_URL}/${defaultImage}`;
      setCurrentImg(currentLogo);
      form.setFieldValue(fieldName, defaultImage); // Chỉ lưu key mặc định vào form
    }
  }, [reset, defaultImage, fieldName, form]);

  // Xử lý khi thay đổi file
  const handleChangeImage: UploadProps['onChange'] = info => {
    setFileList(info.fileList);
    restProps.onChange?.(info);
  };

  // Xử lý khi xóa file
  const handleRemove = () => {
    setCurrentImg(null);
    setFileList([]);
    form.setFieldValue(fieldName, null);
  };

  // Kiểm tra kích thước ảnh
  const isValidDimensions = (file: RcFile): Promise<boolean> => {
    if (!areaImg) return Promise.resolve(true);
    return new Promise(resolve => {
      const img = new Image();
      img.src = URL.createObjectURL(file);
      img.onload = () => {
        const width = img.width;
        const height = img.height;
        URL.revokeObjectURL(img.src);
        resolve(areaImg && width <= areaImg.width && height <= areaImg.height);
      };
    });
  };

  // Kiểm tra trước khi upload
  const handleBeforeUpload = async (file: RcFile) => {
    const concatTypes = ['image/jpeg', 'image/png', 'image/jpg', ...(moreTypes || [])];
    const isJpgOrPng = concatTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.jfif');
    const isLtSize = fileSize ? file.size / 1024 / 1024 <= fileSize : true; // Kiểm tra kích thước file
    const validDimensions = await isValidDimensions(file);

    if (!isLtSize || !isJpgOrPng || !validDimensions) {
      const formattedTypes = concatTypes
        .map(type => type.split('/')[1])
        .filter((v, i, arr) => arr.indexOf(v) === i)
        .join(', ');
      const reasons = [];
      if (!isJpgOrPng) reasons.push(`File sai định dạng (${formattedTypes})`);
      if (!isLtSize) reasons.push(`Dung lượng vượt quá ${fileSize}MB`);
      if (!validDimensions) reasons.push(`Kích thước ảnh không hợp lệ ${areaImg?.width}x${areaImg?.height}px`);

      modalConfirm({
        modal,
        className: 'modal-confirm-upload-training',
        title: 'Không thể upload file',
        content: reasons.join(', '),
        cancelText: null,
        okText: 'Đóng',
        handleConfirm: () => setLoading(false),
      });
      return false;
    }
    return true;
  };

  return (
    <>
      {/* Form.Item ẩn để lưu uploadedKey */}
      <Form.Item
        name={fieldName}
        noStyle
        rules={
          isValidate
            ? [
                {
                  validator: (_, value) => {
                    if (!value) {
                      return Promise.reject(new Error('Vui lòng tải ảnh lên!'));
                    }
                    return Promise.resolve();
                  },
                },
              ]
            : []
        }
      />

      {/* Form.Item để hiển thị Upload component, không gắn trực tiếp với fieldName */}
      <Form.Item
        labelCol={labelCol}
        label={label}
        required={isValidate}
        validateStatus={form.getFieldError(fieldName).length ? 'error' : ''}
        help={form.getFieldError(fieldName)[0]}
        extra={
          <Text type="secondary" className="description-uploadIMG">
            {textType}
          </Text>
        }
      >
        <Upload
          {...restProps}
          listType="picture-card"
          className={`image-uploader ${currentImg && 'upload-success'}`}
          maxCount={1}
          onRemove={handleRemove}
          fileList={fileList}
          showUploadList={false}
          beforeUpload={handleBeforeUpload}
          name="logo"
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              setLoading(true);
              const res = await uploadMedia(file as RcFile, path);
              const key = res?.data?.data?.key; // Lấy key từ API
              const previewUrl = res?.data?.data.Location; // Lấy URL để hiển thị

              setCurrentImg(previewUrl); // Hiển thị ảnh
              setFileList([
                {
                  uid: '-1',
                  name: (file as RcFile).name || 'unknown',
                  status: 'done',
                  url: previewUrl,
                },
              ]);
              form.setFieldValue(fieldName, key); // Gán key vào form
              onSelectImage?.(res?.data?.data);

              onSuccess?.('ok');
            } catch (error: unknown) {
              onError?.(error as AxiosError);
            } finally {
              setLoading(false);
            }
          }}
          onChange={handleChangeImage}
        >
          {currentImg ? <img src={currentImg} alt="image" /> : uploadButton}
        </Upload>
      </Form.Item>
    </>
  );
};

export default FormUploadImage;
