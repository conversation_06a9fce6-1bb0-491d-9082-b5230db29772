.image-uploader {
  width: fit-content;
  &.upload-success .ant-upload-select {
    border: 1px solid #d9d9d9 !important;
    padding: 8px !important;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }
}
.description-uploadIMG {
  font-size: 12px;
  font-weight: 400;
}
.upload-wrapper {
  width: 100%;

  .ant-upload {
    width: 100%;

    .ant-upload-select {
      width: 100% !important;
    }
  }
}

.ellipsis {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: block !important; /* hoặc inline-block tùy context */
  width: 100%;
}

.ant-upload-list-item-actions {
  .anticon-delete {
    color: #ff4d4f !important;
  }
}
.modal-confirm-upload-training {
  .ant-modal-confirm-btns {
    .ant-btn {
      width: 100% !important;
    }
  }
}
