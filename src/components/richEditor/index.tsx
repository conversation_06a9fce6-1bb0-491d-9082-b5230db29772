/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { MAX_LENGTH } from '../../constants/common';
import './styles.scss';
interface RichEditorProps {
  onChange: (value: string) => void;
  value: string;
  ref: React.RefObject<ReactQuill>;
  placeholder?: string;
  maxLength?: number;
}

const FontAttributor = Quill.import('attributors/class/font');
FontAttributor.whitelist = [
  'arial',
  'inconsolata',
  'montserrat',
  'monospace',
  'open-sans',
  'roboto',
  'sans-serif',
  'serif',
  'times-new-roman',
  'ubuntu',
];
Quill.register(FontAttributor, true);

Quill.register('modules/toolbar', Quill.import('modules/toolbar'));

const formats = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'align',
  'strike',
  'script',
  'blockquote',
  'background',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
  'color',
  'code-block',
];

const toolbarOptions = [
  [{ font: FontAttributor.whitelist }],
  [{ header: ['1', '2', '3', '4', '5', false] }],
  [{ align: [] }],
  ['bold', 'italic', 'underline', 'strike'],
  [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
  [{ color: [] }, { background: [] }],
  [{ script: 'sub' }, { script: 'super' }],
  [{ size: ['small', false, 'large', 'huge'] }],
  ['blockquote', 'code-block'],
  ['link', 'image', 'video', 'formula'],
  [{ direction: 'rtl' }],
  ['clean'],
];

const RichEditor = React.forwardRef<ReactQuill, RichEditorProps>(
  ({ value, onChange, placeholder = 'Nhập nội dung...', maxLength = MAX_LENGTH }, ref) => {
    const editorRef = React.useRef<ReactQuill>(null);

    React.useImperativeHandle(ref, () => editorRef.current as ReactQuill);

    React.useEffect(() => {
      const editor = editorRef.current?.getEditor();
      if (!editor) return;

      editor.on('text-change', () => {
        const plainText = editor.getText() || '';
        if (plainText.trim().length > maxLength) {
          editor.deleteText(maxLength, plainText.length);
        }
      });
    }, [maxLength]);

    const handleQuillChange = (value: string) => {
      const editor = editorRef.current?.getEditor();
      const plainText = editor?.getText() || '';

      if (plainText.trim().length <= maxLength) {
        onChange?.(value);
      } else {
        // Prevent inserting more text
        const delta = editor?.getContents();
        if (delta && editor) {
          editor.setContents(delta.slice(0, maxLength));
        }
      }
    };

    return (
      <ReactQuill
        theme="snow"
        modules={{
          toolbar: {
            container: toolbarOptions,
          },
          history: {
            delay: 500,
            maxStack: 100,
            userOnly: true,
          },
        }}
        formats={formats}
        placeholder={placeholder}
        onChange={handleQuillChange}
        value={value}
        ref={editorRef}
      />
    );
  },
);
export default RichEditor;
