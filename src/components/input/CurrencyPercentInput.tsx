import { Input, Select } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { formatCurrency, formatPercent } from '../../utilities/shareFunc';

type InputType = '%' | 'VNĐ' | string;

interface CurrencyPercentInputProps {
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: (value: string) => void;
  onTypeChange?: (type: InputType, value: string) => void;
  type?: string;
  maxLengthInt?: number; // Chỉ áp dụng cho percent
  maxLength?: number; // Chỉ áp dụng cho currency
  placeholder?: string;
  defaultInputType?: InputType;
  maxPercent?: number;
  maxCurrency?: number;
  disabled?: boolean;
  allowTypeSwitch?: boolean;
  externalInputType?: InputType; // Để sử dụng bên ngoài nếu cần
  style?: React.CSSProperties;
  [key: string]: unknown;
}

const CurrencyPercentInput = (props: CurrencyPercentInputProps) => {
  const {
    value,
    onChange,
    onBlur,
    onTypeChange,
    type = 'text',
    placeholder,
    maxLengthInt = 3, // Chỉ áp dụng cho percent
    maxPercent, // Cho percent
    defaultInputType = 'VNĐ',
    maxCurrency, // Cho currency
    maxLength, // Cho currency
    disabled = false,
    allowTypeSwitch = true,
    style,
    externalInputType,
    ...restProps
  } = props;

  const [internalValue, setInternalValue] = useState(value || '');
  const [inputType, setInputType] = useState<InputType>(defaultInputType);

  // Định dạng currency value on blur
  const formatCurrencyValue = useCallback((value: string): string => {
    if (!value || value === '') return '0';

    const cleanValue = unFormatCurrency(value);
    const numericValue = parseFloat(cleanValue);

    if (isNaN(numericValue)) return '0';

    return formatCurrency(numericValue.toFixed(0));
  }, []);

  // Đặt inputType từ externalInputType nếu có
  useEffect(() => {
    if (externalInputType !== undefined && externalInputType !== inputType) {
      setInputType(externalInputType);
    }
  }, [externalInputType, inputType]);

  // Đặt giá trị ban đầu cho internalValue khi inputType hoặc value thay đổi
  useEffect(() => {
    if (inputType === 'VNĐ') {
      setInternalValue(formatCurrency(value || ''));
    } else {
      setInternalValue(value ? value : '');
    }
  }, [inputType, maxLengthInt, value]);

  // Xóa định dạng cho phép tính
  const unFormatCurrency = (num: string): string => {
    return num.toString().replace(/,/g, '');
  };

  // Xác thực và định dạng input currency
  const handleCurrencyChange = (inputValue: string) => {
    if (inputValue === '') {
      setInternalValue('');
      onChange && onChange('');
      return;
    }

    const cleanValue = unFormatCurrency(inputValue);
    const regex = /^\d*\.?\d*$/;

    if (regex.test(cleanValue)) {
      let trimmedValue = cleanValue;
      if (typeof maxLength === 'number') {
        trimmedValue = trimmedValue.slice(0, maxLength);
      }

      // 2. Giới hạn theo giá trị số
      let numericValue = parseFloat(trimmedValue);
      if (typeof maxCurrency === 'number' && numericValue > maxCurrency) {
        numericValue = maxCurrency;
      }

      const formatted = formatCurrency(numericValue.toFixed(0));
      setInternalValue(formatted);
      onChange && onChange(numericValue.toString());
    }
  };

  // Xác thực phần trăm đầu vào (cùng logic với PercentInput )
  const handlePercentChange = (inputValue: string) => {
    if (inputValue === '') {
      setInternalValue('');
      onChange && onChange('');
      return;
    }

    const regex = new RegExp(`^(\\d{0,${maxLengthInt}})(\\.\\d{0,2})?$`);
    if (regex.test(inputValue)) {
      const numericValue = parseFloat(inputValue);
      const percentMax = maxPercent;

      if (percentMax && numericValue > percentMax) {
        setInternalValue(percentMax.toString());
        onChange && onChange(percentMax.toString());
      } else {
        setInternalValue(inputValue);
        onChange && onChange(inputValue);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    if (inputType === 'VNĐ') {
      handleCurrencyChange(inputValue);
    } else {
      handlePercentChange(inputValue);
    }
  };

  const handleBlur = () => {
    let formattedValue: string;

    if (inputType === 'VNĐ') {
      formattedValue = formatCurrencyValue(internalValue);
      setInternalValue(formattedValue);
      onChange && onChange(unFormatCurrency(formattedValue));
    } else {
      formattedValue = formatPercent(internalValue);
      setInternalValue(formattedValue);
      onChange && onChange(formattedValue);
    }

    onBlur && onBlur(formattedValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleBlur();
      e.preventDefault();
    }
  };

  const handleTypeChange = (newType: InputType) => {
    if (newType === inputType) return;

    setInputType(newType);

    const currentValue = inputType === 'VNĐ' ? unFormatCurrency(internalValue) : internalValue;

    if (currentValue && currentValue !== '0' && currentValue !== '0.00') {
      if (newType === 'VNĐ') {
        const formatted = formatCurrencyValue(currentValue);
        setInternalValue(formatted);
        onChange && onChange(unFormatCurrency(formatted));
        onTypeChange && onTypeChange(newType, unFormatCurrency(formatted));
      } else {
        const percentValue = formatPercent(currentValue.slice(0, maxLengthInt));
        setInternalValue(percentValue);
        onChange && onChange(percentValue);
        onTypeChange && onTypeChange(newType, percentValue);
      }
    } else {
      setInternalValue('');
      onChange && onChange('');
      onTypeChange && onTypeChange(newType, '');
    }
  };

  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    return inputType === 'VNĐ' ? 'Nhập số tiền' : 'Nhập phần trăm';
  };

  const getAddonAfter = () => {
    if (!allowTypeSwitch) {
      return <div style={{ minWidth: 48 }}>{inputType === 'VNĐ' ? 'VNĐ' : '%'}</div>;
    }

    return (
      <Select
        value={inputType}
        onChange={handleTypeChange}
        style={{ width: 70 }}
        disabled={disabled}
        size="small"
        options={[
          { value: 'VNĐ', label: 'VNĐ' },
          { value: '%', label: '%' },
        ]}
      />
    );
  };

  return (
    <Input
      {...restProps}
      type={type}
      value={internalValue}
      onChange={handleChange}
      onBlur={handleBlur}
      onKeyDown={handleKeyPress}
      placeholder={getPlaceholder()}
      addonAfter={getAddonAfter()}
      disabled={disabled}
      style={style}
    />
  );
};

export default CurrencyPercentInput;
export type { InputType, CurrencyPercentInputProps };
