import { MenuOutlined } from '@ant-design/icons';
import { Flex, Layout } from 'antd';
import Cookies from 'js-cookie';
import React, { useCallback, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Logo from '../components/logo/Logo';
import { usePage } from '../context/PageContext';
import { useFetch } from '../hooks';
import { getInfoAccount } from '../service/account';
import { UserProfile } from '../types/infoUser';
import HeaderLayout from './header/HeaderLayout';
import MenuCustom from './menu/MenuCustom';
import './PrivateLayout.scss';
import { useAuth } from '../context/AuthContext';

const { Content, Sider } = Layout;

interface Props {
  children: React.JSX.Element | React.JSX.Element[];
}

const PrivateLayout: React.FC<Props> = ({ children }) => {
  const { setAuthor } = useAuth();
  const { isOpenSideBar, setOpenSideBar } = usePage();
  const navigate = useNavigate();
  const token: string = Cookies.get('access_token') || '';
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  const { data } = useFetch<UserProfile>({
    queryKeyArr: ['info-account'],
    cacheTime: 100,
    api: getInfoAccount,
  });

  const dataAccount = data?.data?.data;

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setOpenSideBar(false);
      }
    },
    [setOpenSideBar],
  );
  useEffect(() => {
    if (!token) {
      navigate('/');
    }
    if (dataAccount) {
      sessionStorage.setItem('dataAccount', JSON.stringify(dataAccount));
    }
  }, [dataAccount, navigate, token]);

  const prevFeatures = useRef<unknown>(null);

  useEffect(() => {
    const features =
      dataAccount?.roles?.reduce((acc: { featureName: string }[], role) => {
        role.functionRoles?.forEach(functionRole => {
          if (functionRole.features) {
            functionRole.features.forEach(feature => {
              if (!acc.some(f => f.featureName === feature.featureName)) {
                acc.push(feature);
              }
            });
          }
        });
        return acc;
      }, []) || [];
    if (features && features !== prevFeatures.current) {
      prevFeatures.current = features;
      setAuthor(features);
    }
  }, [dataAccount?.roles, setAuthor]);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);
  return (
    <Layout hasSider style={{ minHeight: '100vh', position: 'relative', overflow: 'hidden' }}>
      <Sider
        ref={sidebarRef}
        className={`container-sider ${isOpenSideBar ? 'expanded' : 'collapsed'}`}
        trigger={null}
        collapsible
        collapsed={false}
        width={315}
      >
        <Flex className={'header-sider'} justify="flex-start" align="center">
          <MenuOutlined
            onClick={() => {
              setOpenSideBar(!isOpenSideBar);
            }}
            className="menuOutlined"
            size={24}
          />
          <Logo onClick={() => navigate('/')} />
        </Flex>
        <MenuCustom />
      </Sider>
      <Layout>
        <HeaderLayout dataAccount={dataAccount} />
        <Content className="content">
          <>{children}</>
        </Content>
      </Layout>
    </Layout>
  );
};

export default PrivateLayout;
