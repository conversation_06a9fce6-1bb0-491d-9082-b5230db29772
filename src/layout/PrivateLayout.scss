.container-sider {
  border-right: 1px solid var(--Table-colorSplit, rgba(0, 0, 0, 0.06));
  background: transparent !important;
  position: absolute !important;
  z-index: 1000;
  transition:
    width 0.3s ease,
    transform 0.3s ease !important;
  &.collapsed {
    width: 80px;
    transform: translateX(-100%);
  }

  &.expanded {
    transform: translateX(0);
  }

  .header-sider {
    height: 40px;
    border-bottom: 1px solid var(--Table-colorSplit, rgba(0, 0, 0, 0.06));
    background: var(--colorFillAlter, rgba(0, 0, 0, 0.02));
    padding: 0px 24px;

    .menuOutlined {
      margin-right: 12px;
      cursor: pointer;
    }
  }

  .header-sider-search {
    min-height: 64px;
    border-bottom: 1px solid var(--Table-colorSplit, rgba(0, 0, 0, 0.06));
    padding: 0 24px;
  }

  .ant-layout-sider-children {
    background: white;

    .container-menu {
      height: calc(100vh - 104px);
      overflow: auto;
    }
  }
}

.content {
  position: relative;
  padding: 32px 120px 64px;
  overflow: auto;
  min-width: 100%;
  max-height: calc(100vh - 40px);
}

.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

@media screen and (max-width: 1200px) {
  .content {
    padding: 24px 60px;
  }
}
@media screen and (max-width: 900px) {
  .content {
    padding: 16px 24px;
  }
}
